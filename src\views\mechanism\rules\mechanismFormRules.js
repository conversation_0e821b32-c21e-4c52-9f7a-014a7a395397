import {validSocialCreditCode, validIdCard, validPhone, validEmail, validHttps} from '@/utils/formValidationRules'

export const rules = {
  companyLicense: [
    {
      required: true,
      message: '请上传营业执照',
      trigger: ['change', 'blur']
    }
  ],
  companyOfficeVideo: [
    {
      required: true,
      message: '请上传办公场地视频',
      trigger: 'change'
    }
  ],
  companyLogo: [
    {
      required: true,
      message: '请上前台logo',
      trigger: 'change'
    }
  ],
  companyCreditCode: [
    {
      required: true,
      validator: validSocialCreditCode,
      trigger: 'blur'
    }
  ],
  agencyName: [
    {
      required: true,
      message: '请输入机构名称',
      trigger: 'blur'
    }
  ],
  agencyType: [
    {
      required: true,
      message: '请选择机构类型',
      trigger: 'change'
    }
  ],
  companyAddress: [
    {
      required: true,
      message: '请选择省市区',
      trigger: 'change'
    }
  ],
  companyAddressDetail: [
    {
      required: true,
      message: '请输入详细地址',
      trigger: 'blur'
    }
  ],
  legalCardFront: [
    {
      required: true,
      message: '请上传法人身份证人像面',
      trigger: 'change'
    }
  ],
  legalCardBack: [
    {
      required: true,
      message: '请上传法人身份证国徽面',
      trigger: 'change'
    }
  ],
  legalName: [
    {
      required: true,
      message: '请输入法人姓名',
      trigger: 'blur'
    }
  ],
  legalCardNumber: [
    {
      required: true,
      validator: validIdCard,
      trigger: 'blur'
    }
  ],
  legalMobile: [
    {
      required: true,
      validator: validPhone,
      trigger: 'blur'
    }
  ],
  legalIsContact: [
    {
      required: true,
      message: '请选择法人是否为联系人',
      trigger: 'change'
    }
  ],
  contactName: [
    {
      required: true,
      message: '请输入联系人姓名',
      trigger: ['blur', 'change']
    }
  ],
  contactMobile: [
    {
      required: true,
      validator: validPhone,
      trigger: ['blur', 'change']
    }
  ],
  contactEmail: [
    {
      required: true,
      validator: validEmail,
      trigger: 'blur'
    }
  ],
  cooperateValidity: [
    {
      required: true,
      message: '请选择合作有效期',
      trigger: 'change'
    }
  ],
  cooperateIsApi: [
    {
      required: true,
      message: '请选择是否api机构',
      trigger: 'change'
    }
  ],
  cooperateIsMatch: [
    {
      required: true,
      message: '请选择是否撞库',
      trigger: 'change'
    }
  ],
  notifyUrl: [
    {
      required: false,
      validator: (rule, value, callback) => {
        const reg = /^(?:https?:\/\/[\w.-]+(?:\.[\w.-]+)+[/#?]?.*)?$/
        if (!reg.test(value)) {
          callback(new Error('请输入正确的地址'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
