<!--新增|编辑报价-->
<template>
  <FormDialog
    ref="dialogFormRef"
    v-model="dialogVisible"
    :title="formId ? '编辑报价' : '新增报价'"
    width="35%"
    :form-model="quoteForm"
    label-width="130px"
    :rules="rules"
    @submit="handleSubmit"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form-item label="产品类型" prop="productId">
      <el-select v-model="quoteForm.productId" filterable placeholder="请选择产品类型" style="width: 100%" @change="handleProductTypeChange">
        <el-option
          v-for="item in productTypeOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
          :disabled="item.disabled"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="产品描述">
      {{ quoteForm.description }}
    </el-form-item>
    <el-form-item label="产品报价" prop="amount">
      <el-input-number v-model="quoteForm.amount" :precision="2" :step="0.1" :min="0" style="width: 100%" />
    </el-form-item>
    <el-form-item label="城市范围" prop="areas">
      <city-selector
        ref="citySelectorRef"
        v-model="quoteForm.areas"
        multiple
        check-strictly
        :show-area="false"
        :clearable="false"
        show-all
        placeholder="城市"
        filter-municipality
        style="width: 100%"
        @change="handleAreasChange"
      />
    </el-form-item>
    <el-form-item label="接收时间段" prop="acceptTimeDate">
      <el-time-picker
        v-if="dialogVisible"
        v-model="quoteForm.acceptTimeDate"
        is-range
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        placeholder="选择时间范围"
        value-format="HH:mm"
        format="HH:mm"
        clearable
        style="width: 100%"
        @change="handleTimePickerHandle"
      />
    </el-form-item>
    <el-form-item label="接收星期" prop="acceptWorkDayList">
      <el-checkbox-group v-model="quoteForm.acceptWorkDayList">
        <el-checkbox v-for="week in weeks" :key="week.value" :label="week.value">{{ week.label }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="节假日是否接收" prop="holidayAccept">
      <el-select v-model="quoteForm.holidayAccept" style="width: 100%">
        <el-option
          label="是"
          :value="1"
        />
        <el-option
          label="否"
          :value="2"
        />
      </el-select>
    </el-form-item>
  </FormDialog>
</template>

<script>
import CitySelector from '@/components/CitySelector/index.vue'
import { rules } from '@/views/mechanism/rules/quoteFormRules'
import { createQuoteApi, editQuoteApi, getQuoteInfoApi } from '@/api/mechanism'
import moment from 'moment'
import { getProductTypeListApi } from '@/api/product'
import FormDialog from '@/components/FormDialog/index.vue'

const quoteFormState = {
  // 产品类型
  productId: '',
  // 产品描述
  description: '',
  // 产品报价
  amount: 0,
  // 城市范围
  areas: [],
  // 接收时间段
  acceptTimeDate: ['00:00', '23:59'],
  startAcceptTime: '00:00',
  endAcceptTime: '23:59',
  // 接收星期
  acceptWorkDayList: [],
  acceptWorkDay: '',
  // 节假日是否接收
  holidayAccept: ''
}

export default {
  name: 'QuoteForm',
  components: { CitySelector, FormDialog },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    formId: {
      type: Number,
      default: null
    },
    agenciesId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      submitLoading: false,
      quoteId: null,
      weeks: [
        {
          value: '1',
          label: '一'
        },
        {
          value: '2',
          label: '二'
        },
        {
          value: '3',
          label: '三'
        },
        {
          value: '4',
          label: '四'
        },
        {
          value: '5',
          label: '五'
        },
        {
          value: '6',
          label: '六'
        },
        {
          value: '7',
          label: '日'
        }
      ],
      rules,
      quoteForm: { ...quoteFormState },
      productTypeOptions: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    // 获取产品类型
    async getProductTypeList() {
      try {
        const { data } = await getProductTypeListApi()

        this.productTypeOptions = data.map(item => {
          return {
            id: item.id,
            name: item.productName,
            price: item.productPrice,
            productDescribe: item.productDescribe,
            disabled: item.status === 0
          }
        })
      } catch (err) {
        console.log(err)
      }
    },
    handleOpen() {
      this.getProductTypeList()
      this.formId && this.$nextTick(() => {
        this.$refs.dialogFormRef.loadFormData(() => {
          return getQuoteInfoApi(this.formId)
        }, (data) => {
          // 处理时间段
          const timeList = [moment(data.startAcceptTime, 'HH:mm').format('HH:mm'), moment(data.endAcceptTime, 'HH:mm').format('HH:mm')]
          // 处理选择星期
          const acceptWorkDayList = data.acceptWorkDay.split(',')
          // 处理地区禁用
          this.$refs.citySelectorRef.setDisabled(data.areaIds || [])

          return {
            ...data,
            acceptTimeDate: timeList,
            acceptWorkDayList,
            areas: data.areaIds
          }
        })
      })
      // 新增时处理地区禁用
      this.$nextTick(() => {
        !this.formId && this.$refs.citySelectorRef.setDisabled([])
      })
    },
    handleTimePickerHandle(e) {
      if (e) {
        this.quoteForm.startAcceptTime = e[0]
        this.quoteForm.endAcceptTime = e[1]
      } else {
        this.quoteForm.startAcceptTime = ''
        this.quoteForm.endAcceptTime = ''
      }
    },
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editQuoteApi({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await createQuoteApi(params)
      }
    },
    handleAreasChange(e) {
      this.$refs.citySelectorRef.setDisabled(e)
    },
    handleProductTypeChange(e) {
      const curItem = this.productTypeOptions.find(item => item.id === e)

      this.quoteForm.amount = curItem.price
      this.quoteForm.description = curItem.productDescribe
    },
    handleClose() {
      this.quoteForm = { ...quoteFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    handleSubmit(formParams, done) {
      this.$confirm('是否确认提交？', '提示', {
        closeOnClickModal: false
      }).then(async() => {
        console.log(formParams, 'formParams')
        try {
          const params = { ...formParams, agenciesId: this.agenciesId }
          // 删除时间段临时字段
          delete params.acceptTimeDate
          // 处理接收星期
          params.acceptWorkDay = this.quoteForm.acceptWorkDayList.join(',')
          delete params.acceptWorkDayList
          const { code } = await this.useHttpInterface(params)
          if (code !== 200) return false

          this.$message.success('提交成功')
          this.handleClose()
        } catch (err) {
          console.log(err)
        } finally {
          done()
        }
      }).catch(() => {
        done()
      })
    }
  }
}
</script>
