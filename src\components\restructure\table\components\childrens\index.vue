<template>
  <el-table-column
    v-if="column"
    :key="column.key || column.title"
    align="center"
    :label="column.title"
    :show-overflow-tooltip="
      basics.isNull(column.tooltip)
        ? true
        : column.tooltip
    "
    :render-header="
      basics.isNull(column.renderHeader)
        ? null
        : column.renderHeader
    "
    :width="column.width"
    :fixed="column.fixed ? column.fixed : false"
    :sortable="
      column.sortable ? column.sortable : false
    "
    :prop="column.key || null"
  >
    <template slot-scope="scope">
      <template v-if="!column.render">
        <span>{{ getLabelText(scope.row, column.key) }}</span>
      </template>
      <Template
        v-else
        :render="column.render"
        :results="{ data: scope, column: column }"
      />
    </template>

    <template v-if="column.children">
      <Childrens
        v-for="item in column.children"
        :key="item.key"
        :column="item"
      />
    </template>

  </el-table-column>
</template>

<script>
import { mapRecursion } from '@/config/basicsMethods'
export default {
  name: 'Childrens',
  props: {
    column: {
      type: [Object, Array],
      default: () => {}
    }
  },
  data() {
    return {
      getLabelText: mapRecursion
    }
  }
}
</script>
