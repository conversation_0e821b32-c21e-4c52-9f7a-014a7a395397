<template>
  <el-form
    ref="ruleForm"
    :model="ruleForm"
    :rules="rules"
    :show-close="false"
    label-width="100px"
    class="demo-ruleForm"
  >
    <el-form-item label="名称" prop="username" :rules="rules.nullStr">
      <el-input v-model="ruleForm.username" placeholder="请输入用户名" style="width:320px;" maxlength="25" />
    </el-form-item>
    <el-form-item label="手机号" prop="mobileNo" class="form-item" :rules="rules.common">
      <el-input
        v-model="ruleForm.mobileNo"
        oninput="value=value.replace(/^\D*([0-9]\d*)?.*$/,'$1')"
        maxlength="11"
        placeholder="请输入手机号"
        style="width:320px;"
        @blur="ruleForm.mobileNo = $event.target.value"
      />
    </el-form-item>
    <el-form-item label="角色" prop="roleId" :rules="rules.common">
      <el-select v-model="ruleForm.roleId" style="width:320px;" clearable placeholder="请选择管理角色">
        <el-option
          v-for="item in rolesList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
          :disabled="item.status == 1"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="所属部门" prop="branchId" :rules="rules.common">
      <el-cascader ref="departmentRef" v-model="ruleForm.branchId" style="width:320px;" :show-all-levels="false" :options="departmentList" :props="{value:'id',label:'branchName',children:'childrenBranch',checkStrictly: true}" @change="getManager()" />
    </el-form-item>
    <el-form-item label="直属上级" prop="directSuperior">
      <el-select v-model="ruleForm.directSuperior" style="width:320px;" clearable placeholder="请选择">
        <el-option
          v-for="item in allUserData"
          :key="item.id"
          :label="item.username"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否启用：" prop="enable">
      <el-radio v-model="ruleForm.enable" :label="0">启用</el-radio>
      <el-radio v-model="ruleForm.enable" :label="1">禁用</el-radio>
    </el-form-item>
    <el-form-item class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button
        type="primary"
        :loading="btnDisabled"
        :disabled="btnDisabled"
        @click="roleUpdated('ruleForm')"
      >确 定</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { get_branch_directSuperior } from '@/api/system'
import { getDepartmentList } from '@/views/system/page/useUsers'
import utils from '@/libs/utils'
const isEmpty = utils.isEmpty
import { commonNullCharacterReg } from '@/libs/validate'
export default {
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    btnDisabled: {
      type: Boolean,
      default: false
    },
    isType: {
      type: String,
      default: 'add'
    },
    rolesList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      ruleForm: {
        mobileNo: '',
        username: '',
        roleId: '',
        enable: 0,
        directSuperior: '',
        branchId: ''
      },
      rules: {
        common: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          { required: true, message: '此项不能为空', trigger: 'change' }
        ],
        nullStr: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          { required: true, message: '此项不能为空', trigger: 'change' },
          { validator: commonNullCharacterReg('此项不能为空'), trigger: 'blur' }
        ]
      },
      allUserData: [],
      departmentList: []
    }
  },
  created() {
    this.getDepartmentInfo()
    this.ruleForm = { ...this.ruleForm, ...this.formData }
    !isEmpty(this.ruleForm.locked) && (this.ruleForm.enable = this.ruleForm.locked)
    !isEmpty(this.ruleForm.branchId) && this.getManager(this.ruleForm.branchId)
  },
  methods: {
    handleDirectSuperior() {
      const isActive = this.allUserData.some(item => item.id == this.ruleForm.directSuperior)
      !isActive && (this.ruleForm.directSuperior = '')
    },
    // 获取部门信息
    async getDepartmentInfo() {
      this.departmentList = await getDepartmentList()
    },
    getManager(id) {
      isEmpty(id) && (id = this.ruleForm.branchId?.[this.ruleForm.branchId.length - 1])
      if (!isEmpty(id)) {
        get_branch_directSuperior({ branchId: id }).then(res => {
          if (res.code == 200) {
            // 筛选不能选自己当上级领导
            const data = res?.data?.filter(item => item.id != this.ruleForm.id) ?? []
            this.allUserData = data || []
            this.handleDirectSuperior()
          }
        })
      } else {
        this.allUserData = []
        this.handleDirectSuperior()
      }
    },
    roleUpdated(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let branchId = this.ruleForm.branchId
          let branchIdVal
          if (Array.isArray(branchId)) { branchIdVal = [...(this.ruleForm.branchId || [])]; branchId = branchIdVal?.pop() }
          this.$emit('submit', { ...this.ruleForm, adminBranch: branchId || '' })
        }
      })
    }

  }
}

</script>

