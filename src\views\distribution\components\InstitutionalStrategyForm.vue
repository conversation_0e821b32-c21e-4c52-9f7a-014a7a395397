<template>
  <div>
    <FormDialog
      ref="dialogFormRef"
      v-model="dialogVisible"
      :title="formId ? '编辑策略' : '新增策略'"
      :form-model="strategyForm"
      width="40%"
      :rules="rules"
      @submit="handleSubmit"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-form-item label="数据来源" prop="clueType">
        <el-select v-model="strategyForm.clueType" clearable :disabled="Boolean(formId)" filterable placeholder="请选择数据来源" style="width: 100%">
          <el-option
            v-for="item in OverallStrategyClueSourceList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="策略名称" prop="strategyName">
        <el-input v-model="strategyForm.strategyName" clearable :maxlength="30" placeholder="请输入策略名称" />
      </el-form-item>
      <el-form-item label="策略描述" prop="description">
        <el-input v-model="strategyForm.description" type="textarea" resize="none" clearable :maxlength="200" placeholder="请输入策略描述" />
      </el-form-item>
      <el-form-item label="分发模式" prop="distributeMode">
        <el-select v-model="strategyForm.distributeMode" clearable filterable placeholder="请选择分发模式" style="width: 100%">
          <el-option
            v-for="item in DistributionModeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="策略排序" prop="strategySorts">
        <el-table
          :data="strategyForm.strategySorts"
          border
          size="mini"
          style="flex: 1"
        >
          <el-table-column
            prop="type"
            label="策略点"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ StrategyPointTypeMap[row.type] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="details"
            label="策略明细"
            align="center"
          />
          <el-table-column
            prop="address"
            label="排序"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-input-number v-model="row.sortNum" size="small" :precision="0" :min="1" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </FormDialog>
    <StrategyPattern v-model="strategyPatternModel" :type="strategyPatternType" :form-value="strategyPatternValue" @close="strategyPatternClose" @selectVal="handleSelectVal" />
  </div>
</template>

<script>
import FormDialog from '@/components/FormDialog/index.vue'
import {
  BiddingModeTypeMap,
  DistributionModeList,
  OverallStrategyClueSourceList,
  ProductModeTypeMap,
  StrategyPointTypeMap,
  OnlineModeMap
} from '../enum'
import rules from '../rules/institutionalStrategyFormRules'
import StrategyPattern from './StrategyPattern.vue'
import {
  addMechanismStrategyApi,
  editMechanismStrategyApi,
  getMechanismStrategyDetailApi,
  getMechanismStrategySortApi
} from '@/api/distribution'

const strategyFormState = {
  clueType: null,
  strategyName: '',
  description: '',
  distributeMode: null,
  strategySorts: []
}

export default {
  name: 'StrategyForm',
  components: {
    FormDialog,
    StrategyPattern
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    formId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      strategyPatternValue: null,
      strategyPatternType: null,
      strategyPatternModel: false,
      strategyForm: { ...strategyFormState }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    OverallStrategyClueSourceList() {
      return OverallStrategyClueSourceList
    },
    DistributionModeList() {
      return DistributionModeList
    },
    rules() {
      return rules
    },
    StrategyPointTypeMap() {
      return StrategyPointTypeMap
    }
  },
  methods: {
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editMechanismStrategyApi({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await addMechanismStrategyApi(params)
      }
    },
    handleSubmit(params, done) {
      this.$confirm('是否确认提交？', '操作提示', {
        closeOnClickModal: false
      }).then(async() => {
        try {
          const { code } = await this.useHttpInterface(params)
          if (code !== 200) return false
          this.$message.success('操作成功')
          this.handleClose()
        } finally {
          done()
        }
      }).catch(() => {
        done()
      })
    },
    handleOpen() {
      this.$nextTick(() => {
        this.$refs.dialogFormRef.loadFormData(() => {
          if (this.formId) {
            return getMechanismStrategyDetailApi(this.formId)
          }
          return getMechanismStrategySortApi()
        })
      })
    },
    handleClose() {
      this.strategyForm = { ...strategyFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    handleEdit(row) {
      this.strategyPatternType = row.type
      if (row.type === 1) {
        this.strategyPatternValue = row.productMode
      } else if (row.type === 2) {
        this.strategyPatternValue = row.priceMode
      } else {
        this.strategyPatternValue = row.onlineMode
      }
      this.strategyPatternModel = true
    },
    strategyPatternClose() {
      this.strategyPatternValue = null
      this.strategyPatternType = null
    },
    handleSelectVal(val, type) {
      // 设置对应的策略点
      this.strategyForm.strategySorts.forEach(item => {
        if (item.type === type) {
          // 判断产品模式|竞价模式
          if (type === 1) {
            item.productMode = val
            // 修改策略描述
            item.details = ProductModeTypeMap[val]
          } else if (type === 2) {
            item.priceMode = val
            // 修改策略描述
            item.details = BiddingModeTypeMap[val]
          } else {
            item.onlineMode = val
            // 修改策略描述
            item.details = OnlineModeMap[val]
            console.log(val, type)
          }
        }
      })
    }
  }
}
</script>
