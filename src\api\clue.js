// 分发管理接口
import { get } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

// 获取分发记录分页列表
export const getDistributeRecordListApi = obj => {
  return get('/distribute-record/page', obj)
}
// 分发记录导出
export const exportDistributeRecordApi = obj => `${CONSTANT.publicPath}/distribute-record/export?${qs.stringify(obj)}`
// 获取撞库记录
export const getCollisionRecordListApi = obj => {
  return get('/distribute-match-record/page', obj)
}
// 撞库记录导出
export const exportCollisionRecordApi = obj => `${CONSTANT.publicPath}/distribute-match-record/export?${qs.stringify(obj)}`
