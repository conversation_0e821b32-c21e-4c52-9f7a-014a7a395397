@import 'element-ui/packages/theme-chalk/src/mixins/mixins';
@import 'element-ui/packages/theme-chalk/src/common/var';

@include b(empty) {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  box-sizing: border-box;
  padding: 40px 0;

  @include e(image) {
    width: 160px;

    img {
      user-select: none;
      width: 100%;
      height: 100%;
      vertical-align: top;
      object-fit: contain;
    }

    svg {
      fill: rgb(220, 221, 224);
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }

  @include e(description) {
    margin-top: 20px;

    p {
      margin: 0;
      font-size: 14px;
      color: #5e6d82;
    }
  }

  @include e(bottom) {
    margin-top: 20px;
  }
}
