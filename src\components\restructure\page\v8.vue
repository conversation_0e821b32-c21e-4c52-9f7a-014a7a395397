/* 解决v5不能展示默认字段的问题 */
<script>
import checkboxDialog from './checkboxDialog.vue'
import Page from './index.vue'
function normalizeProps(vm, keys) {
  const { list, ...otherProps } = vm.props
  const listShow = filterTableHidden(list, keys)
  return {
    on: vm.listeners,
    props: { ...otherProps, list: listShow },
    scopedSlots: vm.scopedSlots
  }
}

// isAll 是否是获取全部list
function getSetting(vm, all = false, pathPrefix = '@setting', isExpanded = false, isAll) {
  if (!vm.props?.list) {
    return
  }

  const path = pathPrefix + vm.parent?.$route?.path
  const settingStr = localStorage.getItem(path)

  if (isExpanded) {
    return settingStr ? JSON.parse(settingStr) : []
  }

  if (all && settingStr && !isAll) {
    // v5基础上的修改处
    let settingStrArray = JSON.parse(settingStr)
    const notShowKeys = handleGetNotShowFilterKey(vm.props.list)
    if (notShowKeys && notShowKeys.length > 0) {
      settingStrArray = notShowKeys.reduce((pre, cur) => {
        if (!settingStrArray.includes(cur)) {
          pre.push(cur)
        }
        return pre
      }, settingStrArray)
    }
    return settingStrArray
  }
  return filterListData(vm.props.list, !all)
}

function setSetting(vm, setting, pathPrefix = '@setting') {
  if (!vm.props?.list) {
    return
  }
  const path = pathPrefix + vm.parent?.$route?.path
  if ((pathPrefix === '@setting' && setting.length === 0) || !Array.isArray(setting)) {
    return new Error('setting is empty')
  }
  localStorage.setItem(path, JSON.stringify(setting))
}

function resetSetting(vm, pathPrefix = '@setting') {
  const path = pathPrefix + vm.parent?.$route?.path
  localStorage.removeItem(path)
}
/** 获取随选项选择展示的key isNotShowFilterKey:true*/
function handleGetNotShowFilterKey(data, type = false) {
  if (!Array.isArray(data)) {
    return []
  }
  return data.reduce((pre, cur) => {
    if (!cur.tableHidden && cur.isNotShowFilterKey) {
      pre.push(cur.key)
    }
    if (!cur.tableHidden && cur.isNotShowFilterKey) {
      pre.push(...handleGetNotShowFilterKey(cur.children))
    }
    return pre
  }, [])
}
/**
 * 过滤显示的key
 * @param {array} data
 * @param {boolean} all
 */
function filterListData(data, all) {
  if (!Array.isArray(data)) {
    return []
  }
  return data.reduce((pre, cur) => {
    const showKey = all ? true : cur.defaultShowKey ?? true // 是否依赖defaultShowKey字段过滤
    if (!cur.tableHidden && showKey && cur.key) {
      pre.push(cur.key)
    }
    if (!cur.tableHidden && showKey && cur.children) {
      pre.push(...filterListData(cur.children, all))
    }
    return pre
  }, [])
}

/**
 * 树形数组，过滤
 */
function filterTreeData(data, setting) {
  return data.reduce((pre, cur) => {
    if (cur.children) {
      cur.children = filterTreeData(cur.children, setting)
    }
    // isNotShowFilterKey：true,不在筛选项展示 2024.1.3某个查询条件有值，表格新增该查询列
    if (setting.includes(cur.key) && !cur.isNotShowFilterKey) {
      return pre.concat([cur])
    }
    return pre
  }, [])
}
/**
 * 树形数组，根据过滤数组，对应数据添加tableHidden属性
 */
function filterTableHidden(data, setting) {
  return data.reduce((pre, cur) => {
    const { children = [], ...i } = cur
    if (children.length === 0 && !setting.includes(i.key) && !i.tableHidden && !i.search) {
      return pre
    }
    if (children.length > 0 && !setting.includes(i.key) && !i.tableHidden) {
      const child = filterTableHidden(children, setting)
      if (child.length > 0) {
        i.children = child
        return pre.concat([i])
      }
      return pre
    }
    return pre.concat([
      {
        ...i,
        children: children.length > 0 ? filterTableHidden(children, setting) : undefined,
        tableHidden: !setting.includes(i.key)
      }
    ])
  }, [])
}
let checkboxShow = false

export default {
  name: 'PageNext',
  functional: true,
  render(h, ctx) {
    const data = ctx.props.list
    const noDataList = data.map(n => {
      if (!n.isCustom && !n.tableHidden) {
        return n.key
      }
      return ''
    }).filter(n => n != '')
    const defaultCheckedKeys1 = getSetting(ctx, true).filter(n => noDataList.indexOf(n) == -1) // v5基础上的修改处
    let defaultCheckedKeys = Array.from(new Set([...defaultCheckedKeys1, ...noDataList]))
    const allKeys = getSetting(ctx, false, '@setting', false, true) // v5基础上的修改处
    const checkboxList = filterTreeData(JSON.parse(JSON.stringify(data)), allKeys)
    return h(Page, normalizeProps(ctx, defaultCheckedKeys), [
      h(
        'template',
        {
          slot: 'searchLine'
        },
        [
          h(
            'el-button',
            {
              style: 'display: inline-block; margin: 0 0 0 10px;',
              props: {
                size: 'small',
                type: 'primary',
                plain: true
              },
              on: {
                click() {
                  checkboxShow = true
                  setTimeout(() => {
                    ctx.parent.$forceUpdate()
                  }, 0)
                }
              }
            },
            '自定义字段'
          ),
          h(checkboxDialog, {
            props: {
              show: checkboxShow,
              data: checkboxList,
              'default-checked-keys': defaultCheckedKeys,
              'show-expand-button': ['allChecked', 'allNoChecked', 'defaultKeyChecked']
            },
            on: {
              changeShow(val) {
                checkboxShow = val
                setTimeout(() => {
                  ctx.parent.$forceUpdate()
                }, 0)
              },
              change(val, loadingInstance) {
                setSetting(ctx, val)
                defaultCheckedKeys = val
                ctx.parent.$forceUpdate()
                ctx.parent.$store.commit('setTableShouldUpdate')
                loadingInstance.close()
              },
              reset(loadingInstance) {
                resetSetting(ctx)
                ctx.parent.$forceUpdate()
                ctx.parent.$store.commit('setTableShouldUpdate')
                loadingInstance.close()
              }
            }
          })
        ]
      )
    ])
  }
}
</script>
<style lang="scss">
.el-popper--page-next {
  padding: 0px !important;
}

.el-tree-node__content {
  height: 33px;
}

.title-container--next {
  text-align: right;
  margin-bottom: 10px;
}
</style>
