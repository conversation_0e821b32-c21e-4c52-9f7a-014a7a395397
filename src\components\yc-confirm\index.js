import Vue from 'vue';
import confirm from './src/yc-confirm.vue';

let confirmConstructor = Vue.extend(confirm);

let theConfirm = function (text) {
  return new Promise((res, rej) => {
    let confirmDom = new confirmConstructor({
      el: document.createElement('div')
    });
    document.body.appendChild(confirmDom.$el);
    confirmDom.text = text;
    confirmDom.onOk = function () {
      res();
      confirmDom.text.isShow = false;
    };
    confirmDom.close = function () {
      rej();
      confirmDom.text.isShow = false;
    }
  })
};

export default theConfirm;
