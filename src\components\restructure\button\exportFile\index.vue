<template>
  <el-button type="primary" size="small" icon="el-icon-download" @click="handleExport"><slot>导出</slot></el-button>
</template>

<script>
import { getBasParams } from '@/config/basicsMethods'
import qs from 'qs'
import global from '@/config/constant.conf'

export default {
  name: 'Index',
  props: {
    url: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleExport() {
      console.log(global.api + this.url + '?' + qs.stringify(getBasParams(this.data)))
      window.location.href = global.api + this.url + '?' + qs.stringify(getBasParams(this.data))
    }
  }
}
</script>

<style scoped>

</style>
