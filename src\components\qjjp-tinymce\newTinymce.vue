<template>
  <div class="tinymce">
    <!-- :init="init" -->
    <textarea :id="tinymceId" ref="EditorText" v-model="content" />
  </div>
</template>

<script>
import lang from '@/tinymce/langs/zh_CN'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver/theme'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/imagetools'
import 'tinymce/icons/default/icons'
tinymce.addI18n('zh_CN', lang)
import { postJjjp } from '@/libs/axios.package'
import GLOBAL from '@/config/constant.conf'
/**
 * https://juejin.cn/post/7157711019442176031
 * window.tinyMCE.activeEditor.getContent()
 * window.tinyMCE.activeEditor.setContent(this.form.content)
 *
*/
export default {
  name: 'tinymce',
  components: { Editor },
  props: {
    textId: {
      type: String,
      default: ''
    },
    set: {
      type: Object,
      default: () => {}
    },
    menubarIsShow: {
      type: Boolean,
      type: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    const ide = Date.now()
    return {
      editorDom: null,
      content: '',
      tinymceId: ide,
      DefaultInit: {
        language_url: lang,
        language: 'zh_CN',
        height: 300,
        plugins: 'link lists image code table imagetools wordcount',
        toolbar:
          'bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink code | removeformat',
        imagetools_toolbar: 'rotateleft rotateright | flipv fliph | editimage imageoptions',
        skin_url: '/tinymce/skins/ui/oxide',
        init_instance_callback: (editor) => {
          editor.on('Input undo redo Change execCommand SetContent', (e) => {
            this.$emit('input', editor.getContent())
          })
        }
      }
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.content = newValue
      },
      deep: true,
      immediate: true
    },
    content: {
      handler(newValue) {
        this.$emit('input', newValue)
      },
      deep: true
    }
  },
  mounted() {
    // tinymce.init({});
    this.init()
  },
  methods: {
    setContent(value) {
      this.setIntervalL = setInterval(() => {
        if (this.editorDom) {
          if (this.setIntervalL) {
            clearInterval(this.setIntervalL)
          }
          if (value) {
            this.editorDom.setContent(value)
          }
        }
      }, 100)
    },
    init() {
      const self = this
      window.tinymce.init({
        setup: function(editor) {
          self.editorDom = editor
          setTimeout(() => {
            this.content && self.editorDom.setContent(this.content)
            self.$emit('input', editor.getContent())
          }, 500)
        },
        // 默认配置
        ...this.DefaultInit,
        ...this.set,
        // 图片上传
        images_upload_handler: function(blobInfo, success, failure) {
          const formData = new FormData()
          formData.append('file', blobInfo.blob())
          postJjjp(`/cms/upload/image`, formData).then(response => {
            console.log(response)
            if (response.code == 200) {
              success(response.data)
            } else {
              failure('上传失败！')
            }
          })
        },
        // 挂载的DOM对象
        selector: `#${this.tinymceId}`
      })
    }
  }
}
</script>

<style>
@import '../../tinymce/skins/ui/oxide/skin.min.css';
</style>

<style lang="scss">
.tox,
.tox-tinymce {
  /*height: 200px;*/
  width: 700px;
}
</style>
