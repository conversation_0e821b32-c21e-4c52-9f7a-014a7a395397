apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${SERVER_NAME}
  name: ${SERVER_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: ${POD_SPEC}
  selector:
    matchLabels:
      app: ${SERVER_NAME}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  minReadySeconds: 10
  template:
    metadata:
      labels:
        app: ${SERVER_NAME}
    spec:
      terminationGracePeriodSeconds: 90
      affinity: 
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution: 
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ${SERVER_NAME}
              topologyKey: kubernetes.io/hostname
      nodeSelector:
        ${K8S_NAMESPACE}: ${PROJECT}
      imagePullSecrets:
        - name: acr
      containers:
        - name: ${SERVER_NAME}
          imagePullPolicy: Always
          image: ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG}
          resources:
            requests:
              memory: ${REQUEST_MEM}
              cpu: ${REQUEST_CPU}
            limits:
              memory: ${LIMITED_MEM}
              cpu: ${LIMITED_CPU}
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            tcpSocket:                  
              port: ${SERVICE_PORT}
            timeoutSeconds: 5



---
apiVersion: v1
kind: Service
metadata:
  name: ${SERVER_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  sessionAffinity: ClientIP
  type: ClusterIP
  ports:
    - port: ${SERVICE_PORT}
      protocol: TCP
      targetPort: ${SERVICE_PORT}
      name: ${SERVER_NAME}
  selector:
    app:  ${SERVER_NAME}

