<template>
  <div>
    <FormDialog
      ref="dialogFormRef"
      v-model="dialogVisible"
      :title="formId ? '编辑策略' : '新增策略'"
      :form-model="overallStrategyForm"
      width="40%"
      :rules="rules"
      label-width="120px"
      @submit="handleSubmit"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-form-item label="策略名称" prop="strategyName">
        <el-input v-model="overallStrategyForm.strategyName" clearable :maxlength="30" placeholder="请输入策略名称" />
      </el-form-item>
      <el-form-item label="数据来源" prop="clueType">
        <el-select v-model="overallStrategyForm.clueType" :disabled="Boolean(formId)" clearable filterable placeholder="请选择数据来源" style="width: 100%">
          <el-option
            v-for="item in OverallStrategyClueSourceList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-row :gutter="10">
        <el-col :span="21">
          <el-form-item label="不接收重复线索" prop="refuseDuplicateClueTime">
            <el-input-number v-model="overallStrategyForm.refuseDuplicateClueTime" :min="0" :max="9999999999" :precision="0" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label-width="0">
            <el-select v-model="overallStrategyForm.timeUnit" style="width: 100%">
              <el-option
                v-for="item in NotReceiveRepeatClueTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="推送排序" prop="pushSortType">
        <el-select v-model="overallStrategyForm.pushSortType" clearable filterable placeholder="请选择推送排序" style="width: 100%">
          <el-option
            v-for="item in PushSortList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分发条件" prop="distributeType">
        <el-select v-model="overallStrategyForm.distributeType" clearable filterable placeholder="请选择分发条件" style="width: 100%">
          <el-option
            v-for="item in DistributionConditionList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </FormDialog>
  </div>
</template>

<script>
import FormDialog from '@/components/FormDialog/index.vue'
import { OverallStrategyClueSourceList, NotReceiveRepeatClueTypeList, PushSortList, DistributionConditionList } from '../enum'
import rules from '../rules/overallStrategyFormRules'
import { addStrategyApi, editStrategyApi, getStrategyDetailApi } from '@/api/distribution'

const overallStrategyFormState = {
  strategyName: '',
  clueType: null,
  refuseDuplicateClueTime: 0,
  timeUnit: 1,
  pushSortType: null,
  distributeType: null
}

export default {
  name: 'OverallStrategyForm',
  components: {
    FormDialog
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    formId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      designatedCitiesModel: false,
      designatedCitiesDate: [],
      overallStrategyForm: { ...overallStrategyFormState }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    OverallStrategyClueSourceList() {
      return OverallStrategyClueSourceList
    },
    NotReceiveRepeatClueTypeList() {
      return NotReceiveRepeatClueTypeList
    },
    PushSortList() {
      return PushSortList
    },
    DistributionConditionList() {
      return DistributionConditionList
    },
    rules() {
      return rules
    }
  },
  methods: {
    handleOpen() {
      this.formId && this.$nextTick(() => {
        this.$refs.dialogFormRef.loadFormData(() => {
          return getStrategyDetailApi(this.formId)
        })
      })
    },
    handleClose() {
      this.overallStrategyForm = { ...overallStrategyFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editStrategyApi({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await addStrategyApi(params)
      }
    },
    handleSubmit(params, done) {
      this.$confirm('是否确认提交？', '操作提示', {
        closeOnClickModal: false
      }).then(async() => {
        try {
          const { code } = await this.useHttpInterface(params)
          if (code !== 200) return false
          this.$message.success('操作成功')
          this.handleClose()
        } catch (err) {
          console.log(err)
        } finally {
          done()
        }
      }).catch(() => {
        done()
      })
    }
  }
}
</script>
