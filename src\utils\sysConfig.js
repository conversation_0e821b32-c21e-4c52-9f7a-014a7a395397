/*
 * @Author: 陈小豆
 * @Date: 2023-07-21 14:56:17
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-04 17:29:44
 */
export const linkList = [
  {
    label: '内部指定页面',
    value: 1
  },
  {
    label: '外链',
    value: 2
  },
  {
    label: '动态链接',
    value: 3
  },
  {
    label: '淘宝',
    value: 4
  },
  {
    label: '京东',
    value: 5
  },
  {
    label: '权益商品',
    value: 6
  },
  {
    label: '拼多多(主题)',
    value: 7
  },
  {
    label: '拼多多(频道)',
    value: 8
  },
  {
    label: '商品详情',
    value: 9
  },
  {
    label: '有赞',
    value: 10
  },
  {
    label: '超级砍价',
    value: 11
  }
]

// 可见设备
export const deviceAuthorityList = [
  { label: '全部', value: 'all' },
  { label: '安卓', value: 'android' },
  { label: '苹果', value: 'ios' }
]
// 可见用户
export const userAuthorityList = [
  { label: '游客用户', value: 1 },
  { label: '已参与0元购普通用户', value: 2 },
  { label: '未参与0元购普通用户', value: 3 },
  { label: '已参与0元购 已完成首单红包 爵士会员用户', value: 4 },
  { label: '已参与0元购 未完成首单红包 爵士会员用户', value: 5 },
  { label: '未参与0元购 已完成首单红包 爵士会员用户', value: 6 },
  { label: '未参与0元购 未完成首单红包 爵士会员用户', value: 7 }
]
export const deviceReportTypeList = [
  { label: '激活授权', value: 5 },
  { label: '登录', value: 1 },
  { label: '关键行为', value: 6 },
  { label: '站外购卡', value: 3 },
  { label: '站外购卡-互动', value: 8 },
  { label: '激励视频', value: 4 },
  { label: '激活+付费', value: 7 },
  // { label: '站内购卡', value: 2 },
  { label: '站内购卡（新）', value: 9 },
  { label: '站内付费点击', value: 10 },
  { label: '周期扣款', value: 11 },
  { label: '站外商品购买', value: 12 },
  { label: '站内商品购买', value: 13 }
]
export const smsSceneList = [
  { label: '留资未购卡', value: 'buy_card_call' },
  { label: '购卡未登录', value: 'electronic_card_order_success' },
  { label: '购卡未登录次日', value: 'electronic_card_order_success_tomorrow' },
  { label: '商品购买未登录', value: 'product_order_success' },
  { label: '下载未登录', value: 'download_un_login' },
  { label: '购卡未登录、下载未登录发送失败补偿', value: 'buy_card_download_retry' },
  { label: '发送成功未登录', value: 'send_success_un_login' },
  { label: '用户点击发送-未登录', value: 'user_send_un_login' }
]
