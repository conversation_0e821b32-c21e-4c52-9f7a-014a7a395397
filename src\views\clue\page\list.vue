<template>
  <page :request="request" :list="list" table-title="分发记录">
    <template #searchContainer>
      <el-button v-permission="'export'" :loading="exportLoading" plain size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
    </template>
  </page>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { ClueStatusList, VehicleStatusList, AuthorizationStatusList, ClueStatusMap } from '../enum'
import { getDistributeRecordListApi, exportDistributeRecordApi } from '@/api/clue'
import { OverallStrategyClueSourceList } from '@/views/distribution/enum'
import { getProductTypeListApi } from '@/api/product'
import moment from 'moment/moment'

export default {
  name: 'list',
  components: { page },
  data() {
    return {
      exportLoading: false,
      listQuery: {},
      productTypeList: [],
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getDistributeRecordListApi({ ...this.listQuery })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '数据来源',
          key: 'source',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: OverallStrategyClueSourceList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '分发类型',
          key: 'productId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.productTypeList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '记录状态',
          key: 'loanStatus',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: ClueStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '接收时间',
          key: 'receivingTime',
          type: formItemType.rangeDatePicker,
          childKey: ['receivingStartTime', 'receivingEndTime'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '分发时间',
          key: 'distributeTime',
          type: formItemType.rangeDatePicker,
          childKey: ['distributeStartTime', 'distributeEndTime'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '记录ID',
          key: 'id',
          width: 80,
          render: (_h, params) => {
            return <el-button type='text' size='mini'>{params.data.row.id}</el-button>
          }
        },
        {
          title: '线索ID',
          key: 'clueId',
          width: 80
        },
        {
          title: '数据来源',
          key: 'source',
          width: 120,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: OverallStrategyClueSourceList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '接收时间',
          key: 'receivingTime',
          render: (_h, params) => {
            return <span>{moment(params.data.row.receivingTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '产品标签',
          key: 'productTag'
        },
        {
          title: '姓名',
          key: 'name'
        },
        {
          title: '手机号',
          key: 'mobileNo',
          width: 100
        },
        {
          title: '城市',
          key: 'city'
        },
        {
          title: '性别',
          key: 'sex',
          render: (_h, params) => {
            const sexMap = {
              1: '男',
              0: '女'
            }
            return <span>{sexMap[params.data.row.sex]}</span>
          }
        },
        {
          title: '年龄',
          key: 'age'
        },
        {
          title: '车牌号',
          key: 'licenseLateNumber'
        },
        {
          title: '车辆状态',
          key: 'carLoan',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: VehicleStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        // {
        //   title: '车辆识别代号',
        //   key: 'carIdentificationNumber',
        //   width: 150
        // },
        {
          title: '芝麻分',
          key: 'sesameScoreName'
        },
        {
          title: '是否授权',
          key: 'authorizationStatus',
          width: 100,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: AuthorizationStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '记录状态',
          key: 'loanStatus',
          width: 100,
          render: (_h, params) => {
            const color = params.data.row.loanStatus === 4 ? '#f00' : ''

            return <span style={{
              color
            }}>{ClueStatusMap[params.data.row.loanStatus]}</span>
          }
        },
        {
          title: '失败原因',
          key: 'errorMsg',
          width: 150
        },
        {
          title: '分发类型',
          key: 'productTypeName',
          width: 150
        },
        {
          title: '分发时间',
          key: 'distributeTime',
          render: (_h, params) => {
            return <span>{moment(params.data.row.distributeTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '分发机构',
          key: 'agenciesName'
        }
      ]
    }
  },
  created() {
    this.getProductTypeList()
  },
  methods: {
    async getProductTypeList() {
      try {
        const { data } = await getProductTypeListApi()

        this.productTypeList = data.map(item => {
          return {
            id: item.id,
            name: item.productName
          }
        })
      } catch (err) {
        console.log(err)
      }
    },
    async handleExport() {
      this.exportLoading = true
      try {
        window.open(exportDistributeRecordApi({
          ...this.listQuery,
          pageSize: 10000,
          token: this.$store.getters.authorization
        }))
      } catch (err) {
        console.log(err)
      } finally {
        this.exportLoading = false
      }
    }
  }
}
</script>
