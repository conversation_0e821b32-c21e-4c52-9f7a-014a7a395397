::v-deep .el-drawer__body {
    overflow: scroll;
    padding: 0 30px 20px;
    position: relative;
  }
  
  .close_button {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0, 1);
    text-align: center;
    cursor: pointer;
  
    i {
      color: white;
      line-height: 40px;
    }
  }
  
  .drawer_package {
    height: 100%;
    position: relative;
  
    .drawer_title {
      padding: 10px 20px 5px;
      vertical-align: middle;
  
      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  
  .addForm_package {
    background-color: rgb(189, 184, 184, 0.1);
    padding: 15px;
    height: 100%;
  
    .demo-ruleForm {
      background-color: #ffffff;
      width: 100%;
      height: 100%;
      position: relative;
    }
  }
  
  .form_view {
    background-color: #ffffff;
    width: 100%;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
  
    .form_view_title {
      margin-bottom: 20px;
  
      .title_line {
        width: 2px;
        height: 10px;
        background-color: #66b1ff;
        display: inline-block;
        vertical-align: middle;
      }
  
      span {
        padding-left: 5px;
        vertical-align: middle;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  