<template>
  <div :class="pClass" class="tree-submenu-span" @click="()=>{isCollapse=!isCollapse;$emit('click')}">
    <slot />
    <span class="span el-icon-arrow-down" />
  </div>
</template>
<script>
export default {
  name: 'treeCollapse',
  props: {
    menuClass: {
      type: Array,
      default: () => []
    },
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isCollapse: true
    }
  },
  computed: {
    pClass() {
      const menuClass = [...this.menuClass]
      this.isCollapse ? menuClass.push('collapse-menu') : menuClass.push('expand-menu')
      return menuClass
    }
  },
  created() {
  }
}
</script>
<style lang="scss" scoped>
.tree-submenu-span{
    position: relative;
      >.span{
      color: #555555;
      position: absolute;
      top: 50%;
      right: 10px;
      font-size: 14px;
      transform:translateY(-50%) rotate(0);
      transform-origin: center center;
      transition: all linear 0.3s;
    }
    &.collapse-menu{
      >.span{
       transform:translateY(-50%) rotate(-90deg);
      }
    }
    }
</style>
