export default {
  state: {
    table: true,
    tableShouldUpdate: false
  },
  mutations: {
    refresh(state, obj) { // 页面刷新
      if (!obj.key) {
        obj.key = 'load'
      }
      state[obj.key] = false
      obj.vm.$nextTick(() => {
        state[obj.key] = true
      })
    },
    setTableShouldUpdate(state, payload) {
      state.tableShouldUpdate = !state.tableShouldUpdate
    }
  },
  actions: {
    tableRefresh({ commit }, vm) {
      commit('refresh', {
        key: 'table',
        vm: vm })
    }
  }
}
