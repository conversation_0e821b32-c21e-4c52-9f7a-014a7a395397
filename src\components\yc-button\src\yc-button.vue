<template>
  <div>
    <button @click.stop="handleClick" :disabled="disabled" :class="['yc-default-btn',type]">
      <slot></slot>
    </button>
  </div>
</template>

<script>
  export default {
    name: "yc-button",
    props: {
      type: {
        type: String,
        default: "primary"
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      handleClick() {
        this.$emit("click")
      }
    }
  }
</script>

<style lang="scss" scoped>
  .yc-default-btn {
    width: 100%;
    height: 44px;
    background: linear-gradient(270deg, rgba(255, 237, 0, 1) 0%, rgba(255, 215, 0, 1) 100%);
    border-radius: .76rem;
    font-size: 20px;
    font-weight: 600;
    color: rgba(97, 69, 77, 1);
    letter-spacing: 1px;
    cursor: pointer;
  }

  .default {

  }

  .primary {
    border-top: 2px solid rgba(236, 103, 155, 1);
    border-bottom: 2px solid rgba(119, 202, 255, 1);
  }
</style>
