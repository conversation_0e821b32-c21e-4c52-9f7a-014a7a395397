/*
 * 系统管理子路由
 * */

const system = [
  {
    path: '/system/role',
    name: 'role',
    meta: {
      title: '系统角色',
      buttons: [
        { key: 'add', name: '新增角色' },
        { key: 'edit', name: '修改角色' },
        { key: 'delete', name: '禁用/启用' },
        { key: 'del-delete', name: '删除' }
      ]
    },
    component: () => import('@/views/system/page/role')
  },
  {
    path: '/system/menu',
    name: 'menu',
    meta: {
      title: '系统菜单',
      buttons: [
        { key: 'add', name: '新增菜单' },
        { key: 'edit', name: '编辑菜单' },
        { key: 'delete', name: '删除菜单' }
      ]
    },
    component: () => import('@/views/system/page/menu')
  },
  {
    path: '/system/userManagement',
    redirect: '/system/accountRole',
    name: 'account',
    meta: {
      title: '用户管理'
    }
  },
  {
    path: '/system/accountRole',
    name: 'accountRole',
    meta: {
      title: '用户管理',
      noVerify: true,
      activeMenu: '/system/userManagement',
      buttons: [
        {
          key: 'addDepartment',
          name: '添加部门'
        },
        {
          key: 'edit',
          name: '修改部门'
        },
        {
          key: 'delete',
          name: '删除部门'
        }
      ]
    },
    component: () => import('@/views/system/page/departmentUser')
  },
  {
    path: '/userAccount',
    name: 'userAccount',
    meta: {
      title: '用户列表',
      buttons: [
        {
          key: 'system/accountRole/addUser',
          name: '添加用户'
        },
        {
          key: 'system/accountRole/accountEditUser',
          name: '编辑'
        },
        {
          key: 'system/accountRole/accountEditStatus',
          name: '禁用/启用'
        }
      ]
    },
    component: () => import('@/views/system/page/departmentUser')
  },
  {
    path: '/system/logRecord',
    name: 'logRecord',
    meta: {
      title: '系统日志'
    },
    component: () => import('@/views/system/page/logTable')
  },
  {
    path: '/system/argument',
    name: 'argument',
    meta: {
      title: '系统参数'
    },
    component: () => import('@/views/system/page/argument')
  },
  {
    path: '/system/ipWhiteList',
    name: 'ipWhiteList',
    meta: {
      title: 'IP白名单'
    },
    component: () => import('@/views/system/page/ip')
  }
]

export default system
