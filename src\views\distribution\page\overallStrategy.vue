<template>
  <div>
    <page :list="list" table-title="总策略" :request="request">
      <template #searchContainer>
        <el-button v-permission="'create'" plain size="small" type="primary" @click="handleOpenForm(null)">创建策略</el-button>
      </template>
    </page>
    <OverallStrategyForm v-model="overallStrategyFormModel" :form-id="overallStrategyId" @close="handleFormClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import {
  ApiMechanismStrategyStatusList,
  DistributionConditionList,
  OverallStrategyClueSourceList,
  PushSortList,
  NotReceiveRepeatClueTypeMap
} from '../enum'
import OverallStrategyForm from '../components/OverallStrategyForm.vue'
import { changeStrategyStatusApi, getStrategyListApi, changeStrategyDefault<PERSON>pi, getStrategyDefault<PERSON>pi } from '@/api/distribution'
import { hasPermission } from '@/utils/menuCodes'

export default {
  name: 'OverallStrategy',
  components: {
    page,
    OverallStrategyForm
  },
  data() {
    return {
      overallStrategyId: null,
      overallStrategyFormModel: false,
      request: {
        getListUrl: async data => {
          const res = await getStrategyListApi(data)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'strategyName',
          title: '策略名称',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '请输入策略名称'
          }
        },
        {
          key: 'clueType',
          title: '数据来源',
          search: true,
          tableHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: OverallStrategyClueSourceList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            placeholder: '请选择'
          }
        },
        {
          key: 'status',
          title: '状态',
          search: true,
          tableHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: ApiMechanismStrategyStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          key: 'createDate',
          title: '创建时间',
          type: formItemType.rangeDatePicker,
          childKey: ['createTimeStart', 'createTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          tableHidden: true,
          search: true,
          pickerDay: 999999
        },
        {
          key: 'updateDate',
          title: '更新时间',
          type: formItemType.rangeDatePicker,
          childKey: ['updateTimeStart', 'updateTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          search: true,
          pickerDay: 999999,
          tableHidden: true
        },
        {
          key: 'index',
          title: '序号',
          width: 80,
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '策略名称',
          key: 'strategyName'
        },
        {
          key: 'clueType',
          title: '数据来源',
          tableView: tableItemType.tableView.text,
          list: OverallStrategyClueSourceList,
          width: 100,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '不接收重复线索',
          key: 'refuseDuplicateClueTime',
          render: (_h, params) => {
            return <span>
              { params.data.row.refuseDuplicateClueTime }{ NotReceiveRepeatClueTypeMap[params.data.row.timeUnit] }内
            </span>
          }
        },
        {
          key: 'pushSortType',
          title: '推送排序',
          tableView: tableItemType.tableView.text,
          list: PushSortList,
          width: 150,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          key: 'distributeType',
          title: '分发条件',
          tableView: tableItemType.tableView.text,
          list: DistributionConditionList,
          width: 100,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          key: 'defaultType',
          title: '是否默认策略',
          render: (h, params) => {
            return (
              <el-switch
                v-model={params.data.row.defaultType}
                active-value={1}
                disabled={!params.data.row.status}
                inactive-value={0}
                onChange={(e) => this.handleDefaultChange(e, params.data.row)}
              />
            )
          },
          width: 120
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: ApiMechanismStrategyStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 80
        },
        {
          key: 'createByName',
          title: '创建人',
          render: (_h, params) => {
            return <div style='text-align: center;'>{params.data.row.createByName} {params.data.row.createByMobile}</div>
          }
        },
        {
          key: 'updateByName',
          title: '更新人',
          render: (_h, params) => {
            return <div style='text-align: center;'>{params.data.row.updateByName} {params.data.row.updateByMobile}</div>
          }
        },
        {
          key: 'createTime',
          title: '创建时间'
        },
        {
          key: 'updateTime',
          title: '更新时间'
        },
        {
          key: 'operation',
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          width: 180,
          fixed: 'right',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'edit' }),
              click: (_$index, _item, params) => {
                this.handleOpenForm(params.id)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'status' }),
              render: (_h, params) => {
                return <el-button type='primary' size='mini' plain disabled={params.data.defaultType === 1} onClick={() => this.changeStrategyStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    async handleDefaultChange(e, row) {
      const revert = () => (row.defaultType ^= 1)
      const request = async() => {
        try {
          const { code } = await changeStrategyDefaultApi({ id: row.id, status: row.defaultType })
          code === 200 ? (this.$message.success('操作成功'), this.$store.dispatch('tableRefresh', this)) : revert()
        } catch { revert() }
      }

      if (e === 1) {
        try {
          const { code, data } = await getStrategyDefaultApi(row.id)
          return code === 200 && data.haveDefault
            ? await this.$confirm(`是否将${row.strategyName}改为默认策略？`, '提示', { closeOnClickModal: false }).then(request).catch(() => { this.$message('取消操作'); revert() })
            : await request()
        } catch { revert() }
      } else {
        return this.$confirm('确认关闭默认策略吗？', '提示', { closeOnClickModal: false })
          .then(request)
          .catch(() => { this.$message('取消操作'); revert() })
      }
    },
    handleOpenForm(id) {
      this.overallStrategyId = id
      this.overallStrategyFormModel = true
    },
    handleFormClose() {
      this.overallStrategyId = null
      this.$store.dispatch('tableRefresh', this)
    },
    changeStrategyStatus(row) {
      this.$confirm(`确认${row.status === 1 ? '禁用' : '启用'}此策略？`, '', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await changeStrategyStatusApi({
            id: row.id,
            status: row.status === 1 ? 0 : 1
          })

          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    }
  }
}
</script>
