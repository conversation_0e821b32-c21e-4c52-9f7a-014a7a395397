<template>
  <div class="role">
    <page :request="request" :hidden-reset-button="true" :list="list">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" @click="handleExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { get_log_page, get_log_export } from '@/api/system'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import { getAllUserList } from '@/views/system/page/useUsers/index.js'
export default {
  components: {
    page
  },
  data() {
    return {
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data, adminId: this.listQuery.adminId }
          const res = await get_log_page(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      },
      listQuery: {
        startDate: moment().subtract(7, 'days').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        adminId: null
      },
      userList: [],
      showUserList: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '操作日期',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          search: true,
          val: [this.listQuery.startDate, this.listQuery.endDate],
          tableHidden: true,
          pickerDay: 365 * 50
        },
        {
          key: 'createTime',
          title: '操作时间',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          key: 'userNane',
          title: '操作人'
        },
        {
          key: 'adminId',
          searchKey: 'adminId',
          title: '操作人',
          type: formItemType.select,
          search: true,
          tableHidden: true,
          render: (h, params) => {
            return <el-select value={this.listQuery.adminId} on={{
              input: value => {
                this.listQuery.adminId = value
                this.handleSearch(this.listQuery.adminId)
              }
            }} filterable clearable placeholder='请输入姓名/手机号' filter-method={(value) => this.handleSearch(value)}>{this.showUserList.map(item => (<el-option key={item.id} value={item.id} label={item.username}>{item.username}</el-option>))}</el-select>
          }
        },
        {
          key: 'operation',
          title: '操作类型'
        },
        {
          key: 'content',
          title: '操作内容'
        }

      ]
    }
  },
  async created() {
    this.userList = await getAllUserList()
    this.showUserList = [...this.userList]
  },
  methods: {
    handleExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = get_log_export({
        ...data,
        token: this.$store.getters.authorization
      })
    },

    handleSearch(value) {
      if (!value) {
        this.showUserList = this.userList
      } else {
        this.showUserList = this.userList.filter(item => item.username.includes(value) || item.mobileNo.includes(value))
      }
    }

  }
}
</script>

  <style lang="scss" scoped>
  </style>
