/*
 * 机构管理子路由
 * */

const mechanism = [
  {
    path: '/mechanism',
    name: 'Mechanism',
    redirect: '/mechanism/list'
  },
  {
    path: '/mechanism/list',
    name: 'MechanismList',
    meta: {
      title: '机构列表',
      buttons: [
        { key: 'add', name: '创建机构' },
        { key: 'detail', name: '详情' },
        { key: 'certification', name: '资质管理' },
        { key: 'account', name: '账号管理' },
        { key: 'product', name: '产品管理' },
        { key: 'status', name: '禁用/启用' }
      ]
    },
    component: () => import('@/views/mechanism/page/list.vue')
  },
  {
    path: '/mechanism/form',
    name: 'MechanismForm',
    meta: {
      title: '创建机构',
      parentTitle: '机构列表',
      activeMenu: '/mechanism/list'
    },
    component: () => import('@/views/mechanism/page/mechanismForm.vue')
  },
  {
    path: '/mechanism/api-list',
    name: 'ApiMechanismList',
    meta: {
      title: 'API机构',
      buttons: [
        { key: 'detail', name: '详情' }
      ]
    },
    component: () => import('@/views/mechanism/page/apiMechanismList.vue')
  }
]

export default mechanism
