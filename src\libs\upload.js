const saveAs = (obj, fileName) => {
  var tmpa = document.createElement('a')
  tmpa.download = fileName || '下载'
  tmpa.href = URL.createObjectURL(obj)
  tmpa.click()
  setTimeout(function() {
    // 延时释放
    URL.revokeObjectURL(obj)
  }, 100)
}
const s2ab = (s) => {
  if (typeof ArrayBuffer !== 'undefined') {
    var buf = new ArrayBuffer(s.length)
    var view = new Uint8Array(buf)
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
    return buf
  } else {
    var buf = new Array(s.length)
    for (var i = 0; i != s.length; ++i) buf[i] = s.charCodeAt(i) & 0xff
    return buf
  }
}

/** 导出表格
 *  @list 导出数据
 *  @title 导出表格名称
 * */
export const uploadXlsx = (list, title) => {
  let data = list
  const wopts = { bookType: 'xlsx', bookSST: true, type: 'binary' }
  var wb = { SheetNames: ['Sheet1'], Sheets: {}, Props: {}}
  data = XLSX.utils.json_to_sheet(data)
  data['!cols'] = new Array(10)
  for (var i = 0; i < data['!cols'].length; i++) {
    data['!cols'][i] = { width: 25 }
  }
  wb.Sheets['Sheet1'] = data
  saveAs(
    new Blob([s2ab(XLSX.write(wb, wopts))], {
      type: 'application/octet-stream'
    }),
    title + '.' + (wopts.bookType == 'biff2' ? 'xls' : wopts.bookType)
  )
}
