#发布流程梳理
## Global variables
variables:
  # 服务名: 用于容器，日志的创建
  SERVER_NAME: "admin-frontend"
  # 容器: 运行的服务端口
  SERVICE_PORT: 80
  # 构建命令
  VUE_INSTALL: "npm install"
  # 构建命令
  VUE_BUILD: "npm run build"
  # 镜像标签
  DOCKER_TAG: "${K8S_NAMESPACE}_${CI_COMMIT_SHORT_SHA}_${CI_PIPELINE_ID}"
  # 镜像文件
  DOCKERFILE_PATH: './.gitlab/Dockerfile'
  # 模版: Kubernetes创建相关资源的模板。主要是Deploymentod, Service, AliLogs
  K8S_TEMPLATES: ".gitlab/k8s_template.yml"
  # 模版目录: 保存K8S的命令
  K8S_TEMPLATES_DIR: "./deploy/${K8S_NAMESPACE}"
  # 镜像地址
  REGISTRY_HOST: crpi-n101a83isbuxrjr0-vpc.cn-hangzhou.personal.cr.aliyuncs.com
  # 镜像仓库
  DOCKER_REPO: car_test
  # 预留变量
  PROJECT: cyberton
  # 人员: 来自CICD传来的变量
  RUN_JOB_USER: $RUN_JOB_USER
  # 描述: 来自CICD传来的变量
  RUN_JOB_DESC: $RUN_JOB_DESC
  # NGINX模版
  NGINX_TEMPLATES: ".gitlab/nginx-template.conf"
  # 该项目钉钉通知TOKEN，用于webhook通知
  DING_API_SECRET: ${DING_API_SECRET}
  # 该项目钉钉通知API，用于webhook通知
  DING_API_TOKEN: ${DING_API_TOKEN}
  # 指定Node版本
  NODE_VERSION: /usr/local/node14/bin

stages:
  - trigger


车贷分发-Admin-Frontend(K8S)-测试:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    RUNNER_TAGS: 'CYBERTON_TEST_NODE'
    DEPLOY_ENV: 'test'
    K8S_NAMESPACE: "cyberton-test"
    POD_SPEC: 1
    REQUEST_MEM: '64M'
    REQUEST_CPU: '0.01'
    LIMITED_MEM: '256M'
    LIMITED_CPU: '0.1'
    K8S_ADDRESS: ${CYBERTON_TEST_K8S}
  only:
    refs:
      - test
    variables:
      - $PROJECT_JOB == "CYBERTON_ADMIN_FRONT_TEST"


车贷分发-Admin-Frontend(K8S)-预发:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    RUNNER_TAGS: 'CYBERTON_PRE_NODE'
    DEPLOY_ENV: 'preRelease'
    K8S_NAMESPACE: "cyberton-pre"
    POD_SPEC: 1
    REQUEST_MEM: '64M'
    REQUEST_CPU: '0.01'
    LIMITED_MEM: '256M'
    LIMITED_CPU: '0.1'
    K8S_ADDRESS: ${CYBERTON_PRE_K8S}
  only:
    refs:
      - pre-release
    variables:
      - $PROJECT_JOB == "CYBERTON_ADMIN_FRONT_PRE"


车贷分发-Admin-Frontend(K8S)-生产:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    RUNNER_TAGS: 'CYBERTON_PROD_NODE'
    DEPLOY_ENV: 'prod'
    K8S_NAMESPACE: "cyberton-pro"
    POD_SPEC: 2
    REQUEST_MEM: '64M'
    REQUEST_CPU: '0.01'
    LIMITED_MEM: '256M'
    LIMITED_CPU: '0.1'
    K8S_ADDRESS: ${CYBERTON_PROD_K8S}
  only:
    refs:
      - master
    variables:
      - $PROJECT_JOB == "CYBERTON_ADMIN_FRONT_PROD"
