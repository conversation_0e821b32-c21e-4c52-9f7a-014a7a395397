import { cascaderConfig, error, formItemType, keyWord, selectConfig } from '@/config/sysConfig.js'
import basics from '@/libs/basics'
import moment from 'moment'
import Vue from 'vue'
/*
 * 请注意 数据默认val请全为string类型
 * */

const parentProps = {
  item: {
    type: Object,
    default: () => {}
  },
  value: ''
}

const getOptionsAttr = (vm, attrName, type = {}) => {
  const itemOptions = vm.item.options
  return itemOptions && itemOptions[attrName] ? itemOptions[attrName](vm) : type
}

const filterListItem = (format, data, defaultValue) => {
  let defaultLabel = ''
  if (basics.isString(data)) {
    defaultLabel = data
  }

  if (basics.isObj(data) && !format && !basics.isNull(data.label) && !basics.isNull(data.value)) {
    defaultLabel = data.label
    defaultValue = data.value
  }
  const value = !(format && format.value) ? defaultValue : data[format.value]
  const label = !(format && format.label) ? defaultLabel : data[format.label]
  return {
    value,
    label
  }
}

export const getDefaultListItem = (list, format, callback) => {
  if (basics.isFunction(format)) callback = format
  return list.map((item, index) => {
    let formatItem = filterListItem(format, item, index)
    const { value, label } = formatItem
    if (basics.isString(value) || basics.isNumber(value)) {
      formatItem = {
        label,
        value: String(value)
      }
    }
    const obj = {
      ...{},
      ...formatItem,
      ...{
        index
      }
    }
    return callback ? callback(obj) : {}
  })
}

const basicsRender = (nodeName, options = () => ({}), childrenNode = () => false) => {
  return {
    render: function(h) {
      if (this.basics.isNull(this.item.list)) this.item.list = []
      if (!this.basics.isArray(this.item.list)) {
        this.item.list = []
        error(`${this.item.key} list must Array`)
      }

      const VMoptions = options(this)
      /* let filterList = ;*/
      /* 发现需要list格式化的 进行格式化*/
      /* if (this.item.listFormat) {
        filterList = this.item.list.map((val, index) => {
          return filterListItem(this.item, val, index);
        });
      }*/
      const VMchildrenNode = childrenNode(this, h, this.item.list, this.item.listFormat)
      const itemOptions = this.item.options
      const on = getOptionsAttr(this, 'on')
      const valueType = itemOptions && itemOptions.valueType ? itemOptions.valueType : ''
      let onInput = () => {}
      if (on.input) {
        onInput = on.input
        delete on.input
      }
      return h(
        nodeName,
        {
          props: Object.assign(
            {},
            {
              value: this.value
            },
            VMoptions.props || {},
            getOptionsAttr(this, 'props')
          ),
          on: Object.assign(
            {},
            {
              input: value => {
                if (valueType === 'Number') {
                  value = Number(value)
                }
                const newValue = basics.isNull(value)
                  ? this.item.type.indexOf(keyWord.multiple) >= 0
                    ? []
                    : ''
                  : value
                if (typeof newValue === 'string') {
                  this.$emit('input', newValue.trim())
                } else {
                  this.$emit('input', newValue)
                }

                onInput(newValue)
              }
            },
            VMoptions.on || {},
            on
          ),
          nativeOn:
            nodeName === 'span' ? null : Object.assign({}, VMoptions.nativeOn || {}, getOptionsAttr(this, 'nativeOn')),
          attrs: Object.assign(
            { inheritAttrs: false },
            this.$attrs || {},
            {
              placeholder: (itemOptions && itemOptions.placeholder) || this.item.title
            },
            VMoptions.attrs || {},
            getOptionsAttr(this, 'attrs')
          ),
          style: Object.assign({}, VMoptions.style || {}, getOptionsAttr(this, 'style'))
        },
        VMchildrenNode
      )
    },
    props: parentProps
  }
}

export const SInput = Vue.component('SInput', basicsRender('el-input', vm => {
  return {
    style: {
      width: '200px'
    }
  }
}))

export const SSelect = Vue.component(
  'SSelect',
  basicsRender(
    'el-select',
    vm => {
      return {
        props: Object.assign(
          {},
          {
            value: basics.isArray(vm.value) ? vm.value : String(vm.value),
            multiple: basics.isArray(vm.value)
          },
          {
            ...selectConfig,
            clearable: vm.item.clearable !== false,
            multiple: vm.item.multiple || false,
            disabled: vm.item?.disabled ?? false
          }
        ),
        style: {
          width: '200px'
        }
      }
    },
    (vm, h, list, format) =>
      getDefaultListItem(list, format, item => {
        return h('el-option', {
          props: {
            key: item.value,
            label: item.label,
            value: item.value,
            disabled: item.disabled
          }
        })
      })
  )
)

export const SSelectSearch = Vue.component(
  'SSelectSearch',
  basicsRender('SSelect', vm => {
    return {
      attrs: {
        multiple: basics.isArray(vm.value),
        item: vm.item,
        filterable: true,
        remote: true,
        reserveKeyword: true,
        clearable: false,
        remoteMethod: value => {
          if (basics.isNull(value)) {
            vm.$emit('listChange', vm.item.$index, [])
            return false
          }
          if (vm.item.options && vm.item.options.on && vm.item.options.on.remoteMethod) {
            vm.item.options.on.remoteMethod(vm, value)
          } else {
            const request = vm.item.selectSearchRequest
            request(value).then(msg => {
              vm.$emit('listChange', vm.item.$index, msg)
            })
          }
        }
      },
      style: {
        width: '80%'
      }
    }
  })
)

export const SRadio = Vue.component(
  'SRadio',
  basicsRender('el-radio-group', undefined, (vm, h, list, format) =>
    getDefaultListItem(list, format, item => {
      const data = vm.item.options || {}
      if (data.valueType === 'Number') {
        item.value = Number(item.value)
      } else if (data.valueType === 'Boolean') {
        item.value = Boolean(item.value)
      } else {
        item.value = String(item.value)
      }
      return h(
        'el-radio',
        {
          props: {
            key: item.value,
            label: item.value,
            disabled: item.disabled
          }
        },
        item.label
      )
    })
  )
)

export const SRadioButton = Vue.component(
  'SRadioButton',
  basicsRender('el-radio-group', undefined, (vm, h, list, format) =>
    getDefaultListItem(list, format, item => {
      const data = vm.item.options || {}
      if (data.valueType === 'Number') {
        item.value = Number(item.value)
      } else if (data.valueType === 'Boolean') {
        item.value = Boolean(item.value)
      } else {
        item.value = String(item.value)
      }
      return h(
        'el-radio-button',
        {
          props: {
            key: item.value,
            label: item.value,
            disabled: item.disabled
          }
        },
        item.label
      )
    })
  )
)

export const SCheckbox = Vue.component(
  'SCheckbox',
  basicsRender('el-checkbox-group', undefined, (vm, h, list, format) =>
    getDefaultListItem(list, format, item => {
      return h(
        'el-checkbox',
        {
          props: {
            key: item.value,
            label: item.value,
            disabled: item.disabled
          }
        },
        item.label
      )
    })
  )
)

export const SInputNumber = Vue.component(
  'SInputNumber',
  basicsRender('el-input-number', vm => {
    const data = vm.item.options || {}
    return {
      props: {
        max: data.max,
        min: data.min,
        step: data.step,
        precision: data.precision,
        disabled: data.disabled
      }
    }
  })
)
const dateOptionst = {
  pickerMinDate: null,
  onPick: ({ minDate }) => {
    dateOptionst.pickerMinDate = new Date(minDate).getTime()
  },
  disabledDate: time => {
    if (dateOptionst.pickerMinDate) {
      const oneDay = 60 * 24 * 3600 * 1000
      const maxTime = dateOptionst.pickerMinDate + oneDay
      const minTime = dateOptionst.pickerMinDate - oneDay
      return time.getTime() > maxTime || time.getTime() < minTime || time.getTime() > new Date().getTime()
    } else {
      return time.getTime() > Date.now() - 24 * 3600 * 1000
    }
  },
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}

export const STimePicker = Vue.component(
  'STimePicker',
  basicsRender('el-time-picker', vm => {
    const data = vm.item.options || {}
    let value = vm.value
    if (!data.valueFormat || data.valueFormat !== 'timestamp') {
      if (basics.isNumber(value)) {
        value = moment(value).format('HH:mm:ss')
        vm.$emit('input', value)
      }
    }
    return {
      props: {
        value: value,
        type: data.type ? data.type : 'time',
        format: data.format ? data.format : 'HH : mm : ss',
        valueFormat: data.valueFormat ? data.valueFormat : 'HH:mm:ss',
        'is-range': data['is-range'] || false
      }
    }
  })
)

export const STimePickerRange = Vue.component(
  'STimePickerRange',
  basicsRender('el-time-picker', vm => {
    const data = vm.item.options || {}
    let value = vm.value
    if (!data.valueFormat || data.valueFormat !== 'timestamp') {
      if (basics.isNumber(value)) {
        value = moment(value).format('HH:mm:ss')
        vm.$emit('input', value)
      }
    }
    return {
      props: {
        value: value,
        type: data.type ? data.type : 'time',
        format: data.format ? data.format : 'HH : mm : ss',
        valueFormat: data.valueFormat ? data.valueFormat : 'HH:mm:ss',
        'is-range': true
      },
      style: {
        width: '220px'
      },
      attrs: {
        startPlaceholder: (vm.item.options && vm.item.options.startPlaceholder) || '开始时间',
        endPlaceholder: (vm.item.options && vm.item.options.endPlaceholder) || '结束时间'
      }
    }
  })
)

export const SDateTimePicker = Vue.component(
  'SDateTimePicker',
  basicsRender('el-date-picker', vm => {
    const data = vm.item.options || {}
    let value = vm.value
    if (!data.valueFormat || data.valueFormat !== 'timestamp') {
      if (basics.isNumber(value)) {
        value = moment(value).format('yyyy-MM-dd HH:mm:ss')
        vm.$emit('input', value)
      }
    }
    return {
      props: {
        value: value,
        type: data.type ? data.type : 'datetime',
        format: data.format ? data.format : 'yyyy-MM-dd HH:mm:ss',
        valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd HH:mm:ss'
      },
      attrs: {
        startPlaceholder: (vm.item.options && vm.item.options.startPlaceholder) || '开始时间',
        endPlaceholder: (vm.item.options && vm.item.options.endPlaceholder) || '结束时间',
        defaultTime: (vm.item.options && vm.item.options.defaultTime) || ['12:00:00']
      }
    }
  })
)

export const SDatePicker = Vue.component(
  'SDatePicker',
  basicsRender('el-date-picker', vm => {
    const data = vm.item.options || {}
    let value = vm.value
    if (!data.valueFormat || data.valueFormat !== 'timestamp') {
      if (basics.isNumber(value)) {
        value = moment(value).format('YYYY-MM-dd')
        vm.$emit('input', value)
      }
    }
    return {
      props: {
        value: value,
        type: data.type ? data.type : 'date',
        format: data.format ? data.format : 'yyyy - MM - dd',
        valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd'
      }
    }
  })
)

export const SDatePickerDaterange = Vue.component(
  'SDatePicker',
  basicsRender('el-date-picker', vm => {
    const data = vm.item.options || {}
    const value = [].concat(vm.value || [])
    return {
      props: {
        clearable: vm.item.clearable || false,
        value: value,
        type: 'daterange',
        format: data.format ? ((data.format === 'YYYYMMDD' || data.format === 'YYYY-MM-DD' || data.format === 'YYYY-MM-dd' || data.format === 'yyyy-MM-dd') ? 'yyyy-MM-dd' : data.format) : 'yyyy-MM-dd',
        valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd',
        pickerOptions: vm.item.type === formItemType.rangeDatePicker ? (!vm.item.notSevenDayLimit ? basics.pickerOptions(vm.item.pickerDay) : dateOptionst) : {}
      },
      style: {
        width: '250px'
      },
      attrs: {
        startPlaceholder: (vm.item.options && vm.item.options.startPlaceholder) || '开始日期',
        endPlaceholder: (vm.item.options && vm.item.options.endPlaceholder) || '结束日期'
      }
    }
  })
)

export const SDatePickerDaterangeGai = Vue.component(
  'SDatePicker',
  basicsRender('el-date-picker', vm => {
    const data = vm.item.options || {}
    const value = [].concat(vm.value || [])
    return {
      props: {
        clearable: false,
        value: value,
        type: 'daterange',
        format: data.format ? ((data.format === 'YYYYMMDD' || data.format === 'YYYY-MM-DD' || data.format === 'YYYY-MM-dd' || data.format === 'yyyy-MM-dd') ? 'yyyy-MM-dd' : data.format) : 'yyyy-MM-dd',
        valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd',
        pickerOptions: vm.item.type === formItemType.datePickerDaterangeGai ? (!vm.item.notSevenDayLimit ? basics.pickerOptions() : dateOptionst) : {}
      },
      style: {
        width: '250px'
      },
      attrs: {
        startPlaceholder: (vm.item.options && vm.item.options.startPlaceholder) || '开始日期',
        endPlaceholder: (vm.item.options && vm.item.options.endPlaceholder) || '结束日期'
      }
    }
  })
  // basicsRender('span', undefined, (vm, h, list) => {
  //   const data = vm.item.options || {}
  //   const value = [].concat(vm.value || [])
  //   // console.info(value,'valuevaluevalue')
  //   return [
  //     h('el-date-picker', {
  //       props: {
  //         clearable: true,
  //         value: value[0],
  //         valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd'
  //       },
  //       attrs: {
  //         placeholder: (data && data.startPlaceholder) || '开始日期',
  //         type: (data && data.type) || 'date'
  //       },
  //       style: {
  //         width: data && data.type && data.type === 'datetime' ? '190px' : '150px'
  //       },
  //       on: {
  //         input: valueTO => {
  //           value[0] = valueTO
  //           vm.$emit('input', value)
  //         }
  //       }
  //     }),
  //     '-',
  //     h('el-date-picker', {
  //       props: {
  //         clearable: true,
  //         value: value[1],
  //         valueFormat: data.valueFormat ? data.valueFormat : 'yyyy-MM-dd'
  //       },
  //       style: {
  //         width: data && data.type && data.type === 'datetime' ? '190px' : '150px'
  //       },
  //       attrs: {
  //         placeholder: (data && data.startPlaceholder) || '结束日期',
  //         type: (data && data.type) || 'date'
  //       },
  //       on: {
  //         input: valueTO => {
  //           value[1] = valueTO
  //           vm.$emit('input', value)
  //         }
  //       }
  //     })
  //   ]
  // })
)

export const SInputGai = Vue.component('SInputGai', {
  props: {
    item: Object,
    value: Array
  },
  render(h) {
    const vm = this
    const data = vm.item.options || {}
    const value = [].concat(vm.value || [])
    const commonInput = (Index, wordKey) =>
      h('el-input', {
        props: {
          value: value[Index]
        },
        style: {
          width: '100px'
        },
        attrs: {
          placeholder: (data && data[wordKey + 'Placeholder']) || ''
        },
        on: {
          input: valueTO => {
            value[Index] = valueTO
            vm.$emit('input', value)
          }
        }
      })
    return h('span', [commonInput(0, 'start'), '-', commonInput(1, 'end')])
  }
})

export const SInputNext = Vue.component('SInputNext', {
  props: {
    item: Object,
    value: String,
    results: Object
  },
  render(h) {
    const vm = this
    const data = vm.item.options || {}
    let value = vm.value || ''
    const res = vm.results
    const willDisabled = typeof data?.disabled === 'function' ? data?.disabled(res) : false
    return h('el-input', {
      props: {
        value: vm.value
      },
      attrs: {
        placeholder: data?.placeholder || '',
        disabled: willDisabled
      },
      on: {
        input: valueTO => {
          value = valueTO
          vm.$emit('input', value)
        }
      }
    })
  }
})

export const SSwitch = Vue.component(
  'SSwitch',
  basicsRender('el-switch', vm => {
    const on = getOptionsAttr(vm, 'on')
    if (on.input) {
      if (!basics.isNull(vm.value)) {
        on.input(String(vm.value))
      }
    }
    return {
      props: {
        value: basics.isNull(vm.value) ? '' : String(vm.value)
      },
      on: {
        input: () => {},
        change: newValue => {
          vm.$emit('input', newValue)
        }
      }
    }
  })
)

export const Template = Vue.component('Template', {
  props: {
    render: {
      type: Function,
      default: h => {}
    },
    results: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  // 标签生成
  render: function(h) {
    return this.render(h, this.results)
  }
})

export const RenderCustom = Vue.component('RenderCustom', {
  props: {
    value: {},
    render: {
      type: Function,
      default: h => {}
    },
    results: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  // 标签生成
  render: function(h) {
    return this.render(h, this.results, this)
  }
})

export const SCascader = Vue.component(
  'SCascader',
  basicsRender('el-cascader', vm => {
    const list = vm.item.list || []
    const options = []
    list.map(item => {
      if (!item.parentId) {
        const obj = {
          label: item.cityName,
          value: item.cityName,
          parentId: item.parentId,
          id: item.id,
          children: []
        }
        options.push(obj)
      }
    })
    options.map(item => {
      list.map(c => {
        if (item.id === c.parentId) {
          const obj = {
            label: c.cityName,
            value: c.cityName,
            parentId: c.parentId,
            id: c.id
          }
          item.children.push(obj)
        }
      })
    })
    return {
      props: Object.assign(
        {},
        {
          value: vm.value,
          options: options,
          props: {
            checkStrictly: true,
            multiple: true,
            emitPath: false
          }
        },
        cascaderConfig
      )
    }
  })
)
export const STimeSelectRangeMultiple = Vue.component(
  'STimeSelectRange',
  basicsRender('span', undefined, (vm, h, list) => {
    const data = vm.item.options || {}
    const value = [].concat(vm.value || [])
    // console.info(value,'valuevaluevalue')
    return [
      h('el-time-select', {
        props: {
          clearable: true,
          value: value[0],
          valueFormat: data.valueFormat ? data.valueFormat : 'HH:mm',
          pickerOptions: data.startPickerOptions || {}
        },
        attrs: {
          placeholder: (data && data.startPlaceholder) || '开始时间'
        },
        style: {
          width: '150px'
        },
        on: {
          input: valueTO => {
            value[0] = valueTO
            vm.$emit('input', value)
          }
        }
      }),
      '-',
      h('el-time-select', {
        props: {
          clearable: true,
          value: value[1],
          valueFormat: data.valueFormat ? data.valueFormat : 'HH:mm',
          pickerOptions: data.endPickerOptions || {}
        },
        style: {
          width: '150px'
        },
        attrs: {
          placeholder: (data && data.startPlaceholder) || '结束时间'
        },
        on: {
          input: valueTO => {
            value[1] = valueTO
            vm.$emit('input', value)
          }
        }
      })
    ]
  })
)
