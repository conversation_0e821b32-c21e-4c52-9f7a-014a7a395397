<template>
 <div v-if="text.isShow">
   <el-dialog
     :title="text.type"
     :visible.sync="text.isShow"
     width="30%"
     :before-close="close">
     <span style="font-size: 16px;line-height: 30px">{{text.msg}}</span>
     <span slot="footer" class="dialog-footer">
      <el-button @click="close()">取 消</el-button>
      <el-button type="primary" @click="onOk()">确 定</el-button>
    </span>
   </el-dialog>
 </div>
</template>

<script>
  export default {
    name: "confirm",
    data(){
      return{
        text:{
          isShow:false,
          type:'操作提示',
          msg:'确定删除此条信息？',
          btn:{
            okText:'确定',
            noText:'取消'
          },
        }
      }
    },
    methods:{
      close(){
        console.log('关闭');
      },
      onOk(){
        console.log('确定')
      }
    }
  }
</script>

<style scoped>

</style>
