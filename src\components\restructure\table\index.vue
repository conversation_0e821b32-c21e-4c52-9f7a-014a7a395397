<template>
  <div class="table-base">
    <el-checkbox-group
      v-if="changeTab"
      v-model="chooseColumns"
      style="margin-bottom: 20px; display: flex; flex-flow: wrap; align-items: center"
    >
      <el-checkbox v-for="item in column" :key="item.key || item.type" :label="item.key || item.type">
        {{ item.title || '操作' }}
      </el-checkbox>
      <el-button type="primary" size="mini" style="margin-left: 30px" @click="ensureColumns">保存</el-button>
    </el-checkbox-group>
    <div v-if="tableTitle" class="tab-head">
      <span class="title">{{ tableTitle }}</span>
    </div>
    <el-table
      :key="$store.state.ref.tableShouldUpdate"
      v-loading="tableLoading"
      :data="data"
      size="medium"
      border
      :highlight-current-row="type === tableItemType.tabType.radio"
      :height="tableStyle.height"
      :max-height="maxHeight"
      style="width: 100%"
      :cell-class-name="rowStyle"
      :span-method="spanMethod"
      :tree-props="treeProps"
      :row-key="rowKey"
      :lazy="lazy"
      :load="load"
      @select="(...e) => selectMethodsType('select')(...e)"
      @select-all="(...e) => selectMethodsType('selectAll')(...e)"
      @current-change="currentChange"
      @selection-change="changeFun"
      @sort-change="sortChange"
      @row-click="$emit('rowClick',$event)"
    >
      <el-table-column
        v-if="type === tableItemType.tabType.selection"
        type="selection"
        :selectable="selectableMethods"
        align="center"
        width="55"
      />
      <el-table-column
        v-if="type === tableItemType.tabType.index"
        type="index"
        align="center"
        label="序号"
        width="50"
      />
      <template v-for="(i, index) in orderColumn">
        <template v-if="!i.slot">
          <el-table-column
            v-if="i.tableColumnType"
            :key="i.key || index"
            :type="i.tableColumnType"
            align="center"
            :show-overflow-tooltip="basics.isNull(i.tooltip) ? true : i.tooltip"
            :render-header="basics.isNull(i.renderHeader) ? null : i.renderHeader"
            :width="i.width || '50px'"
            :fixed="i.fixed ? i.fixed : false"
            :sortable="i.sortable ? i.sortable : false"
            :prop="i.key || null"
          />

          <el-table-column
            v-else
            :key="i.key || index"
            :fixed="i.fixed ? i.fixed : false"
            :label="getItemType(i, tableItemType.active) ? '操作' : i.title"
            align="center"
            :show-overflow-tooltip="basics.isNull(i.tooltip) ? true : i.tooltip"
            :render-header="
              basics.isNull(i.renderHeader)
                ? getItemType(i, tableItemType.expand)
                  ? expandHeader
                  : null
                : i.renderHeader
            "
            :width="getItemType(i, tableItemType.expand) && expand ? i.expandWidth || 300 : secondaryHeadingWidthSet(i)"
            :sortable="i.sortable ? i.sortable : false"
            :prop="i.key || null"
          >
            <template v-for="(childrenItem, childrenIndex) in i.children">
              <el-table-column
                v-if="i.children"
                :key="childrenItem.key || childrenIndex"
                align="center"
                :label="childrenItem.title"
                :show-overflow-tooltip="basics.isNull(childrenItem.tooltip) ? true : childrenItem.tooltip"
                :render-header="basics.isNull(childrenItem.renderHeader) ? null : childrenItem.renderHeader"
                :width="childrenItem.width"
                :fixed="childrenItem.fixed ? childrenItem.fixed : false"
                :sortable="childrenItem.sortable ? childrenItem.sortable : false"
                :prop="childrenItem.key || null"
              >
                <template slot-scope="scope">
                  <template v-if="!childrenItem.render">
                    <span>{{ getLabelText(scope.row, childrenItem.key) }}</span>
                  </template>
                  <Template v-else :render="childrenItem.render" :results="{data: scope, column: childrenItem}" />
                </template>
              </el-table-column>
            </template>

            <template v-if="!i.children" slot-scope="scope">
              <div v-if="getItemType(i, tableItemType.active)">
                <actionButton
                  :active-type="i.activeType"
                  :params="scope.row"
                  :index="scope.$index"
                  @dialog="dialog"
                  @deleteActive="deleteActive(scope.row, scope.$index)"
                  @getById="getById(scope.row)"
                />
              </div>
              <ActionExpand v-else-if="getItemType(i, tableItemType.expand)" :expand.sync="expand">
                <template v-if="expand" v-slot:default="slotProps">
                  <div v-if="i.hidden && i.hidden(scope.row)">-</div>
                  <Template v-else-if="i.render" :render="i.render" :results="{data: scope, column: i}" />
                  <actionButton
                    v-else
                    :active-type="i.activeType"
                    :params="{...scope.row, slotProps}"
                    :index="scope.$index"
                    @dialog="dialog"
                    @deleteActive="deleteActive(scope.row, scope.$index)"
                    @getById="getById(scope.row)"
                  />
                </template>
              </ActionExpand>
              <div v-else-if="i.tableView">
                <span>
                  <tableView :column="i" :data="scope.row" />
                </span>
              </div>
              <template v-else-if="!i.render">
                <span v-if="i.exhibitionMode && i.exhibitionMode === exhibitionMode.text">{{
                  exhibitionModeText(i.list, scope.row[i.key])
                }}</span>
                <span>{{ getLabelText(scope.row, i.key, i.standBy) }}</span>
              </template>
              <Template v-else :render="i.render" :results="{data: scope, column: i}" />
            </template>
          </el-table-column>
        </template>
        <template v-if="i.slot">
          <slot :name="i.slot" :column="i" />
        </template>
      </template>
    </el-table>
    <div class="pagination-container-footer">
      <slot name="tableFooter" :table-data="data">
        <div />
      </slot>
      <pagination
        v-if="paginationState"
        :fy="fy"
        :total="total"
        :page-size.sync="tableInfo.pageSize"
        :current.sync="tableInfo.pageNumber"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import pagination from '../pagination/index'
import { Template, getDefaultListItem } from '../form/components/render/render'
import { keyWord, picturePath, exhibitionMode, tableItemType, deleteConfirm } from '@/config/sysConfig'
import tableView from './components/tableView'
import actionButton from './components/button/index'
import ActionExpand from './components/expand/expand.vue'
import { mapRecursion, copy } from '@/config/basicsMethods'
import debounce from 'lodash/debounce'
export default {
  components: {
    pagination,
    Template,
    actionButton,
    tableView,
    ActionExpand
  },
  props: {
    fy: {
      type: Array,
      default: null
    },
    defaultTotal: {
      type: Number,
      default: 0
    },
    spanMethod: {
      type: Function,
      default: () => {
        return [1, 1]
      }
    },
    currentTab: {
      type: String,
      default: ''
    },
    tableTitle: {
      type: String,
      default: ''
    },
    changeTab: {
      type: Boolean,
      default: false
    },
    column: {
      type: Array,
      required: true
    },
    query: {
      type: Object,
      default: () => {}
    },
    rowStyle: {
      type: Function,
      default: () => {}
    },
    request: {
      type: Object,
      default: () => {
        return {
          getListUrl: () => Promise.resolve([]),
          deleteUrl: () => Promise.resolve()
        }
      }
    },
    type: {
      type: String,
      default: ''
    },
    paginationState: {
      type: Boolean,
      default: true
    },
    maxHeight: {
      type: String,
      default: 'auto'
    },
    rowData: {
      type: Array,
      default: () => []
    },
    selectMethods: {
      type: Object,
      default: () => ({})
    },
    tableStyle: {
      type: Object,
      default: () => ({})
    },
    treeProps: {
      type: Object,
      default: () => ({})
    },
    rowKey: {
      type: String,
      default: ''
    },
    lazy: {
      type: Boolean,
      default: false
    },
    load: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      oldVal: null,
      tableLoading: false,
      copyColumns: [],
      chooseColumns: [],
      tableItemType: tableItemType,
      exhibitionMode: exhibitionMode,
      keyWord: keyWord,
      total: 0,
      tableInfo: {
        pageSize: 10,
        pageNumber: 1
      },
      data: [],
      defaultData: [],
      orderColumn: [],
      getLabelText: mapRecursion,
      firstEntry: false /* 是否第一次进入 兼容以前的模式*/,
      expand: false, // 是否展开操作栏
      expandHeader: h => {
        return (
          <el-button
            type='text'
            onClick={() => {
              this.expand = !this.expand
            }}>
            {!this.expand ? <i class='el-icon-back'></i> : null}
            操作
            {this.expand ? <i class='el-icon-right'></i> : null}
          </el-button>
        )
      }
    }
  },
  watch: {
    fy: {
      handler(val) {
        console.info(val)
        if (val && this.oldVal != val) {
          this.oldVal = val
          clearTimeout(this.steChange)
          this.steChange = setTimeout(() => {
            this.tableInfo.pageSize = val[0]
            this.tableInfo.pageNumber = 1
          }, 500)
        }
      }
    },
    query(to) {
      this.tableInfo = this.$options.data().tableInfo
      this.getTableList()
    },
    column(to) {
      this.orderColumn = this.handlerOrderColumn()
    },
    rowData(to) {
      this.tableInfo = this.$options.data().tableInfo
      this.getTableList()
    },
    defaultTotal: {
      handler(val) {
        this.handleSizeChange(val)
      }, deep: true
    }
  },
  activated() {
    if (this.firstEntry) {
      this.firstEntry = false
      return
    }
    this.getTableList()
  },
  mounted() {
    this.firstEntry = true
    this.getTableList()
    this.defaultTotal != 0 && this.handleSizeChange(this.defaultTotal)
  },
  async created() {
    if (this.changeTab) {
      this.chooseColumns = []
      if (localStorage.getItem(this.currentTab)) {
        this.orderColumn = JSON.parse(localStorage.getItem(this.currentTab))
        for (const i in this.orderColumn) {
          this.chooseColumns.push(this.orderColumn[i]['key'] || this.orderColumn[i]['type'])
        }
      } else {
        this.orderColumn = JSON.parse(JSON.stringify(this.column))
        for (const i in this.orderColumn) {
          this.chooseColumns.push(this.orderColumn[i]['key'] || this.orderColumn[i]['type'])
        }
      }
      this.ensureColumns()
    }
    this.orderColumn = await this.handlerOrderColumn()
  },
  methods: {
    ensureColumns() {
      const arr = []
      for (let j = 0; j < this.column.length; j++) {
        for (const i in this.chooseColumns) {
          if (this.chooseColumns[i] == this.column[j].key || this.chooseColumns[i] == this.column[j].type) {
            arr.push(this.column[j])
          }
        }
      }
      this.orderColumn = arr
      localStorage.setItem(this.currentTab, JSON.stringify(this.orderColumn))
    },
    picturePath(path) {
      return picturePath(path)
    },
    handleSizeChange(val) {
      /* 条数切换触发*/
      this.tableInfo.pageSize = val
      this.tableInfo.pageNumber = 1
      this.getTableList()
    },
    handleCurrentChange(val) {
      /* 页数切换触发*/
      this.tableInfo.pageNumber = val
      this.getTableList()
    },
    exhibitionModeText(list, index) {
      const filter = list.filter(item => {
        if (item.value === String(index)) {
          return item
        }
      })
      return filter[0].label
    },
    getItemType(item, type) {
      if (item.type && item.type === type) return true
      return false
    },
    secondaryHeadingWidthSet(val) {
      // 二级标题动态子标题固定宽度问题
      if (val.secondaryHeading) {
        return val.children.length * 80
      } else {
        return val.width || ''
      }
    },
    getTableList(options) {
      const { isPushData = false } = options || {}
      this.tableLoading = true
      if (!this.basics.isArrNull(this.rowData)) {
        this.data = this.rowData
        this.total = this.data.length
        this.defaultData = this.data
        return Promise.resolve(false)
      }
      return this.request
        .getListUrl({ ...{}, ...this.tableInfo, ...this.query })
        .then(msg => {
          let res = copy(msg)
          if (msg.data && (msg.data.rows || msg.data.rows === null)) {
            res = {
              code: 0,
              data: msg.data.rows || [],
              message: null,
              result: 'success',
              totalCount: msg.data.total,
              pageId: null
            }
          }
          const { data = [] } = res
          if (isPushData) {
            this.data.push(...data)
          } else {
            this.data = data
          }
          this.defaultData = this.data
          if (this.paginationState !== false) {
            this.total = this.basics.isNull(res.totalCount) ? data.length : res.totalCount
          }
          this.tableLoading = false
          this.$emit('load', res)
        })
        .catch(e => {
          this.tableLoading = false
        })
    },
    deleteActive(item, index) {
      deleteConfirm(this).then(msg => {
        this.request
          .deleteUrl(item)
          .then(msg => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.data.splice(index, 1)
            this.getTableList()
          })
          .catch(err => {
            if (err?.msg === 'disabled') return
            this.$message({
              type: 'error',
              message: err.msg || '删除失败!'
            })
          })
      })
    },
    getById(item) {
      const getByIdUrl = this.request.getByIdHttp
      const http = this.basics.isNull(getByIdUrl)
        ? () => Promise.resolve(item)
        : this.basics.isObj(getByIdUrl)
          ? () => Promise.resolve(getByIdUrl)
          : getByIdUrl
      this.$emit('getByIdCallback', () => {
        return new Promise(resolve => {
          http(
            {
              id: item.id
            },
            item
          ).then(msg => {
            resolve(msg)
          })
        })
      })
    },
    dialog(data) {
      this.$emit('dialog', data)
    },
    changeFun(selection) {
      try {
        this.selectMethodsType('selectionChange')(selection)
      } catch (e) {
        console.log(e)
      }
      this.$emit('selectionChange', selection)
    },
    selectMethodsType(type) {
      if (this.selectMethods[type]) {
        return this.selectMethods[type]
      } else {
        return function() {}
      }
    },
    sortChange({ column, prop, order }) {
      // 自定义排序 首行不参与排序 按需扩展
      let tableData = JSON.parse(JSON.stringify(this.data))
      const num1 = tableData.shift()
      tableData.sort((x, y) => {
        if (order === 'ascending') {
          return Number(x[prop]) - Number(y[prop])
        }
        if (order === 'descending') {
          return Number(y[prop]) - Number(x[prop])
        }
      })
      if (!order) {
        tableData = this.defaultData && this.defaultData.length > 1 && this.defaultData.slice(1)
      }
      this.data = [num1, ...tableData]
    },
    selectableMethods() {
      if (this.selectMethods && this.selectMethods['selectable']) {
        return this.selectMethods['selectable'].apply(undefined, arguments)
      }
      return true
    },
    currentChange(value) {
      try {
        this.selectMethodsType('currentChange')(value)
      } catch (e) {
        console.log(e)
      }
      this.$emit('currentChange', value)
    },
    handlerOrderColumn() {
      let column = ''
      if (this.changeTab) {
        column = copy(this.orderColumn)
      } else {
        column = copy(this.column)
      }
      column.forEach(async(item, index) => {
        if (this.basics.isNull(item.list) && item.tableView && item.tableView === tableItemType.tableView.requestText) {
          if (this.basics.isNull(item.list)) this.$set(item, 'list', [])
          await item.requestList().then(msg => {
            getDefaultListItem(msg.data, item.listFormat, list => {
              item.list.push(list)
            })
          })
        } else if (
          !this.basics.isNull(item.list) &&
          this.basics.isArray(item.list) &&
          !this.basics.isArrNull(item.list)
        ) {
          getDefaultListItem(item.list, item.listFormat, list => {
            item.list[list.index] = list
          })
        }
      })
      return column
    },
    debounceChangeTableKey: debounce(function() {
      this.tableKey++
    }, 400)
  }
}
</script>
<style scoped>
.table-base ::v-deep .el-checkbox {
  width: 150px;
  height: 34px;
  line-height: 30px;
  display: flex;
  align-items: center;
}
.table-base ::v-deep .el-checkbox .el-checkbox__label {
  font-size: 14px;
  white-space: normal;
}
.pagination-container-footer{
  display: flex;
  align-items:center;
  justify-content: space-between;
  flex-wrap: nowrap;
}
</style>
