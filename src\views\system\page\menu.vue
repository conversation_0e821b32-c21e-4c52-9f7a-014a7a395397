<template>
  <div>
    <div style="display: flex;align-items: center;">
      <el-input v-model="parameterObj.title" style="margin-right: 20px;width: 300px;" placeholder="请输入菜单名" clearable />
      <div class="row-bg">
        <el-button type="primary" plain icon="el-icon-search" @click="getData()">查询</el-button>
        <el-button type="primary" plain @click="refresh()">重置</el-button>
        <el-button v-permission="'add'" type="primary" plain icon="el-icon-circle-plus-outline" @click="linkDetails('add')">新增</el-button>
      </div>
    </div>
    <section>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        border
        :cell-style="cellStyle"
        style="width: 100%;margin: 30px 0;"
      >
        <el-table-column prop="title" label="菜单名" align="left" class-name="title-class" />
        <el-table-column prop="path" label="地址" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.path || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="icon图标">
          <template slot-scope="scope">
            <i v-if="scope.row.icon" :class="scope.row.icon" />
            <i v-else>--</i>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" />
        <el-table-column label="操作" width="270">
          <template slot-scope="scope">
            <el-button v-permission="'edit'" type="warning" size="mini" plain @click="linkDetails('edit',scope.row)">编辑</el-button>
            <el-button v-permission="'delete'" type="danger" size="mini" plain @click="handleClose(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
    <!-- 新增菜单/菜单详情 -->
    <section>
      <el-dialog
        :title="isType === 'add' ? '新增菜单' :'菜单详情'"
        width="500px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
        :close-on-click-modal="false"
        :before-close="onHide"
      >
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
          <el-form-item label="菜单名：" prop="title">
            <el-input v-model="ruleForm.title" autocomplete="off" placeholder="请输入菜单名称" style="width:300px;" />
          </el-form-item>
          <el-form-item label="地址：" prop="path">
            <el-input v-model="ruleForm.path" style="width:300px;" placeholder="请输入地址" autocomplete="off" />
          </el-form-item>
          <el-form-item label="父级菜单：">
            <el-cascader
              v-model="ruleForm.parentId"
              :props="{ checkStrictly: true,value:'id',label:'title' }"
              :options="tableData"
              :disabled="isType !== 'add'"
              clearable
              placeholder="自为父级"
              style="width: 300px;"
            />
          </el-form-item>
          <el-form-item label="icon：" prop="icon">
            <el-input v-model="ruleForm.icon" style="width:300px;" placeholder="请输入icon" autocomplete="off" />
          </el-form-item>
          <el-form-item label="排序：" prop="sort">
            <el-input
              v-model="ruleForm.sort"
              autocomplete="off"
              style="width:300px;"
              placeholder="请输入排序内容,数字越大越靠前"
            />
          </el-form-item>
          <el-form-item class="dialog-footer">
            <el-button @click="roleUpdated('0','ruleForm')">取 消</el-button>
            <el-button
              type="primary"
              :loading="btn_disabled"
              :disabled="btn_disabled"
              @click="roleUpdated('1','ruleForm')"
            >确 定
            </el-button>
          </el-form-item>
        </el-form>

      </el-dialog>
    </section>
  </div>
</template>

<script>
import {
  add_menu,
  put_menu,
  get_menu_list,
  get_menu_edit,
  del_menu
} from '@/api/system'

export default {
  data() {
    return {
      total: 0,
      pageSize: 10,
      currentPage: 1,
      parameterObj: {
        title: ''
      },
      isType: '',
      menuId: '',
      tableData: [],
      dialogFormVisible: false,
      allMenu: [],
      ruleForm: {
        title: '',
        icon: '',
        path: '',
        parentId: '',
        sort: ''
      },
      rules: {
        title: [
          { required: true, message: '菜单名不能为空', trigger: 'blur' },
          { min: 2, max: 10, message: '菜单名必须是2-10位字符串' }
        ],
        path: [
          { required: true, message: '地址不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      },
      loadingFlag: false,
      btn_disabled: false,
      DataLoading: false
    }
  },
  created() {
    this.getData()
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 指定列号
        return 'text-align:left;font-weight:bold;'
      } else {
        return ''
      }
    },
    getData(type) {
      // 获取菜单列表
      if (type === 'new') {
        this.loadingFlag = true
        this.currentPage = 1
      }
      this.DataLoading = true
      get_menu_list({
        title: this.parameterObj.title,
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.loadingFlag = false
        this.DataLoading = false
        if (res.code === 200) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.$store.dispatch('user/getUserInfo')
        this.DataLoading = false
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },

    // 【提交】 新增 | 修改
    roleUpdated(isShow, formName) {
      const thar = this
      if (isShow === '0') { // 取消
        thar.dialogFormVisible = false
        thar.delForm()
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            const obj = thar.ruleForm
            this.btn_disabled = true
            if (thar.isType === 'add') { // 新增
              const parentId = thar.ruleForm.parentId ? thar.ruleForm.parentId.slice(-1) : ''
              obj.parentId = parentId ? parentId.join(',') : ''
              add_menu(obj).then(res => {
                this.btn_disabled = false
                if (res.code == 200) {
                  thar.delForm()
                  thar.getData()
                  this.$message({
                    message: '新增菜单成功',
                    type: 'success'
                  })
                  thar.dialogFormVisible = false
                } else {
                  this.$message.error(res.message)
                }
              })
            } else {
              obj.id = thar.menuId
              put_menu(obj).then(res => {
                this.btn_disabled = false
                if (res.code == 200) {
                  thar.delForm()
                  thar.getData()
                  this.$message({
                    message: '修改菜单成功',
                    type: 'success'
                  })
                  thar.dialogFormVisible = false
                } else {
                  this.$message.error(res.message)
                }
              })
            }
          }
        })
      }
    },
    // 【打开】 新增 | 修改
    linkDetails(type, item) {
      const thar = this
      thar.isType = type
      thar.dialogFormVisible = true
      if (thar.isType === 'edit') {
        // 获取详情
        thar.menuId = item.id
        get_menu_edit({
          id: item.id
        }).then(res => {
          if (res.code == 200) {
            thar.ruleForm.title = res.data.title
            thar.ruleForm.icon = res.data.icon
            thar.ruleForm.parentId = res.data.parentId
            thar.ruleForm.path = res.data.path
            thar.ruleForm.sort = res.data.sort
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },

    // 删除菜单
    handleClose(item) {
      this.$confirm('是否确认删除？', '操作提示', { closeOnClickModal: false }).then(() => {
        del_menu({
          id: item.id
        }).then(res => {
          if (res.code == 200) {
            this.$message({
              message: '删除菜单成功',
              type: 'success'
            })
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        // 取消
      })
    },
    delForm() {
      // 清空新增时所填内容
      this.$refs.ruleForm.clearValidate() // 移除校验结果
      this.ruleForm = this.$utils.removeObj(this.ruleForm)
      this.menuId = ''
    },
    delParameter() {
      // 清空查询参数内容
      this.parameterObj = this.$utils.removeObj(this.parameterObj)
    },
    onHide() {
      this.dialogFormVisible = false
      this.delForm()
    },
    refresh() {
      // 刷新
      this.currentPage = 1
      this.delParameter()
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
  .titleName{
    font-weight: bold;
  }
</style>
