<template>
  <div class="role">
    <page :request="request" :list="list">
      <template #searchContainer>
        <el-button size="small" type="primary" @click="handleCreate(null)">添加</el-button>
      </template>
    </page>
    <ArgumentForm v-model="argumentFormModel" :form-id="argumentFormId" @close="handleClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { getSystemList } from '@/api/system'
import { tableItemType, formItemType } from '@/config/sysConfig'
import ArgumentForm from '../components/ArgumentForm.vue'

const PARAMS_STATUS_MAP = {
  1: '启用',
  0: '禁用'
}

export default {
  components: {
    page,
    ArgumentForm
  },
  data() {
    return {
      argumentFormId: null,
      argumentFormModel: false,
      request: {
        getListUrl: async data => {
          const res = await getSystemList(data)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      },
      list: [
        // 搜索
        {
          key: 'paramName',
          type: formItemType.input,
          tableHidden: true,
          formHidden: true,
          search: true,
          options: {
            placeholder: '请输入参数名称或ID'
          }
        },
        // 表格
        {
          key: 'id',
          title: '参数ID',
          width: 100
        },
        {
          key: 'paramName',
          title: '参数名称'
        },
        {
          key: 'paramDesc',
          title: '参数说明'
        },
        {
          key: 'paramValue',
          title: '类型配置'
        },
        {
          key: 'status',
          title: '状态',
          tableView: tableItemType.tableView.text,
          list: Object.keys(PARAMS_STATUS_MAP).sort((a, b) => b - a).map(key => {
            return {
              label: PARAMS_STATUS_MAP[key],
              value: key
            }
          }),
          width: 100
        },
        {
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 120,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: (_$index, _item, params) => {
                this.handleCreate(params.id)
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleCreate(id) {
      this.argumentFormId = id
      this.argumentFormModel = true
    },
    handleClose() {
      this.argumentFormId = null
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>
