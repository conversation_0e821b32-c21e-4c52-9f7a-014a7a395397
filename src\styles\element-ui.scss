// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
.dialog_g {
  margin-top: 10vh !important;
  .el-dialog__body{
    padding-top: 0;
  }
}

.position_sticky {
  .el-table__header-wrapper {
    position: sticky;
    top: 84px;
    z-index: 4;
  }
  .el-card,
  .el-table,
  .table-base {
    overflow: visible !important;
  }
}
.app-main:has(.position_sticky){
  overflow: visible !important;
}
