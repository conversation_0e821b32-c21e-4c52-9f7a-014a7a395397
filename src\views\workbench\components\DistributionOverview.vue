<template>
  <div>
    <Page :request="request" :list="list" table-title="数据概览">
      <template #searchContainer>
        <el-button v-permission="'export'" :loading="exportLoading" plain size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
      </template>
      <div slot="titleContainer" class="title-container-box">
        <el-tabs v-model="listQuery.type" @tab-click="handleClick">
          <el-tab-pane
            v-for="(item, index) in tabs"
            :key="index"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <template v-slot:tableFooter>
        <div class="table-footer">合计：总接收{{ tableData.totalRecharge || 0 }}条，已分发{{ tableData.totalConsume || 0 }}条，已取消{{ tableData.totalPush || 0 }}条</div>
      </template>
    </Page>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import { formItemType } from '@/config/sysConfig'
import moment from 'moment/moment'

export default {
  components: {
    Page
  },
  data() {
    return {
      exportLoading: false,
      tabs: [{
        value: '0',
        label: '综合'
      },
      {
        value: '1',
        label: '1类'
      },
      {
        value: '2',
        label: '2类'
      },
      {
        value: '3',
        label: '3类'
      }
      ],
      listQuery: {
        startDate: moment().startOf('month').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        type: '0'
      },
      tableData: {},
      request: {
        getListUrl: async data => {
          await new Promise((resolve, reject) => {
            setTimeout(() => {
              resolve({ totalRecharge: 0, totalConsume: 0, totalPush: 0 })
            })
          }).then(res => {
            this.tableData = res
          })
          return {
            data: {
              total: 0,
              rows: []
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '日期范围',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          pickerDay: 9999999,
          clearable: true,
          search: true,
          val: [this.listQuery.startDate, this.listQuery.endDate],
          tableHidden: true
        },
        {
          title: '总接收',
          key: 'channelCode'
        },
        {
          title: '可分发',
          key: 'channelCode1'
        },
        {
          title: '可分发率',
          key: 'apiCode'
        },
        {
          title: '待分发',
          key: 'landingPageId'
        },
        {
          title: '待分发率',
          key: 'newUser'
        },
        {
          title: '已分发',
          key: 'newUser1'
        },
        {
          title: '已分发率',
          key: 'newUser2'
        },
        {
          title: '取消分发',
          key: 'newUser3'
        },
        {
          title: '取消分发率',
          key: 'newUser4'
        },
        {
          title: '分发失败',
          key: 'newUser5'
        },
        {
          title: '分发失败率',
          key: 'newUser6'
        }
      ]
    }

  },
  methods: {
    handleClick() {
      setTimeout(() => {
        this.$store.dispatch('tableRefresh', this)
      })
    },
    handleExport() {}
  }
}
</script>

<style lang="scss" scoped>
.title-container-box{
  justify-content: flex-start;
  ::v-deep .el-tabs__nav-wrap::after{
    background-color: transparent;
  }
}
.table-footer{
  margin-top: 15px;
}
</style>
