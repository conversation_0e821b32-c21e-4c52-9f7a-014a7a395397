import { put, get, post } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'
/*
 * 登录接口
 * */
export const loginIn = obj => {
  return get('admin/login', {
    mobileNo: obj.mobileNo,
    smsCode: obj.smsCode,
    code: obj.code
  })
}

/*
 * 获取短信验证码
 * */
export const get_code = obj => {
  return get('/admin/send/sms', {
    mobileNo: obj.mobileNo
  })
}
/*
 * 退出登录接口
 * */
export const logout = obj => {
  return get('/admin/auth/logout', null)
}

/*
 * 修改密码接口
 * */
export const editPassword = obj => {
  return put('auth/password', {
    origin_password: obj.origin_password,
    new_password: obj.new_password,
    confirm_password: obj.confirm_password
  })
}

/*
 * 登录账号查询
 * */
export const user_me = () => {
  return get('admin/me', null)
}

/*
 * 购卡来源
 * */
export const user_selectChannelName = obj => {
  return get('/user/selectChannelName', obj)
}

/*
 * 用户列表
 * */
export const user_list = obj => {
  return get('/user/list', obj)
}

/*
 * 用户详情
 * */
export const user_detail = id => {
  return get('/user/' + id, null)
}

/*
 * 用户详情
 * */
export const get_user_account = obj => {
  return get(`/user/${obj.userid}/accountdetail`, obj)
}

/*
 * 用户统计
 * */
export const get_user_statistics = obj => {
  return get(`user/listUserStatistics`, obj)
}

/*
 * 购卡售后订单
 * */
export const get_viprefund_order = id => {
  return get(`/vipOrders/vipRefundOrder/${id}`, null)
}

/*
 * 获取客服列表
 * */
export const get_customer_list = obj => {
  return get(`/admin/customer/list`, obj)
}

/*
 * 获取用户会员订单
 * */
export const get_vipOrders_list = obj => {
  return get(`/vipOrders/users`, obj)
}

/**
 * 用户来访记录
 */
export const userVisitRecordPage = obj => {
  return get(`/user/visit/record/page`, obj)
}

/**
 * 用户来访记录详情
 */
export const userVisitRecordDetail = obj => {
  return get(`/user/visit/record/detail`, obj)
}

/**
 * 用户来访记录导出
 */
export const userVisitRecordExport = data => CONSTANT.publicPath + '/user/visit/record/export?' + qs.stringify(data)

/**
 * 用户来访策略查询
 */
export const userVisitStrategyGetList = obj => {
  return get(`/userVisit/strategy/getList`, obj)
}

/**
 * 用户来访策略查询
 */
export const userVisitStrategyEdit = obj => {
  return post(`/userVisit/strategy/edit`, obj)
}

/**
 * 策略编辑权限校验
 */
export const userVisitStrategyCheckPermission = obj => {
  return get(`/userVisit/strategy/checkPermission`, obj)
}

/**
 * 策略编辑权限校验v2
 */
export const userVisitStrategyCheckPermissionV2 = obj => {
  return get(`/userVisit/strategy/checkPermissionV2`, obj)
}
