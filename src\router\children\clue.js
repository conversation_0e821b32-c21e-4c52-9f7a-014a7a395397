/*
 * 线索管理子路由
 * */

const clue = [
  {
    path: '/clue',
    name: 'Clue',
    redirect: '/clue/list'
  },
  {
    path: '/clue/list',
    name: 'ClueList',
    meta: {
      title: '分发记录',
      buttons: [
        { key: 'export', name: '导出' }
      ]
    },
    component: () => import('@/views/clue/page/list.vue')
  },
  {
    path: '/clue/match-record',
    name: 'MatchRecord',
    meta: {
      title: '撞库记录',
      buttons: [
        { key: 'export', name: '导出' },
        { key: 'detail', name: '机构撞库明细' }
      ]
    },
    component: () => import('@/views/clue/page/matchRecord.vue')
  }
]

export default clue
