<template>
  <h1 class="yc-title">{{title}}</h1>
</template>

<script>
  export default {
    name: "ycTitle",
    // props:{
    //   title:{
    //     type:String,
    //     default:''
    //   }
    // },
    data(){
      return{
        title:''
      }
    },
    mounted() {
      this.title = this.$route.meta.title;
    }
  }
</script>

<style scoped lang="scss">
  .yc-title {
    font-size: 24px;
    letter-spacing: 1px;
    border-bottom: 1px solid #dfdfdf;
    padding-bottom: 12px;
  }
</style>