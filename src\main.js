import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// 加载Descriptions组件样式
import '@/components/Descriptions/descriptions.scss'
import '@/components/DescriptionsItem/descriptions-item.scss'
// 加载Empty组件样式
import '@/components/Empty/empty.scss'

import '@/styles/index.scss' // global css
import '@/config/index'

import App from './App'
import store from './store'
import router from './router'
import install from './utils/install'
Vue.use(install, router, store)

import '@/icons' // icon
import '@/permission' // permission control

import './directive'
import basics from './libs/basics'
Vue.use(basics)

import uploader from 'vue-simple-uploader'
Vue.use(uploader)
import VueWorker from 'vue-worker'
Vue.use(VueWorker)

// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

Vue.use(ElementUI)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
