<template>
  <div class="empty-state">
    <i :class="icon"></i>
    <p class="empty-text">{{ text }}</p>
    <p v-if="description" class="empty-desc">{{ description }}</p>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    icon: {
      type: String,
      default: 'el-icon-data-analysis'
    },
    text: {
      type: String,
      default: '暂无数据'
    },
    description: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #DCDFE6;
  }

  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 14px;
    color: #C0C4CC;
  }
}
</style> 