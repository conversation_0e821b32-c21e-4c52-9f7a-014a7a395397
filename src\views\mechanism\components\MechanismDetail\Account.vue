<template>
  <page ref="pageRef" :request="request" :list="list" />
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { MechanismStatusList } from '@/views/mechanism/enum'
import { getSassAccountListApi, getSassRoleListApi } from '@/api/mechanism'

export default {
  name: 'Account',
  components: { page },
  props: {
    agenciesId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      roleOptions: [],
      listQuery: {
        keywords: '',
        roleId: null,
        status: null,
        agenciesId: this.agenciesId
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getSassAccountListApi({
            ...this.listQuery,
            agenciesId: this.agenciesId
          })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '账号信息',
          key: 'keywords',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '请输入账号/手机号/姓名'
          }
        },
        {
          title: '角色',
          key: 'roleId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.roleOptions,
          val: 5,
          disabled: true,
          options: {
            placeholder: '请选择角色'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismStatusList,
          options: {
            placeholder: '请选择状态'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '用户ID',
          key: 'id'
        },
        {
          title: '登录名',
          key: 'userName'
        },
        {
          title: '用户姓名',
          key: 'nickName'
        },
        {
          title: '手机号码',
          key: 'mobileNo'
        },
        {
          title: '角色',
          key: 'roleId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.roleOptions,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        }
      ]
    }
  },
  methods: {
    // 刷新列表
    async refreshList() {
      await this.getSassRoleList()
      this.$nextTick(() => {
        this.$refs.pageRef.exposeReset()
      })
    },
    // 获取角色
    async getSassRoleList() {
      try {
        const { data } = await getSassRoleListApi()

        this.roleOptions = data
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
