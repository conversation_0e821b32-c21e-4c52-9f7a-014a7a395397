import { submission } from '@/config/sysConfig'

export default {
  state: {
    submitType: submission.insert,
    pictureInfo: {
      state: false,
      path: ''
    }
  },
  mutations: {
    submission(state, str) {
      state.submitType = str
    },
    disposePictureInfo(state, path) {
      state.pictureInfo = {
        state: true,
        path: path
      }
    }
  },
  actions: {
    addSubmission({ commit, state }) { // 添加
      commit('submission', submission.insert)
    },
    editSubmission({ commit, state }) { // 修改
      commit('submission', submission.update)
    }
  },
  getters: {
    submissionType: state => state.submitType
  }
}
