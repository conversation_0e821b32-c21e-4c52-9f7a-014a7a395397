<template>
  <el-dialog
    title="撞库历史"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="80%"
    @open="handleOpen"
    @close="handleClose"
  >
    <Page ref="pageRef" :request="request" :list="list" />
  </el-dialog>
</template>

<script>
import Page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { CollisionStatusList } from '../enum'
import { getCollisionRecordListApi } from '@/api/clue'
import { getMechanismListApi } from '@/api/mechanism'

export default {
  name: 'MatchHistory',
  components: { Page },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 弹窗显示|隐藏
    value: Boolean,
    tableId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      agenciesList: [],
      listQuery: {
        status: null,
        agenciesId: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getCollisionRecordListApi({
            ...this.listQuery,
            // 默认查机构撞库
            type: 2,
            clueId: this.tableId
          })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    list() {
      return [
        {
          title: '撞库机构',
          key: 'agenciesId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.agenciesList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '撞库状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: CollisionStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '撞库时间',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['receivingStartTime', 'receivingEndTime'],
          pickerDay: 9999999,
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '撞库记录ID',
          key: 'id'
        },
        {
          title: '撞库时间',
          key: 'createTime'
        },
        {
          title: '撞库耗时',
          key: 'times',
          render: (_h, params) => {
            return <span>{params.data.row.times}s</span>
          }
        },
        {
          title: '撞库状态',
          key: 'statusName'
        },
        {
          title: '失败原因',
          key: 'errorMsg'
        },
        {
          title: '撞库机构',
          key: 'agenciesName'
        }
      ]
    }
  },
  methods: {
    handleOpen() {
      this.getAgenciesList()
      this.$nextTick(() => {
        this.$refs.pageRef.exposeReset()
      })
    },
    // 获取机构list
    async getAgenciesList() {
      const res = await getMechanismListApi({
        pageNum: 1,
        pageSize: 999999999
      })

      this.agenciesList = res.data.records.map(item => {
        return {
          id: item.id,
          name: item.agencyName
        }
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
