<template>
  <div>
    <!-- <getByIdForm
      v-if="getByIdFormLoad"
      ref="formNode"
      :data="getByIdData"
      :form-item-list="list"
      label-width="150px"
      :display-state="true"
    /> -->
    <el-form ref="formNode" :model="getByIdData" :rules="addRules" label-width="120px" class="demo-ruleForm">
      <el-form-item label="角色名称" prop="name" :rules="addRules.common">
        <el-input v-model="getByIdData.name" maxlength="25" type="text" />
      </el-form-item>
      <el-form-item label="角色CODE" prop="code" :rules="addRules.common">
        <el-input v-model="getByIdData.code" type="text" />
      </el-form-item>
      <el-form-item>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="sure">确定</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
// import getByIdForm from '@/components/restructure/form/components/getById'/
import { get_role_by_id, update_role, add_role } from '@/api/system'
import { commonNullCharacterReg } from '@/libs/validate'
export default {
  props: {
    roleId: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    getByIdFormLoad: false,
    getByIdData: {
      id: '',
      code: '',
      name: ''
    },
    addRules: {
      common: [{ required: true, message: '此项不能为空', trigger: 'blur' },
        { required: true, message: '此项不能为空', trigger: 'change' },
        { validator: commonNullCharacterReg('此项不能为空'), trigger: 'blur' }]
    }
  }),
  mounted() {
    if (this.type === 'edit') {
      this.$store.dispatch('editSubmission')
      // 做详情的请求
      this._bydRoleDetail()
    } else {
      this.getByIdFormLoad = true
    }
  },
  methods: {
    _bydRoleDetail() {
      get_role_by_id(this.roleId).then(res => {
        this.getByIdFormLoad = true
        for (const key in res.data) {
          if (this.getByIdData.hasOwnProperty(key)) {
            this.getByIdData[key] = res.data[key]
          }
        }
      })
    },
    close() {
      this.$emit('close')
    },
    sure() {
      this.$refs.formNode.validate(async valid => {
        if (valid) {
          if (this.type === 'add') {
            const postData = { ...this.getByIdData }
            delete postData.id
            add_role(postData).then((res) => {
              if (res.code == 200) {
                this.$message.success('添加成功')
                this.$emit('success')
                this.close()
              }
            })
          } else if (this.type === 'edit') {
            const postData = { ...this.getByIdData }
            update_role(postData).then(res => {
              if (res.code == 200) {
                this.$message.success('编辑成功')
                this.$emit('success')
                this.close()
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="" scoped>

</style>
