<template>
  <div>
    <page :list="list" :request="request" />
  </div>
</template>

<script>
import { page } from '@/components/restructure/page/export'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { caseAndCase } from '@/config/basicsMethods'

export default {
  name: 'Index',
  components: {
    page
  },
  props: {
    column: {
      type: Function,
      default: () => {}
    },
    dataRequest: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      request: {
        getListUrl: data =>
          this.dataRequest({ ...data, ...this.$route.query, ...this.data })
      },
      list: [],
      data: {
        columnName: ''
      }
    }
  },
  mounted() {
    this.column(this.$route.query).then(msg => {
      let filter = []
      filter = msg.map(item => {
        return {
          title: item.columnComment,
          key: item.columnName,
          suture: JSON.stringify({
            title: item.columnComment,
            key: item.columnName,
            type:
              item.dataType === 'timestamp'
                ? formItemType.datePickerDaterangeGai
                : undefined,
            MQtype: item.dataType
          }),
          width: item.columnName === 'id' ? 80 : '',
          tableView:
            item.dataType === 'timestamp'
              ? tableItemType.tableView.date
              : undefined
        }
      })
      this.list = filter.concat([
        {
          title: '筛选字段',
          key: 'filter',
          tableHidden: true,
          type: formItemType.select,
          search: true,
          list: filter,
          listFormat: {
            value: 'suture',
            label: 'title'
          },
          options: {
            on: () => {
              return {
                change: value => {
                  const item = this.list[this.list.length - 1]
                  item.search = true
                  if (this.basics.isNull(value)) {
                    item.search = false
                  } else {
                    const { title, type, key, MQtype } = JSON.parse(value)
                    this.data = {
                      columnName: caseAndCase(key),
                      dataType: MQtype
                    }
                    this.$set(this.list, this.list.length - 1, {
                      search: true,
                      title,
                      key: key,
                      tableHidden: true,
                      childKey: type ? ['beginTime', 'endTime'] : undefined,
                      type: type || formItemType.input,
                      options: type
                        ? {
                          valueFormat: 'timestamp'
                        }
                        : undefined
                    })
                  }
                }
              }
            }
          }
        },
        {
          title: '隐藏搜索',
          key: 'test',
          search: false,
          tableHidden: true,
          type: formItemType.input
        }
      ])
    })
  }
}
</script>
