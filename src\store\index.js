import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import ref from './modules/ref'
import submission from './modules/submission'
import tagsView from './modules/tagsView'
import localStorage from './modules/localStorage'
Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    ref,
    submission,
    tagsView,
    localStorage
  },
  getters
})

export default store
