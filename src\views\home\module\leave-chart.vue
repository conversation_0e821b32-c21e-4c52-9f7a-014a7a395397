<template>
  <div>
    <div class="item-title-wrap">
      <h1 class="yc-home-sub-title">留资上架消耗概览</h1>
      <div style="display: inline-flex;align-items: center;">
        <p style="margin-right: 40px;">
          <el-button type="text" :class="{'day-btn-true':dayTime === 7}" class="day-btn" @click="dayChange(7)">7天</el-button>
          <el-button type="text" :class="{'day-btn-true':dayTime === 30}" class="day-btn" @click="dayChange(30)">30天</el-button>
          <el-button type="text" :class="{'day-btn-true':dayTime === 90}" class="day-btn" @click="dayChange(90)">90天</el-button>
        </p>
        <el-date-picker
          v-model="timeArr"
          :picker-options="basics.pickerOptions()"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          style="width: 290px;"
          @change="timeChange()"
        />
      </div>
    </div>
    <div class="yc-home-content" style="border-bottom: none;">
      <el-row type="flex" align="center" justify="space-between">
        <el-col>
          <ul class="tagWrap">
            <li class="tag" :class="{tagTrue:tagName === 'number'}" @click="tagCik('number')">数量</li>
            <li class="tag" :class="{tagTrue:tagName === 'amount'}" @click="tagCik('amount')">金额</li>
          </ul>
        </el-col>
        <el-col style="text-align: right">
          <ul v-if="tagName === 'number'" class="colorWrap">
            <li class="clr"><span class="color" style="background: #34a0ff;" /><label class="name">留资量</label></li>
            <li class="clr"><span class="color" style="background: #36cbcb;" /><label class="name">上架量</label></li>
            <li class="clr"><span class="color" style="background: #53cb74;" /><label class="name">抢单量</label></li>
            <li class="clr"><span class="color" style="background: #975fe4;" /><label class="name">退单量</label></li>
          </ul>
          <ul v-if="tagName === 'amount'" class="colorWrap">
            <li class="clr"><span class="color" style="background: #34a0ff;" /><label class="name">抢单金额</label></li>
            <li class="clr"><span class="color" style="background: #36cbcb;" /><label class="name">退单金额</label></li>
          </ul>
        </el-col>
      </el-row>
      <div ref="lineChart" class="chart-content" />
    </div>
  </div>

</template>

<script>
import echarts from 'echarts'
import moment from 'moment'
import { overview_chat_loan } from '@/api/dataStatistics'

export default {
  name: 'leave-chart.vue',
  data() {
    return {
      option: '',
      dayTime: 7,
      timeArr: [],
      tagName: 'number',
      numberObj: '',
      amountObj: ''
    }
  },
  mounted() {
    this.getDay()
    this.getData()
  },
  methods: {
    optionChange() {
      this.option = {
        color: this.tagName === 'number' ? this.numberObj.color : this.amountObj.color,
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '6%',
          right: '8%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.tagName === 'number' ? this.numberObj.xAxis : this.amountObj.xAxis
        },
        yAxis: {
          type: 'value'
        },
        dataZoom: [
          {
            type: 'inside'
            // start: 0,
            // end: 10
          }, {
            // start: 0,
            // end: 10,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ],
        series: this.tagName === 'number' ? this.numberObj.series : this.amountObj.series
      }
    },
    tagCik(name) {
      this.tagName = name
      this.getData()
    },
    getDay() {
      const day = moment().subtract(this.dayTime - 1, 'days')
      const arr = []
      arr[0] = moment(day).format('YYYY-MM-DD')
      arr[1] = moment().format('YYYY-MM-DD')
      this.timeArr = arr
    },
    dayChange(day) {
      this.dayTime = day
      this.getDay()
      this.getData()
    },
    timeChange(day) {
      this.dayTime = 0
      this.getData()
    },
    getData() {
      const thar = this
      this.numberObj = {
        // color: ['#e69d87', '#8dc1a9','#dd6b66', '#759aa0'],
        color: ['#34a0ff', '#36cbcb', '#53cb74', '#975fe4'],
        xAxis: [],
        series: [
          {
            name: '留资量',
            type: 'line',
            smooth: true,
            key: 'loan_num',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#34a0ff'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          },
          {
            name: '上架量',
            type: 'line',
            smooth: true,
            key: 'lone_online_num',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#36cbcb'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          },
          {
            name: '抢单量',
            type: 'line',
            smooth: true,
            key: 'order_num',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#53cb74'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          },
          {
            name: '退单量',
            type: 'line',
            smooth: true,
            key: 'refund_num',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#975fe4'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          }
        ]
      }
      this.amountObj = {
        color: ['#34a0ff', '#36cbcb'],
        xAxis: [],
        series: [
          {
            name: '抢单金额',
            type: 'line',
            smooth: true,
            key: 'order_amount',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#34a0ff'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          },
          {
            name: '退单金额',
            type: 'line',
            smooth: true,
            key: 'refund_amount',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#36cbcb'
                }, {
                  offset: 1,
                  color: '#fff'
                }])
              }
            },
            data: []
          }
        ]
      }
      overview_chat_loan({
        begin_at: this.timeArr ? this.$utils.getTimer(this.timeArr[0]) : '',
        end_at: this.timeArr ? this.$utils.getTimer(this.timeArr[1]) : ''
      }).then(res => {
        if (res.code === 200) {
          const day_list = []
          if (thar.tagName === 'number') {
            /**  数量*/
            res.data.forEach(function(item) {
              day_list.push(item.day)
              thar.numberObj.series.forEach(function(ele) {
                switch (ele.key) {
                  case 'loan_num':
                    ele.data.push(item.loan_num)
                    break
                  case 'lone_online_num':
                    ele.data.push(item.lone_online_num)
                    break
                  case 'order_num':
                    ele.data.push(item.order_num)
                    break
                  case 'refund_num':
                    ele.data.push(item.refund_num)
                    break
                }
              })
            })
            thar.numberObj.xAxis = day_list
          } else {
            /**  金额 */
            res.data.forEach(function(item) {
              day_list.push(item.day)
              thar.amountObj.series.forEach(function(ele) {
                switch (ele.key) {
                  case 'order_amount':
                    ele.data.push(thar.$options.filters.changeMoney(item.order_amount))
                    break
                  case 'refund_amount':
                    ele.data.push(thar.$options.filters.changeMoney(item.refund_amount))
                    break
                }
              })
            })
            thar.amountObj.xAxis = day_list
          }
          this.optionChange()
          const _lineChart = echarts.init(thar.$refs.lineChart)
          _lineChart.setOption(thar.option, true)
          window.addEventListener('resize', function() {
            _lineChart.resize()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
    .chart-content{
        width: 100%;
        height: 400px;
    }
    .yc-home-content {
        border-bottom: 1px solid #dfdfdf;
        padding: 20px;
    }
    .yc-home-sub-title {
        font-size: 18px;
        text-align: center;
    }
    .item-title-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: auto;
        border-radius: 4px 4px 0 0;
        padding: 20px;
    }
    .tagWrap{
        display: flex;
        align-items: center;
        .tag{
            background: #FFF;
            border: 1px solid #DCDFE6;
            border-right: none;
            font-weight: 500;
            color: #606266;
            text-align: center;
            height: 35px;
            line-height: 35px;
            font-size: 13px;
            width: 97px;
            cursor: pointer;
        }
        .tag:first-child{
            border-radius:  4px 0 0 4px;
        }
        .tag:last-child{
            border-radius: 0 4px 4px 0;
            border-right: 1px solid #DCDFE6;
        }
        .tag:hover{
            color:#409EFF;
        }

        .tagTrue{
            color:#fff !important;
            border:solid 1px #409EFF;
            background-color:#409EFF ;
            border-right: none;
        }
    }
    .colorWrap{
        display: inline-flex;
        .clr{
            display: inline-flex;
            align-items: center;
            margin-right: 20px;
        }
        .clr:last-child{
            margin-right: 0;
        }
        .color{
            width: 40px;
            height: 20px;
            display: inline-block;
            margin-right: 10px;
            border-radius: 2px;
        }
        .name{
            font-size: 14px;
            color: #666;
        }
    }
    .day-btn{
        color: #666;
    }
    .day-btn-true{
        color:#409EFF ;
    }
</style>
