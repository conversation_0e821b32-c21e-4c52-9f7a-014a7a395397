/*
 * 分发策略子路由
 * */

const distribution = [
  {
    path: '/distribution',
    name: 'Distribution',
    redirect: '/distribution/overall-strategy'
  },
  {
    path: '/distribution/overall-strategy',
    name: 'OverallStrategy',
    meta: {
      title: '总策略',
      buttons: [
        { key: 'create', name: '创建策略' },
        { key: 'edit', name: '编辑' },
        { key: 'status', name: '启用|禁用' }
      ]
    },
    component: () => import('@/views/distribution/page/overallStrategy.vue')
  },
  {
    path: '/distribution/institutional-strategy',
    name: 'InstitutionalStrategy',
    meta: {
      title: '机构策略',
      buttons: [
        { key: 'create', name: '创建策略' },
        { key: 'edit', name: '编辑' },
        { key: 'status', name: '启用|禁用' },
        { key: 'remove', name: '移除' }
      ]
    },
    component: () => import('@/views/distribution/page/institutionalStrategy.vue')
  }
]

export default distribution
