<template>
  <div class="gauge-chart-box">
    <div ref="gaugeChart" style="height: 168px" />
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  name: 'gauge-chart',
  components: {},
  props: {
    data: {
      type: [Number, String],
      default: 0
    },
    groundingColor: {
      type: String,
      default: '#EEEEEE'
    },
    topColor: {
      type: String,
      default: '#358EF3'
    }
  },
  data() {
    return {
      option: {
        series: [
          {
            type: 'gauge',
            radius: '80%',
            center: ['70px', '50%'],
            splitNumber: 0, // 刻度数量
            startAngle: 90,
            endAngle: -269.9999,
            axisLine: {
              show: true,
              lineStyle: {
                width: 4,
                color: [
                  [
                    1, this.groundingColor
                  ]
                ]
              }
            },
            // 分隔线样式。
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            // 仪表盘详情，用于显示数据。
            detail: {
              show: true,
              offsetCenter: [0, 2],
              color: '#333',
              formatter: function(params) {
                return `${params.toFixed(2)}%`
              },
              textStyle: {
                fontSize: 28,
                fontFamily: 'DINB'
              }
            },
            data: [
              {
                value: this.data
              }
            ]
          },
          {
            type: 'gauge',
            radius: '82%',
            center: ['70px', '50%'],
            splitNumber: 0, // 刻度数量
            startAngle: 90,
            endAngle: -269.9999,
            axisLine: {
              show: true,
              lineStyle: {
                width: 8,
                color: []
              }
            },
            // 分隔线样式。
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            // 仪表盘详情，用于显示数据。
            detail: {
              show: false
            },
            data: [
              {
                value: this.data
              }
            ]
          }
        ]
      },
      chart: null
    }
  },
  watch: {
    data() {
      this.getData()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.chart = echarts.init(this.$refs.gaugeChart)
    })
  },
  methods: {
    getData() {
      this.option.series[0].data[0].value = this.data
      this.option.series[1].axisLine.lineStyle.color[0] = [
        this.data / 100, new echarts.graphic.LinearGradient(
          0, 0, 1, 0, [
            {
              offset: 0,
              color: this.topColor
            }
          ]
        )
      ]
      this.chart.clear()
      this.chart.setOption(this.option)
    }
  }
}
</script>

<style lang="scss" scoped>
  .gauge-chart-box {
    width: 100%;
    height: 100px;
  }
</style>
