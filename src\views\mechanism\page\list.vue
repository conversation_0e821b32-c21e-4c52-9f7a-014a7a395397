<template>
  <div>
    <page :request="request" :list="list" table-title="机构列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button v-permission="'add'" size="small" type="primary" @click="handleCreate('')">创建机构</el-button>
      </div>
    </page>
    <MechanismDetail :id="mechanismId" v-model="drawerShow" :account-id="accountId" />
    <AccountMan :id="mechanismId" v-model="accountManShow" />
    <ProductConfig :id="mechanismId" v-model="productConfigShow" />
  </div>
</template>

<script lang="jsx">
import page from '@/components/restructure/page/index.vue'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { ApiMechanismList, MechanismTypeList, CooperationStatusList, MechanismStatusList } from '../enum'
import CitySelector from '@/components/CitySelector/index.vue'
import MechanismDetail from '../components/MechanismDetail/MechanismDetail.vue'
import AccountMan from '../components/AccountMan.vue'
import ProductConfig from '../components/ProductConfig.vue'
import { getMechanismListApi, changeMechanismStatusApi } from '@/api/mechanism'
import { hasPermission } from '@/utils/menuCodes'
import moment from 'moment'

export default {
  components: {
    page,
    MechanismDetail,
    AccountMan,
    ProductConfig
  },
  data() {
    return {
      accountId: null,
      // 机构id
      mechanismId: null,
      productConfigShow: false,
      accountManShow: false,
      drawerShow: false,
      cityIdStr: null,
      listQuery: {
        // 开始时间
        createTimeStart: '',
        // 结束时间
        createTimeEnd: '',
        // 机构信息：信用代码、名称、姓名、手机号
        keywords: '',
        // 机构类型
        agencyType: '',
        // 所在城市ID, 多个用逗号分隔或其他分隔符
        addressIds: '',
        // 是否是api机构
        cooperateIsApi: null,
        // 创建人
        createBy: '',
        // 合作状态
        cooperateStatus: null,
        // 机构状态
        status: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const params = { ...this.listQuery, ...data }
          const res = await getMechanismListApi(params)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '机构信息',
          key: 'keywords',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '输入信用代码/名称/姓名/手机号'
          }
        },
        {
          title: '机构类型',
          key: 'agencyType',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismTypeList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: 'API机构',
          key: 'cooperateIsApi',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: ApiMechanismList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '创建时间',
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['createTimeStart', 'createTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          val: [this.listQuery.createTimeStart, this.listQuery.createTimeEnd],
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '创建人',
          key: 'createBy',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '输入姓名/手机号'
          }
        },
        {
          title: '合作状态',
          key: 'cooperateStatus',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: CooperationStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '所在城市',
          key: 'addressIds',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          renderCustom: (_h, params, vm) => {
            return <CitySelector
              value={vm.value}
              placeholder='城市'
              check-strictly
              style={{ width: '200px' }}
              onChange={(e) => vm.$emit('input', e)}
            />
          }
        },
        {
          title: '机构ID',
          key: 'id',
          width: 80,
          render: (_h, params) => {
            if (!hasPermission({ buttons: 'detail' })) {
              return <div>{params.data.row.id}</div>
            }
            return <el-button type='text' onClick={() => this.handleOpenDetail(params.data.row)}>{params.data.row.id}</el-button>
          }
        },
        {
          title: '统一社会信用代码',
          key: 'companyCreditCode',
          width: 150
        },
        {
          title: '机构名称',
          key: 'agencyName'
        },
        {
          title: '机构类型',
          key: 'agencyType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismTypeList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '所在城市',
          key: 'companyAddressName',
          render: (_h, params) => {
            return <div class='tooltip'>{params.data.row.companyAddressName}</div>
          },
          width: 250
        },
        {
          title: '联系人',
          key: 'contactName'
        },
        {
          title: '联系人手机号',
          key: 'contactMobile',
          width: 100
        },
        {
          title: '有效期截止日',
          key: 'cooperateEndDate',
          width: 100,
          render: (_h, params) => {
            const textColorMap = {
              '1': '',
              '2': '#f00',
              '3': '#9f9f9f'
            }

            return <span style={{
              color: textColorMap[params.data.row.expireFlag]
            }}>{params.data.row.cooperateEndDate}</span>
          }
        },
        {
          title: 'API机构',
          key: 'cooperateIsApi',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: ApiMechanismList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '合作状态',
          key: 'cooperateStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: CooperationStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '创建人',
          key: 'createByName',
          width: 150,
          render: (_h, params) => {
            return <div>{params.data.row.createByName} {params.data.row.createByMobile}</div>
          }
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 150,
          render: (_h, params) => {
            return <div>{moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm')}</div>
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 150,
          render: (_h, params) => {
            return <div>{moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm')}</div>
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 310,
          fixed: 'right',
          activeType: [
            {
              text: '详情',
              key: 'detail',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'detail' }),
              click: (_$index, _item, params) => {
                this.handleOpenDetail(params)
              }
            },
            {
              text: '资质管理',
              key: 'qualifications',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'certification' }),
              click: (_$index, _item, params) => {
                this.handleCreate(params.id)
              }
            },
            {
              text: '账号管理',
              key: 'account',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'account' }),
              click: (_$index, _item, params) => {
                this.handleOpenAccount(params.id)
              }
            },
            {
              text: '产品配置',
              key: 'config',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'product' }),
              click: (_$index, _item, params) => {
                this.handleOpenProductConfig(params)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'status' }),
              render: (_h, params) => {
                return <el-button type='primary' plain onClick={() => this.changeMechanismStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    resetForm() {
      this.$store.dispatch('tableRefresh', this)
    },
    // 账号管理
    handleOpenAccount(id) {
      this.mechanismId = id
      this.accountManShow = true
    },
    // 产品配置
    handleOpenProductConfig(row) {
      this.mechanismId = row.id
      this.productConfigShow = true
    },
    // 打开详情
    handleOpenDetail(row) {
      this.accountId = row.accountId
      this.mechanismId = row.id
      this.drawerShow = true
    },
    // 打开创建机构
    handleCreate(id) {
      this.$router.push({
        path: '/mechanism/form',
        query: {
          id
        }
      })
    },
    // 改变机构状态
    changeMechanismStatus(row) {
      this.$confirm(`确认${row.status === 1 ? '禁用' : '启用'}此机构？`, '', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await changeMechanismStatusApi({
            id: row.id,
            status: row.status === 1 ? 0 : 1
          })

          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
