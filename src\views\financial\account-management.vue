<template>
  <div class="account-management">
    <Page :request="request" :list="list" table-title="账户管理">
      <div slot="titleContainer" class="title-container-box">
        <div class="title-table-list">
          <div class="title-item">
            总金额（元）
          </div>
          <div class="title-item min-width-80">
            {{ sumData.rechargeTotalAmount }}
          </div>
          <div class="title-item">
            可用余额合计(元)
          </div>
          <div class="title-item min-width-80">
            {{ sumData.rechargeBalance }}
          </div>
          <div class="title-item">
            扣款合计(元)
          </div>
          <div class="title-item min-width-80">
            {{ sumData.chargebacksAmount }}
          </div>
          <div class="title-item">
            保证金总金额(元)
          </div>
          <div class="title-item min-width-80">
            {{ sumData.marginAmount }}
          </div>
          <div class="title-item">
            赠送总金额(元)
          </div>
          <div class="title-item min-width-80">
            {{ sumData.giveAmount }}
          </div>
        </div>
      </div>
    </Page>
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="{title:dialogTitle,width:'450px'}">
      <addMoneyForm :id="accountId" :type="dialogType" @submit="submit" @cancel="dialogFormVisible = false" />
    </SDialog>
    <Drawer
      :visible.sync="drawer"
    >
      <div class="pd-10px">
        <accountDetail :id="accountId" />
      </div>
    </Drawer>
  </div>
</template>

<script>
import Drawer from '@/components/Drawer/FormSection2.vue'
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { hasPermission } from '@/utils/menuCodes'
import SDialog from '@/components/restructure/dialog'
import addMoneyForm from './components/accountManagement/addMoneyForm.vue'
import accountDetail from './components/accountManagement/accountDetail.vue'
import { get_financial_account, get_financial_sum, edit_financial_account } from '@/api/financial'
const activeType = [
  {
    key: 'recharge',
    name: '充值',
    value: 1
  },
  {
    key: 'deduct',
    name: '扣除',
    value: 2
  },
  {
    key: 'bail_recharge',
    name: '保证金充值',
    value: 3
  },
  { key: 'detail',
    name: '明细'
  }
]
export default {
  components: {
    Page,
    SDialog,
    addMoneyForm,
    accountDetail,
    Drawer
  },
  data() {
    return {
      dialogFormVisible: false,
      dialogTitle: '',
      dialogType: '',
      activeEvent: [],
      drawer: false,
      accountId: null,
      listQuery: {
        accountType: '',
        keyword: ''
      },
      accountList: [
        { value: 1, label: '公司账户' },
        { value: 2, label: '个人账户' }
      ],
      sumData: {},
      request: {
        getListUrl: async data => {
          const res = await get_financial_account(data)

          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '关键词',
          key: 'keyword',
          type: formItemType.input,
          search: true,
          val: this.listQuery.keyword,
          tableHidden: true,
          options: {
            placeholder: '账户id/账户名称/证件号码'
          }
        },
        {
          title: '账户ID',
          key: 'accountNo'
        },
        {
          title: '账户类型',
          key: 'accountType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.accountList,
          val: this.listQuery.accountType,
          clearable: true,
          search: true
        },
        {
          title: '账户名称',
          key: 'accountName'
        },
        {
          title: '证件号码',
          key: 'credentialsCode'
        },
        {
          title: '充值总金额(元)',
          key: 'rechargeTotalAmount'
        },
        {
          title: '可用余额(元)',
          key: 'rechargeBalance'
        },
        {
          title: '已扣款金额(元)',
          key: 'chargebacksAmount'
        },
        {
          title: '冻结金额(元)',
          key: 'freezeAmount'
        },
        {
          title: '保证金金额(元)',
          key: 'marginAmount'
        },
        {
          title: '赠送金额(元)',
          key: 'giveAmount'
        },
        {
          title: '开通日期',
          key: 'createTime'
        },
        {
          title: '操作',
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 320,
          fixed: 'right',
          activeType: activeType.map(buttons => {
            return {
              text: buttons.name,
              key: buttons.key,
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                const { key, name } = buttons
                if (['recharge', 'deduct', 'bail_recharge'].includes(key)) {
                  this.dialogFormVisible = true
                  this.dialogTitle = name
                  this.dialogType = key
                } else if (['detail'].includes(key)) {
                  this.drawer = true
                }
                this.accountId = params.id
              },
              hidden: !hasPermission({ buttons: buttons.key })
            }
          })
        }
      ]
    }

  },
  created() {
    // this.activeEvent = hasPermission({ path, buttons: meta?.buttons ?? [] })
    this.getAllAmount()
  },
  methods: {
    getAllAmount() {
      get_financial_sum().then(res => {
        this.sumData = res?.data ?? {}
      }).catch(() => {})
    },
    refreshData() {
      this.$store.dispatch('tableRefresh', this)
      this.getAllAmount()
    },
    submit(data) {
      // 充值提交 this.dialogType
      const businessType = activeType.find(item => item.key === this.dialogType)?.value
      edit_financial_account({ ...data, businessType, type: this.dialogType === 'deduct' ? 2 : 1 }).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.dialogFormVisible = false
          this.refreshData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.account-management{
    .title-container-box{
        justify-content: flex-start;
        width: 100%;
        border: none;
        padding: 0 0;
      }
      .title-table-list{
        display: flex;
        border: 1px solid #dfdfdf;
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        margin-bottom: 10px;
      }
      .title-item{
        flex-shrink: 0;
        height: 40px;
        line-height: 40px;
        padding: 0 15px;
        border-right:1px solid #dfdfdf;
        &:last-child{
          border: none;
        }
      }
      .pd-10px{
        padding: 10px ;
      }
      .min-width-80{
        min-width: 10%;
      }
}
</style>
