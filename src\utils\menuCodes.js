import { constantRoutes } from '@/router'
import store from '@/store'
export const getCurrentPathName = () => {
  let path = location.hash || location.pathname
  path = path.replace('#', '')
  return path
}
export const rolesButtons = () => {
  let { buttons: roles } = store.getters.userInfo
  !roles && (roles = JSON.parse(sessionStorage.getItem('user') || {})?.buttons)
  return roles
}
/**
 * 处理角色菜单遍历数据
 * @param {*} menuData 来自接口数据
 */

export function handleMenuData(menuData) {
  if (!menuData) return []
  const findCurPath = (item, cur) => {
    if (item.children && item.children.length > 0) {
      item.children.forEach(el => {
        return findCurPath(el, cur)
      })
    } else if (item.path === cur.path && item.meta.buttons) {
      cur.children = item.meta?.buttons?.map(el => {
        return { id: cur.path + '/' + el.key, title: el.name }
      })
      cur.children && cur.children.unshift({ id: cur.path + '/' + 'review', title: '查看' })
      return cur
    }
  }
  const handleChildren = (cur) => {
    if (cur.children && cur.children.length > 0) {
      cur.children.forEach(item => {
        handleChildren(item)
      })
      return cur
    } else {
      for (let i = 0; i < constantRoutes.length; i++) {
        const item = constantRoutes[i]
        const insertCur = findCurPath(item, cur)
        if (insertCur) {
          cur = { ...insertCur }
          break
        }
      }
      return cur
    }
  }

  return menuData.reduce((acc, cur) => {
    const arrayList = handleChildren(cur)
    arrayList && acc.push(arrayList)
    return acc
  }, [])
}
//
/**
 *当前菜单处理按钮权限
 * @param {*} curMenuInfo：{buttons: 'detail' }
 * @returns []
 */
export function hasPermission(curMenuInfo) {
  let { buttons } = curMenuInfo
  const roles = rolesButtons()

  const path = getCurrentPathName()

  if (!buttons || !path) return false
  buttons = path + '/' + buttons

  return (roles?.some(role => {
    return buttons.includes(role) || role.includes(buttons)
  }))
}
