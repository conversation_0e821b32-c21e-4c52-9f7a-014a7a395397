<template>
  <div class="role">
    <div class="page-title">角色权限</div>
    <div class="page-container">
      <div class="user-left">
        <div class="user-col">
          <div class="t-label">角色列表</div>
          <el-button v-permission="'add'" class="add-btn" type="text" @click="addRole">新增</el-button>
        </div>
        <div v-for="item in roleList" :key="item.id" class="user-col cursor-pointer" :class="{active: curSelectRole.id===item.id}" @click="curSelectRole=item">
          <div class="user-name">{{ item.name }}</div>
          <div v-if="item.code!='SUPER_ROLE'" class="right-btns">
            <el-button v-permission="'edit'" type="primary" plain size="small" @click.stop="editRole(item.id)">编辑</el-button>
            <el-button v-permission="'delete'" type="primary" plain size="mini" @click.stop="changeUseStatus(item)">{{ item.status===0?'禁用':'启用' }}</el-button>
            <el-button v-permission="'del-delete'" type="primary" plain size="mini" @click.stop="deleteRole(item)">删除</el-button>
          </div>
        </div>
      </div>
      <div class="user-right">
        <div class="t-label flex justify-between">权限设置  <el-button size="small" type="primary" @click="roleSure">保存</el-button></div>
        <rolePermissionV2 v-if="curSelectRole.id" ref="rolePermissionV2" :role-id="curSelectRole.id" />
      </div>
    </div>
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps" :close-on-click-modal="false">
      <role-detail :role-id="roleId" :type="type" @success="success" @close="close" />
    </SDialog>
  </div>
</template>

<script>
import { get_admin_role_list, updateRoleStatus, del_role } from '@/api/system'
import roleDetail from '@/views/system/page/roleDetail'
import SDialog from '@/components/restructure/dialog'
import rolePermissionV2 from './rolePermissionV2.vue'
export default {
  components: {
    SDialog,
    roleDetail,
    rolePermissionV2
  },
  data() {
    return {
      roleList: [],
      dialogOps: {
        title: '',
        width: '500px'
      },
      type: '',
      dialogFormVisible: false,
      roleId: '',
      curSelectRole: {}
    }
  },
  async mounted() {
    await this.getRoleList().catch(() => {})
    this.curSelectRole = this.roleList?.[0] ?? {}
  },
  methods: {
    getRoleList() {
      return get_admin_role_list().then(res => {
        if (res.code == 200) {
          this.roleList = res.data
        }
        return res
      })
    },
    addRole() {
      this.type = 'add'
      this.dialogOps.title = '新增角色'
      this.dialogFormVisible = true
    },
    close() {
      this.dialogFormVisible = false
    },
    success() {
      this.getRoleList()
      this.$store.dispatch('user/getUserInfo')
    },
    editRole(id) {
      this.roleId = id
      this.type = 'edit'
      this.dialogOps.title = '编辑角色'
      this.dialogFormVisible = true
    },
    changeUseStatus(item) {
      // 处理禁用启用状态
      this.$confirm(`确定要${item.status === 0 ? '禁用' : '启用'}此角色`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateRoleStatus(item.id).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.success()
          }
        })
      })
    },
    deleteRole(item) {
      this.$confirm(`确定删除该角色？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del_role(item.id).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.success()
          }
        })
      })
    },
    roleSure() {
      this.$refs?.rolePermissionV2?.sure()
    }
  }
}
</script>

<style lang="scss" scoped>
.role{
  .page-title{
    font-size: 16px;
    margin-bottom: 10px;
  }
  .page-container{
    display: flex;
    border: 1px solid #dfdfdf;
  }
  .flex{
    display: flex;
    align-items: center;
    &.justify-between{
      justify-content: space-between;
    }
  }
  .t-label{
    font-size: 16px;
    margin-bottom: 10px;
  }
  .user-left{
    max-width: 400px;
    min-width: 350px;
    flex-shrink: 0;
  }
  .user-right,.user-left{
    height: 84vh;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .user-right{
    flex-grow: 1;
    border-left: 1px solid #dfdfdf;
    padding: 10px ;
  }
  .user-col{
    min-height: 46px;
    margin: 0 5px;
    display: flex;
    padding: 0 5px;
    justify-content: space-between;
    align-items: center;
    &.active{
      background-color:rgba(64, 158, 255, 0.2) ;
    }
  }
  .add-btn{
    color: #409EFF;
  }
  .right-btns{
    display: flex;
    .el-button{
      padding: 5px 5px;
    }
  }
  .cursor-pointer{
    cursor: pointer;
  }
}
</style>
