import CONSTANT from '@/config/constant.conf'
import Cookies from 'js-cookie'
import moment from 'moment'
import cloneDeep<PERSON>ith from 'lodash/cloneDeepWith'

const utils = {
  /*
   * 截取字符串并把多余部分处理成省略号
   * @param {string} str - 需要处理的字符串
   * @param {number} num - 截取字符串的结束为止数字
   * @return {string} 返回截取后的字符串
   * */
  subStr(val, _num) {
    const len = val.length
    const values =
      len > 2
        ? val.replace(val.substring(1, len - 1), '*')
        : `${val.substr(0, 1)}*`
    return values || val
  },

  /*
   * 获取token
   * @return token or false
   * */
  getToken() {
    const token = Cookies.get('token')
    if (token) return token
    else return false
  },

  /*
   * 设置token
   * @param {string} token - 本地存储token
   * @return {}
   * */
  setToken(token) {
    return Cookies.set('token', token, {
      expires: CONSTANT.cookieExpires || 3
    })
  },

  /*
   * 验证电话号码
   * @param {string or number} val - 电话号码
   * @return true or false
   * */
  validatePhone(val) {
    return /^1[3456789]\d{9}$/.test(val)
  },

  /*
   * 密码验证
   * @param {string} val - 密码值
   * @return true or false
   * */
  validatePass(val) {
    return /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,8}$/.test(val)
  },
  verificationPass(val) {
    // 包含大写、小写字母、数字，特殊字符组合的8-32位
    return /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,32}$/.test(
      val
    )
  },
  pass_errorText: '大小写字母、数字、特殊字符组合的8-32位',

  /*
   * 金额验证-只能保留两位小数和整数
   * @param {string,number} val - 值
   * @return true or false
   * */
  validateNumber(val) {
    return /^\d+(\.\d{1,2})?$/.test(val)
  },

  // 严格金额验证 - 不能为0 支持最多两位小数和整数
  validateMoney(val) {
    return /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(val)
  },

  /*
   * 验证字符串是否为空
   * @param {string} str - 字符串
   * @return true ot false
   * */
  isEmpty(str) {
    return typeof str === 'undefined' || str === null || str === ''
  },
  getTimer(time) {
    return time ? moment(time).format('YYYY-MM-DD') : ''
  },

  /*
   * 映射对应表
   * @param {array} stateKey - 索引id
   * @param {array} stateVal - 索引值
   * @param {string} type - 需要筛选出来的索引id
   * @return {string}
   * */
  lookUp(stateKey, stateVal, type) {
    let _level = ''
    for (const i in stateKey) {
      if (stateKey[i] === type) {
        _level = stateVal[i]
        return _level
      }
    }
    return _level
  },
  /**
   * 时间秒数格式化
   * @param {number} s - 时间戳（单位：秒）
   * @return {string} 格式化后的时分秒
   */
  secToTime(s) {
    let t
    if (s > -1) {
      const hour = Math.floor(s / 3600)
      const min = Math.floor(s / 60) % 60
      const sec = s % 60
      if (hour < 10) {
        t = '0' + hour + ':'
      } else {
        t = hour + ':'
      }

      if (min < 10) {
        t += '0'
      }
      t += min + ':'
      if (sec < 10) {
        t += '0'
      }
      t += sec
    }
    return t
  },

  /*
   * 打印当前版本号
   * @return version
   * */
  beautifulConsoleVersion() {
    return console.log(
      '%c当前版本: ' + CONSTANT.version,
      'color: #04BE02;font-size:22px'
    )
  },

  /*
   * 判断数组是否包含某个字符
   * @param {array} arr 数组
   * @param {string} val 判断包含的字符
   * @return true of false
   * */
  includeValue(arr, val) {
    return arr.indexOf(val) > -1
  },

  backIndex(arr, val) {
    return JSON.stringify(arr).indexOf(val) > -1
  },
  /*
   * 清空对象
   * */
  removeObj(data) {
    for (const key in data) {
      if (data[key] instanceof Array) {
        data[key] = []
      } else {
        data[key] = ''
      }
    }
    return data
  },
  /**
   * @description 格式化菜单
   * @param menu
   */
  formatMenu(menu) {
    return cloneDeepWith(menu, item => {
      if (item.children === null) {
        delete item.children
        return item
      }
    })
  },
  recursionRoute(arr, curRoute, permission) {
    for (const value of arr) {
      if (
        value.path === curRoute.path ||
        (curRoute.meta && curRoute.meta.activeMenu === value.path)
      ) {
        permission.has = true
      }
      if (value.children) {
        utils.recursionRoute(value.children, curRoute, permission)
      }
    }
    return permission.has
  },
  permissionRouter(allRouter, curRoute) {
    var permission = {
      has: false
    }
    if (curRoute.meta && curRoute.meta.noVerify) {
      return true
    }
    return utils.recursionRoute(allRouter, curRoute, permission)
  },

  /*
   *  本地存储菜单目录
   * */
  setMenus(values) {
    return sessionStorage.setItem('menuData', JSON.stringify(values)) || []
  },

  /*
   *  获取存储菜单目录
   * */
  getMenus() {
    return JSON.parse(sessionStorage.getItem('menuData')) || []
  },

  /*
   *  删除存储菜单目录
   * */
  remMenus() {
    return sessionStorage.removeItem('menuData')
  },
  /*
   *  存储用户信息
   * */
  setUserData(values) {
    return sessionStorage.setItem('user', JSON.stringify(values)) || ''
  },
  /*
   * 获取用户信息
   * */
  getUserData() {
    return JSON.parse(sessionStorage.getItem('user')) || {}
  },

  setLastLoginTime(values) {
    return localStorage.setItem('lastLoginTime', values) || ''
  },
  /*
   * 获取用户信息
   * */
  getLastLoginTime() {
    return localStorage.getItem('lastLoginTime') || {}
  },
  removeLastLoginTime() {
    return localStorage.removeItem('lastLoginTime')
  },
  /*
   * 订单状态
   * */
  strOrder(status) {
    let str = ''
    const statusList = [
      { name: '待付款', status: 'WAITPAY' },
      { name: '待发货', status: 'WAITSEND' },
      { name: '已发货', status: 'FINISHSEND' },
      { name: '已完成', status: 'FINISHED' },
      { name: '已取消', status: 'CANCELED' },
      { name: '已删除', status: 'DELETED' }
    ]
    for (let i = 0; i < statusList.length; i++) {
      if (statusList[i].status === status) {
        str = statusList[i].name
        break
      }
    }
    return str
  },
  getOsList() {
    return [
      { label: 'android', value: 'android' },
      { label: 'ios', value: 'ios' },
      { label: 'ios-ys', value: 'ios-ys' }
    ]
  }
}

export default utils
