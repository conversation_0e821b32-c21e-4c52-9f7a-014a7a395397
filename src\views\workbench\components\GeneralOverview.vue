<template>
  <div>
    <PageTable @goDetail="goDetail" />
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="{width:'60%'}">
      <PageTable :detail-info="detailData" type="detail" />
    </SDialog>
  </div>
</template>

<script>
import PageTable from './StatisticsTable.vue'
import SDialog from '@/components/restructure/dialog'
export default {
  components: {
    PageTable,
    SDialog
  },
  data() {
    return {
      detailData: {},
      dialogFormVisible: false
    }
  },
  methods: {
    goDetail(data) {
      this.detailData = { ...data }
      this.dialogFormVisible = true
    }
  }
}
</script>
