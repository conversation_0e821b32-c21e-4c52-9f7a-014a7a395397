<template>
  <div>
    <Page :request="request" :list="list" table-title="撞库记录">
      <template #searchContainer>
        <el-button v-permission="'export'" :loading="exportLoading" plain size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
      </template>
    </Page>
    <MatchHistory v-model="matchHistoryModel" :table-id="matchHistoryId" @close="handleClose" />
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { OverallStrategyClueSourceList } from '@/views/distribution/enum'
import { CollisionStatusList } from '../enum'
import { hasPermission } from '@/utils/menuCodes'
import MatchHistory from '@/views/clue/components/MatchHistory.vue'
import { getCollisionRecordListApi, exportCollisionRecordApi } from '@/api/clue'

export default {
  name: 'MatchRecord',
  components: {
    Page,
    MatchHistory
  },
  data() {
    return {
      matchHistoryId: null,
      matchHistoryModel: false,
      exportLoading: false,
      listQuery: {
        source: null,
        status: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getCollisionRecordListApi({
            ...this.listQuery,
            // 默认查用户类型
            type: 1
          })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '数据来源',
          key: 'source',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: OverallStrategyClueSourceList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '撞库状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: CollisionStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '撞库时间',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['receivingStartTime', 'receivingEndTime'],
          pickerDay: 9999999,
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '记录ID',
          key: 'id'
        },
        {
          title: '线索ID',
          key: 'clueId'
        },
        {
          title: '数据来源',
          key: 'sourceName'
        },
        {
          title: '姓名',
          key: 'name'
        },
        {
          title: '手机号',
          key: 'mobileNo'
        },
        {
          title: '城市',
          key: 'city'
        },
        {
          title: '性别',
          key: 'sexName'
        },
        {
          title: '年龄',
          key: 'age'
        },
        {
          title: '车牌号',
          key: 'licensePlateNumber'
        },
        {
          title: '车辆状态',
          key: 'carLoanName'
        },
        // {
        //   title: '车辆识别代号',
        //   key: 'carIdentificationNumber'
        // },
        {
          title: '芝麻分',
          key: 'sesameScoreName'
        },
        {
          title: '撞库状态',
          key: 'statusName'
        },
        {
          title: '失败原因',
          key: 'errorMsg'
        },
        {
          title: '撞库时间',
          key: 'createTime'
        },
        {
          title: '撞库耗时',
          key: 'times',
          render: (_h, params) => {
            return <span>{params.data.row.times}s</span>
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 130,
          fixed: 'right',
          activeType: [
            {
              text: '机构撞库明细',
              key: 'detail',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'detail' }),
              click: (_$index, _item, params) => {
                this.handleOpenHistory(params)
              }
            }
          ]
        }
      ]
    }

  },
  methods: {
    async handleExport() {
      this.exportLoading = true
      try {
        window.open(exportCollisionRecordApi({
          ...this.listQuery,
          pageSize: 10000,
          token: this.$store.getters.authorization
        }))
      } catch (err) {
        console.log(err)
      } finally {
        this.exportLoading = false
      }
    },
    handleOpenHistory(row) {
      this.matchHistoryId = row.clueId
      this.matchHistoryModel = true
    },
    handleClose() {
      this.matchHistoryId = null
    }
  }
}
</script>
