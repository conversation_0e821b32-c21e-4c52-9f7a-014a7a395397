{"name": "vue-admin", "version": "1.0.0", "description": "vue admin with Element UI & axios & iconfont & permission control & lint", "author": "ZSY", "scripts": {"dev": "vue-cli-service serve --mode development", "build:prod": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:preRelease": "vue-cli-service build --mode preRelease", "build:testB": "vue-cli-service build --mode testB", "build:testC": "vue-cli-service build --mode testC", "build:testE": "vue-cli-service build --mode testE", "preview": "node build/index.js --preview --report", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@tinymce/tinymce-vue": "^3.0.1", "@vant/area-data": "^1.5.0", "@vue/cli-shared-utils": "^5.0.8", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "ansi-styles": "^6.1.0", "axios": "0.18.1", "clipboard": "^2.0.4", "compressorjs": "^1.2.1", "core-js": "^3.22.7", "echarts": "^4.2.1", "element-ui": "2.15.14", "js-cookie": "2.2.0", "lodash": "^4.17.20", "lottie-web": "^5.9.4", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "^6.9.4", "tinymce": "^5.4.2", "v-viewer": "^1.5.1", "vue": "2.6.10", "vue-phone-preview": "^0.4.17", "vue-router": "3.0.6", "vue-simple-uploader": "^0.7.6", "vue-worker": "^1.2.1", "vue2-animate": "^2.1.3", "vuedraggable": "^2.24.0", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}