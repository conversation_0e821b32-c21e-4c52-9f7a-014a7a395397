<template>
  <div>
    <span v-if="column.tableView===tableView.text||column.tableView===tableView.requestText" @click="handleClick">{{ getText(column) }}</span>
    <span v-if="column.tableView===tableView.jump" class="JumpClick" @click="handleJumpClick">{{ getJump(data[column.key]) }}</span>
    <span v-if="column.tableView===tableView.clipboard" :data-clipboard-text="data[column.key]" class="JumpClick" @click="handCopy">{{ data[column.key] }}</span>
    <span v-if="column.tableView===tableView.subTitle">{{ getSubTitle(column) }}</span>
    <span v-if="column.tableView===tableView.prefixTitle">{{ getPrefixTitle(column) }}</span>
    <span v-if="column.tableView===tableView.picture" style="position: relative;">
      <viewer v-if="imagesArray&&imagesArray.length" :images="imagesArray" class="img-wrap" style="margin: auto;">
        <template v-for="(item , index) in imagesArray">
          <img v-show="index==0" :key="index" v-error :src="item" :style="index==0?`max-width: 50px;max-height: 50px;`:`width:0px;height:0px`">
        </template>
      </viewer>
      <span v-if="imagesArray.length>1" style="position: absolute;bottom: -3px;left: 50%;transform: translateX(-50%);color: #000;font-size: 12px;">×{{ imagesArray.length }}</span>
    </span>
    <span v-if="column.tableView===tableView.tagState" @click="handleClick">
      <el-tag :type="column.tagState[String(data[column.key])]">{{ getText(column) }}</el-tag>
    </span>
    <span v-if="column.tableView===tableView.date" @click="handleClick">{{mapRecursion(data,column.key), ((column.options&&column.options.format)||'yyyy-MM-dd hh:mm:ss') | parseTimeTwo}}</span>
    <span v-if="column.tableView===tableView.colorView||column.tableView===tableView.pointerColorView" :style="getStyle" @click="handleClick">{{ basics.isNull(data[column.key]) ? ((column.options&&column.options.alternate)||'-') : column.options&&column.options.alternate ? data[column.key] : getText(column) }}</span>
  </div>
</template>

<script>
import { tableItemType, picturePath } from '@/config/sysConfig'
import { mapRecursion } from '@/config/basicsMethods'
import Clipboard from 'clipboard'
export default {
  name: 'Index',
  props: {
    column: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableView: tableItemType.tableView,
      picturePath: picturePath,
      mapRecursion: mapRecursion,
      tagState: ''
    }
  },
  computed: {
    getStyle: function() {
      return {
        ...{},
        ...{
          color: this.column.color || '#409EFF',
          cursor:
            this.column.tableView === this.tableView.pointerColorView
              ? 'pointer'
              : 'auto'
        },
        ...(this.column.style || {}),
        ...(this.column.options && this.column.options.style
          ? this.column.options.style(this.data)
          : {})
      }
    },
    imagesArray() {
      if (this.data && Object.keys(this.data).length > 0) {
        if (Array.isArray(this.data[this.column.key])) {
          return this.data[this.column.key]
        } else {
          return this.data[this.column.key]?.split(',') ?? []
        }
      } else {
        return []
      }
    }
  },
  methods: {
    handCopy() {
      const clipboard = new Clipboard('.JumpClick')
      clipboard.on('success', e => {
        this.$message.success('复制链接成功')
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制
        this.$message.error('该浏览器不支持自动复制')
        // 释放内存
        clipboard.destroy()
      })
    },
    getText(column, callback) {
      if (this.basics.isNull(column.list)) return '-'
      const index = this.data[column.key]
      const filter = column.list.filter(item => {
        if (String(item.value) === String(index)) {
          return item
        }
      })
      if (callback) callback(filter[0])
      return filter[0] ? filter[0].label : '-'
    },
    getSubTitle(column) {
      const data = this.data[column.key]
      if (this.basics.isNull(data)) return '-'
      return column.options.subTitle + data
    },
    getPrefixTitle(column) {
      const data = this.data[column.key]
      if (this.basics.isNull(data)) return '-'
      return data + column.options.prefixTitle
    },
    getJump(data) {
      if (this.basics.isNull(data)) return '-'
      return data
    },
    handleClick() {
      try {
        if (this.column.options) {
          this.column.options.on.click(this.data, this.column)
        } else {
          this.column.tableOptions.on.click(this.data, this.column)
        }
      } catch (e) {
        console.log(e)
      }
    },
    getOptions() {
      return this.column.options || {}
    },
    async handleJumpClick() {
      if (this.column.options) {
        const options = this.column.options
        // const query = data.query || [];
        // let aggieQuery = {};
        // if (this.basics.isArray(query)) {
        //   data.query.filter(item => {
        //     aggieQuery[item] = data[item] || '';
        //   });
        // } else if (this.basics.isFunction(query)) {
        //   aggieQuery = await query(this.data);
        // }
        this.$router.push({
        //   name: data.name,
          path: options.path + this.data[options.paramsKey],
          query: options.query
        })
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.test {
  cursor: auto;
}
.login-page {
  cursor: pointer;
  max-width: 50px;
  max-height: 50px;
  &:hover {
    box-shadow: 1px 1px 8px -2px #333333;
  }
}
.JumpClick {
  color: #409eff;
  cursor: pointer;
}
div {
  white-space: nowrap;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
