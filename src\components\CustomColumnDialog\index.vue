<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="custom-column-dialog">
      <!-- 快捷操作按钮 -->
      <div class="operation-buttons">
        <el-button size="small" @click="handleSelectAll">全选</el-button>
        <el-button size="small" @click="handleUnselectAll">全不选</el-button>
        <el-button size="small" @click="handleRestoreDefault">默认</el-button>
      </div>

      <!-- 列选择区域 -->
      <div class="column-list">
        <div v-for="(group, groupIndex) in groupedColumns" :key="'group-' + groupIndex">
          <div class="column-group">
            <div class="group-header">
              <el-checkbox
                :value="isGroupSelected(group.columns)"
                :indeterminate="isGroupIndeterminate(group.columns)"
                @change="(val) => handleGroupCheckChange(val, group.columns)"
              >
                {{ group.name }}
              </el-checkbox>
            </div>
            <div class="group-items">
              <el-checkbox
                v-for="column in group.columns"
                :key="column.key"
                :value="isColumnSelected(column.key)"
                @change="(val) => handleColumnChange(val, column.key)"
                class="column-item"
              >
                {{ column.title }}
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'CustomColumnDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    columnList: {
      type: Array,
      default: () => []
    },
    defaultSelected: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'column',  // 可选值: 'column'(自定义列) 或 'group'(自定义分组)
      validator: (value) => ['column', 'group'].includes(value)
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedColumns: new Set()
    }
  },
  created() {
    // this.selectedColumns = this.getDefaultColumnKeys()
  },
  computed: {
    // 根据类型返回对话框标题
    dialogTitle() {
      return this.type === 'column' ? '自定义列设置' : '自定义分组设置'
    },
    // 将列按照分组进行整理
    groupedColumns() {
      return this.processGroupedColumns(this.columnList, this.type)
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        console.log(`${this.type} 对话框打开，默认选中：`, this.defaultSelected);
        
        // 空数组表示用户选择的"全不选"，应保留
        // undefined或null表示未设置，应使用默认值
        if (this.defaultSelected === undefined || this.defaultSelected === null) {
          console.log(`${this.type} 对话框使用默认键`);
          this.selectedColumns = this.getDefaultColumnKeys();
        } else {
          console.log(`${this.type} 对话框使用传入的默认选中项，长度:`, this.defaultSelected.length);
          this.selectedColumns = new Set(this.defaultSelected);
        }
          
        console.log(`${this.type} 对话框设置选中项完成，总数：`, this.selectedColumns.size);
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    // 处理分组列数据
    processGroupedColumns(columnList, type) {
      const groups = new Map()
      
      columnList.forEach(item => {
        // 根据类型过滤不同的数据
        if (type === 'column' && item.isCustomGroup) return
        if (type === 'group' && !item.isCustomGroup) return

        // 只处理有 customColumns 和 customColumnsGroupName 的项
        if (item.customColumns && item.customColumnsGroupName) {
          const groupName = item.customColumnsGroupName
          
          if (!groups.has(groupName)) {
            groups.set(groupName, {
              name: groupName,
              columns: []
            })
          }
          
          // 只添加当前项的 customColumns，并且过滤掉 isCustom: false 的列
          const group = groups.get(groupName)
          this.processCustomColumns(item.customColumns, group.columns)
        }
      })

      // 过滤掉没有列的分组
      return Array.from(groups.values()).filter(group => group.columns.length > 0)
    },
    
    // 处理自定义列
    processCustomColumns(sourceColumns, targetColumns) {
      sourceColumns.forEach(column => {
        // 过滤掉 isCustom 为 false 的列
        if (column.isCustom === false) return
        
        // 检查是否已经存在相同的列，避免重复
        if (!targetColumns.some(existingColumn => existingColumn.key === column.key)) {
          targetColumns.push(column)
        }
      })
    },
    
    // 检查列是否被选中
    isColumnSelected(key) {
      return this.selectedColumns.has(key)
    },
    
    // 处理单个列的选中状态变化
    handleColumnChange(checked, key) {
      if (checked) {
        this.selectedColumns.add(key)
      } else {
        this.selectedColumns.delete(key)
      }
      // 强制更新以触发重新渲染
      this.selectedColumns = new Set(this.selectedColumns)
    },
    
    // 检查分组是否全部选中
    isGroupSelected(columns) {
      if (!columns || columns.length === 0) return false
      return columns.every(col => this.selectedColumns.has(col.key))
    },
    
    // 检查分组是否部分选中
    isGroupIndeterminate(columns) {
      if (!columns || columns.length === 0) return false
      const selectedCount = columns.filter(col => this.selectedColumns.has(col.key)).length
      return selectedCount > 0 && selectedCount < columns.length
    },
    
    // 处理分组复选框变化
    handleGroupCheckChange(checked, columns) {
      if (!columns || columns.length === 0) return
      
      // 创建一个新的 Set，避免直接修改原引用
      const newSelectedColumns = new Set(this.selectedColumns)
      
      columns.forEach(col => {
        if (checked) {
          newSelectedColumns.add(col.key)
        } else {
          newSelectedColumns.delete(col.key)
        }
      })
      
      // 赋值为新的 Set
      this.selectedColumns = newSelectedColumns
    },
    
    // 获取所有可选列的key
    getAllColumnKeys() {
      return this.collectColumnKeys(this.groupedColumns, column => column.isCustom !== false)
    },
    
    // 获取默认显示的列key
    getDefaultColumnKeys() {
      return this.collectColumnKeys(this.groupedColumns, column => column.isCustom !== false && !column.defaultHidden && !column.noCheck)
    },
    
    // 收集满足条件的列键
    collectColumnKeys(groups, filterFn) {
      const keys = new Set()
      groups.forEach(group => {
        group.columns.forEach(col => {
          if (filterFn(col)) {
            keys.add(col.key)
          }
        })
      })
      return filterFn === undefined ? new Set(Array.from(keys)) : keys
    },
    
    // 全选
    handleSelectAll() {
      // 创建新的 Set 实例，强制触发响应式更新
      const allKeys = this.getAllColumnKeys()
      this.selectedColumns = new Set(allKeys)
    },
    
    // 全不选
    handleUnselectAll() {
      // 创建新的 Set 实例，强制触发响应式更新
      this.selectedColumns = new Set()
    },
    
    // 恢复默认
    handleRestoreDefault() {
      // 创建新的 Set 实例，强制触发响应式更新
      const defaultKeys = this.getDefaultColumnKeys()
      this.selectedColumns = new Set(defaultKeys)
    },

    // 获取选中的分组键
    getSelectedGroupKeys() {
      const selectedGroupKeys = []
      this.groupedColumns.forEach(group => {
        group.columns.forEach(col => {
          if (this.selectedColumns.has(col.key) && col.groupKey) {
            selectedGroupKeys.push(col.groupKey)
          }
        })
      })
      return selectedGroupKeys
    },

    // 如果是 group 分组模式，则返回选中的分组键 groupKey
    getSelectedKeys() {
      // if (this.type === 'group') {
      //   return this.getSelectedGroupKeys()
      // }
      return Array.from(this.selectedColumns)
    },
    
    // 确认
    handleConfirm() {
      // 对于column模式，检查是否至少选择了一项
      if (this.type === 'column' && this.selectedColumns.size === 0) {
        this.$message.warning('自定义列设置至少需要选择一项')
        return
      }
      
      this.$emit('confirm', this.getSelectedKeys())
      this.handleClose()
    },
    
    // 关闭
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-column-dialog {
  .operation-buttons {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #EBEEF5;
  }

  .column-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0 20px;

    .column-group {
      margin-bottom: 20px;

      .group-header {
        margin-bottom: 12px;
        
        ::v-deep .el-checkbox__label {
          font-weight: 500;
          color: #606266;
        }
      }

      .group-items {
        padding-left: 20px;

        .column-item {
          display: block;
          margin-bottom: 12px;
        }
      }
    }
  }
}

::v-deep .el-checkbox__label {
  font-weight: normal;
}
</style> 