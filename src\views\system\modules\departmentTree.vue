<template>
  <div class="tree-node-menu">
    <div v-if="item.childrenBranch&&item.childrenBranch.length" :key="item.id" class="tree-submenu">
      <tree-collapse :menu-class="[defaultActive==item.id&&'active-menu-item','menu-item-flex','tree-submenu-collapse','flex' ,'justify-between']" @click="emitEvent('select',item)">
        <div class="text-wrap">{{ item.branchName }}</div>
        <div class="edit-btn">
          <el-button v-permission="'edit'" type="text" icon="el-icon-edit" @click.stop="emitEvent('linkDetails',item)" />
          <el-button v-if="!isTopDepartment" v-permission="'delete'" type="text" icon="el-icon-delete" @click.stop="emitEvent('deleteDepartment',item)" />
        </div>
      </tree-collapse>
      <div class="padding-left-10 childNode">
        <TreeNode v-for="el in item.childrenBranch" :key="el.id" :default-active="defaultActive" :item="el" />
      </div>
    </div>
    <template v-else>
      <div :key="item.id" :class="{'active-menu-item': defaultActive==item.id}" class="tree-menu-item" @click="emitEvent('select',item)">
        <div class="menu-item-flex flex justify-between">
          <div class="text-wrap">{{ item.branchName }}</div>
          <div class="edit-btn">
            <el-button v-permission="'edit'" type="text" icon="el-icon-edit" @click.stop="emitEvent('linkDetails',item)" />
            <el-button v-if="!isTopDepartment" v-permission="'delete'" type="text" icon="el-icon-delete" @click.stop="emitEvent('deleteDepartment',item)" />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import bus from '@/utils/bus'
import treeCollapse from './treeCollapse.vue'
export default {
  name: 'TreeNode',
  components: {
    treeCollapse
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    defaultActive: {
      type: [String, Number],
      default: ''
    },
    isTopDepartment: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    emitEvent(type, item) {
      bus.$emit('treeClick', type, item)
    }
  }
}
</script>
<style lang="scss" scoped>

.tree-node-menu{
  .tree-submenu,.tree-menu-item{
    cursor: pointer;
  }
  .edit-btn{
    position: relative;
     display: none;
     flex-shrink: 0;
     .el-button--text{
      color: #555555;

     }
    }
    .text-wrap{
      white-space: wrap;
    }
  .tree-menu-item{
    margin-bottom: 10px;
  }
  .menu-item-flex{
    padding: 0 10px;
    min-height: 40px;
  }
  .padding-left-10{
    padding-left: 10px;
  }
  .childNode{
    padding: 0 10px;
  }
  .tree-submenu{
    >.tree-submenu-collapse{
      padding:5px  15px 5px 15px;
      margin-bottom: 10px;
      width: 100%;
    &.collapse-menu + .childNode{
      display: none;
    }
    &.expand-menu +.childNode{
      display: block;
    }
    }
  }
  .active-menu-item{
      position: relative;
         &::before{
          content: '';
          position: absolute;
          top: 0;
          left: -40%;
          width: 200%;
          background: rgba(64, 158, 255,0.2);
          height: 100%;
          transition: all linear 0.3s;
    }
    .edit-btn{
      display: flex;
      padding-right: 15px;
      }
    }

  .flex {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    &.justify-between{
      justify-content: space-between;
    }
  }

}
</style>
