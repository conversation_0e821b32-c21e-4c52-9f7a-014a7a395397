set -x
set -e

echo "----------------------------环境变量准备----------------"
echo $PATH
PATH=${NODE_VERSION}:$PATH
node -v | xargs echo "node version: "
echo "commit message:  ${CI_COMMIT_MESSAGE}"


echo "----------------------------安装模块--------------------"

if [ -n "${HTTP_PROXY}" ]; then
    echo "加速器模式: 使用代理加速"
    ${VUE_INSTALL} --proxy ${HTTP_PROXY}
else
    echo "加速器模式: 使用淘宝镜像加速"
   npm config set registry http://registry.npm.taobao.org && ${VUE_INSTALL} 
fi

echo "----------------------------开始构建--------------------"
${NODE_VERSION}/${VUE_BUILD}:${DEPLOY_ENV}
