.tab-head{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  //background: #304156;
  border-top: 1px solid #EBEEF5;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  height: 50px;
  .title{
    color: #304156;
    font-size: 16px;
  }
}
/*
* 条件查询部分
*/
.search-row{
  position: relative;
  margin-top: 30px;


  .search-col{
    display: inline-flex;
    align-items: center;
    margin-right: 30px;
    & > span {
      margin-bottom: 10px;
    }
    .name{
      font-size:14px;display: inline-block;align-items: center
    }
    .col{
      margin-bottom: 10px;
      font-size:14px;display: inline-block;align-items: center;
      width: auto;
    }
  }
  .row-right{
    position: absolute;right:0;top:0;
  }
  .search-btn{
    margin-left: 30px;
  }
  .search-minInput{
    width: 150px;
    margin-right: 10px;
  }
  .search-maxInput{
    width: 200px;
    margin-right: 10px;
  }
}
/*
* 分页
*/
.pagination{
  text-align: right;
  margin: 15px 0 0px;
}
/*
* 详情标题
*/
.details-title{
  font-size: 16px;
  height: 40px;line-height: 40px;
  background: rgba(224, 224, 224, 0.48);
  padding: 0 20px;
  margin: 30px 0;
  border-radius: 4px;
}
/*
* element 表格
*/
.el-table td, .el-table th{
  text-align: center;
}
/*
* 自定义表格
*/
.tab_main{
  border-top: 1px solid #EBEEF5;
  border-left: 1px solid #EBEEF5;
  width: 100%;
  .th_title{
    border-right: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
    border-top: 1px solid #EBEEF5;
    padding: 12px;
    line-height: 23px;
    color:#909399;
  }
  td{
    padding: 12px;
    line-height: 23px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    font-family: PingFangTC-Semibold, Helvetica, Arial, sans-serif;
    color:#606266;
    text-align: center;
    border-right: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
  }
  .name{
    color: #909399;
  }
}
.item-h1-title {
  font-size: 16px;
  letter-spacing: 1px;
  font-weight: bold;
  padding-bottom: 16px;
}
.detailWrap{
  .list-content {
    width: calc(100% - 40px);
    height: auto;
    border-bottom: 1px solid #dfdfdf;
    padding-top: 20px;
    margin: 0 20px;
    position: relative;
  }


  .item-title{
    text-align:justify ;
    min-width: 60px;
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    line-height: 20px !important;
    color: #666;
    font-family: PingFangTC-Semibold, Helvetica, Arial, sans-serif;
    position: relative;
    &::after {
      display: inline-block;
      width: 100%;
      content: '';
      height: 0;
    }
    &::before{
      position: absolute;
      right:-13px;
      top:0px;
      display: inline-block;
      content: '：';
      width:auto;
      height:auto;
    }
  }
  .item-name{
    text-align:justify ;
    min-width: 60px;
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    line-height: 20px !important;
    color: #666;
    font-family: PingFangTC-Semibold, Helvetica, Arial, sans-serif;
  }
  .item-text{
    font-family: PingFangTC-Semibold, Helvetica, Arial, sans-serif;
    width: auto;
    text-align: left;
    font-size: 14px;
    line-height: 20px;
    margin-left: 18px;
    color: #999;
  }
}
.detailWrap .list-content:last-of-type{
  border-bottom:none;
}
.warning{
  color:#E6A23C !important;
}
.success{
  color:#67C23A !important;
}
.danger{
  color:#999 !important;
  //color:#F56C6C !important;
}
.dangerRed{
  color:#F56C6C ;
}

//Tooltip 文字提示
.el-tooltip__popper.is-light{
  border:solid 1px #979797 !important;
}
.totalText {
  border: solid 1px #9cc9ff;
  height: 40px;
  line-height: 40px;
  color: #777;
  background-color: #edf5fd;
  padding: 0 15px;
  border-radius: 4px;
  * {
    font-size: 14px;
  }
}
.img-wrap {
  display: inline-block;
  width: auto;
  height: auto;
  border: 1px dashed #dfdfdf;
  padding: 10px;
  text-align: center;
  margin-right: 15px;
  border-radius: 4px;
  img {
    width: auto;
    height: auto;
    max-width: 120px;
    max-height: 70px;
    vertical-align: top;
  }
}

.filter-container {
  border: 1px solid #EBEEF5;
  margin-bottom: 20px;
  .filter-box {
    padding: 10px 10px 0 10px;
  }
  .title{
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #EBEEF5;
    font-size: 16px;
    margin-bottom: 10px;
    height: 50px;
  }
}
.filter-container-less {

}

.title-container {
  width: 100%;
}
.backG {
  background: #eee!important;
}
.tab-title {
  .title-container-box{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px;
    //background: #304156;
    border-top: 1px solid #EBEEF5;
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
    height: 50px;
  }
}

// el-image大图预览close
.el-image-viewer__close i {
  font-size: 40px !important;
  color: #fff;
}

