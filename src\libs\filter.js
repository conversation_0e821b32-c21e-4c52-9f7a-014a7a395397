/**
 * 过滤器
 **/

import moment from 'moment'
import basics from '@/libs/basics'
export const substr = (_val, _num, end_num) => {
  const len = _val.length
  const xx = _val.substring(_num, len - end_num)
  const values = _val.replace(xx, '****')
  return _val.length > _num ? values : _val
}

function prefix(num, fill) {
  const len = ('' + num).length
  return Array(fill > len ? fill - len + 1 || 0 : 0).join(0) + num
}

export const formatDate = (stamp, fmt = 'YYYY-MM-dd hh:mm:ss') => {
  if (!stamp || stamp === '0') {
    return '--'
  }
  // const date = new Date(stamp * 1000)
  const date = new Date(stamp)

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substring(4 - RegExp.$1.length))
  }
  const o = {
    'Y+': date.getFullYear(),
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + ''
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : prefix(str, 2))
    }
  }

  return fmt
}

/**
 * 字段值为空处理
 */
export const strDefault = (str, placeholder = '--') => {
  return typeof str == 'undefined' || str == null || str == '' ? placeholder : str
}

export const parseTimeTwo = (time, fmt = 'yyyy-MM-dd hh:mm:ss') => {
  if (time === '-') {
    return time
  }
  return moment(time).format(fmt)
}

export const parseAmount = (val) => {
  if (basics.isNull(val)) {
    return `¥ 0`
  }
  val = val.toString().replace(/\$|\,/g, '')
  if (isNaN(val)) {
    val = '0'
  }
  const sign = (val == (val = Math.abs(val)))
  val = Math.floor(val * 100 + 0.50000000001)
  let cents = val % 100
  val = Math.floor(val / 100).toString()
  if (cents < 10) {
    cents = '0' + cents
  }
  for (var i = 0; i < Math.floor((val.length - (1 + i)) / 3); i++) {
    val = val.substring(0, val.length - (4 * i + 3)) + ',' + val.substring(val.length - (4 * i + 3))
  }

  return '¥ ' + (((sign) ? '' : '-') + val + '.' + cents)
}

export const parseTAmount = (val) => {
  if (basics.isNull(val)) {
    return `0`
  }
  val = val.toString().replace(/\$|\,/g, '')
  if (isNaN(val)) {
    val = '0'
  }
  const sign = (val == (val = Math.abs(val)))
  val = Math.floor(val * 100 + 0.50000000001)
  val = Math.floor(val / 100).toString()
  for (var i = 0; i < Math.floor((val.length - (1 + i)) / 3); i++) {
    val = val.substring(0, val.length - (4 * i + 3)) + ',' + val.substring(val.length - (4 * i + 3))
  }

  return (((sign) ? '' : '-') + val)
}
export function subscriptionPeriodsStr(value) {
  const str = ''
  // str = AppleCycle.find(item => item.value === value)?.label ?? ''
  return str
}
