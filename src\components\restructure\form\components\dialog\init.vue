<template>
  <div>
    <dialogForm
      v-if="show"
      :dialog-footer-state="dialogFooterState"
      :label-width="labelWidth"
      :click-type="clickType"
      :before-update="beforeUpdate"
      :title="title"
      :dialog-form-visible.sync="dialogFormVisible"
      :form-item-list="formItemList"
      :data="data"
      :urls="urls"
      @getSubSuccess="$emit('getSubSuccess',true)"
    />
  </div>
</template>

<script>
import dialogForm from './index'
import dialogFormMixins from './mixins/index'
export default {
  name: 'Init',
  components: {
    dialogForm
  },
  mixins: [dialogFormMixins],
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFormVisible: this.show
    }
  },
  watch: {
    dialogFormVisible(to) {
      if (!to) {
        setTimeout(() => {
          this.$emit('update:show', to)
        }, 500)
      }
    },
    show(to) {
      setTimeout(() => {
        this.dialogFormVisible = to
      })
    }
  }
}
</script>

<style scoped>

</style>
