{
  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,
  "eslint.enable": true,
  "eslint.format.enable": true,

  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  // Disable the default formatter, use eslint instead
  "[vue]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    {
      "rule": "style/*",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "format/*",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-indent",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-spacing",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-spaces",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-order",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-dangle",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-newline",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*quotes",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*semi",
      "severity": "off",
      "fixable": true
    }
  ],
  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "json5",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  // Configuration of i18n i18n-ally
  "i18n-ally.enabledParsers": [
    "json"
  ],
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.localesPaths": [
    "src/locales"
  ],
  "i18n-ally.keystyle": "nested",
  // Markdownlint rules
  "markdownlint.config": {
    "default": true,
    "MD033": false,
    "MD041": false
  },
  "cSpell.words": [
    "oppo",
    "persistedstate"
  ]
}
