<template>
  <div class="role-account">
    <div class="page-container flex">
      <div v-permission="'review'" class="user-left">
        <div class="user-col">
          <div class="t-label">用户管理</div>
        </div>
        <el-button v-permission="'addDepartment'" class="add-btn" type="primary" @click="linkDetails('add',{},'departmentForm')">添加部门</el-button>
        <TreeNode v-for="(item,index) in departmentList" :key="item.id" :is-top-department="index===0" :default-active="curDepartmentId" :item="item" @deleteDepartment="deleteDepartment" />
      </div>
      <div v-permission="'/userAccount/review'" class="user-right">
        <div class="t-label">权限设置</div>
        <userTable :cur-department-id="curDepartmentId" :is-child="true" :parent-department-list="departmentList" />
      </div>
    </div>
    <SDialog :dialog-form-visible.sync="dialogFormVisible1" :data="{width:dialogOps.width,title:`${dialogOps.title}部门`}" :close-on-click-modal="false">
      <editDepartment :form-data="departmentForm" :department-list="departmentList" :type="dialogOps.title==='修改'?'edit':'add'" :roles-list="rolesList" :btn-disabled="btn_disabled" @submit="departmentSubmit" @close="dialogFormVisible1=false" />
    </SDialog>
  </div>
</template>

<script>
import TreeNode from '../modules/departmentTree.vue'
import bus from '@/utils/bus'
import {
  deleteBranch,
  editBranch,
  get_branch_detail
} from '@/api/system'
import { getDepartmentList, getRoleLists } from './useUsers'
import SDialog from '@/components/restructure/dialog'
import editDepartment from '../modules/editDepartment'
import userTable from './userTable.vue'
export default {
  components: {
    SDialog,
    editDepartment,
    userTable,
    TreeNode
  },
  data() {
    return {
      btn_disabled: false,
      dialogFormVisible: false,
      dialogFormVisible1: false,
      tableData: [],
      total: 0,
      pageSize: 10,
      DataLoading: false,
      currentPage: 1,
      dialogOps: {
        width: '480px',
        title: ''
      },
      parameterObj: {
        mobileNo: '',
        role_id: '',
        username: ''
      },
      rolesList: [],
      ruleForm: {
      },
      defaultActive: '',
      departmentList: [],
      curDepartmentId: '',
      departmentForm: {}
    }
  },
  mounted() {
    this.initData()
    bus.$on('treeClick', (type, item) => {
      if (type === 'select') this.curDepartmentId = item.id
      if (type === 'linkDetails') this.linkDetails('edit', item)
      if (type === 'deleteDepartment') this.deleteDepartment(item)
    })
  },
  methods: {
    async initData() {
      this.getRoleList()
      await this.getDepartmentInfo()
      this.curDepartmentId = this.departmentList?.[0]?.id ?? ''
    },

    // 获取部门信息
    async getDepartmentInfo() {
      this.departmentList = await getDepartmentList()
    },

    // 删除部门
    deleteDepartment(item) {
      const { id } = item
      this.$confirm('确认删除此部门？', '提示', { type: 'warning' }).then(() => {
        // 此处删除部门
        deleteBranch(id).then(async res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getDepartmentInfo()
            this.curDepartmentId == id && (this.curDepartmentId = this.departmentList?.[0]?.id ?? '')
          }
        })
      })
    },
    // 部门提交
    departmentSubmit(data) {
      this.btn_disabled = true
      editBranch(data).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: this.dialogOps.title + '成功'
          })
          this.dialogFormVisible1 = false
          this.getDepartmentInfo()
        }
      }).finally(() => {
        this.btn_disabled = false
      })
    },
    // 【打开用户管理】 新增 | 修改
    async  linkDetails(type, item) {
      if (type === 'edit') {
        await get_branch_detail({ branchId: item.id }).then(res => {
          if (res.code == 200) {
            this.departmentForm = res.data
          }
          return res
        }).catch(() => {})
      }
      this.dialogOps.type = type
      if (type === 'add') {
        this.dialogOps.title = type === 'add' ? '新增' : '修改'
        this.departmentForm = { }
      } else {
        this.dialogOps.title = '修改'
      }
      this.dialogFormVisible1 = true
    },

    /**
       * 获取角色
       */
    async getRoleList() {
      this.rolesList = await getRoleLists()
    }
  }
}
</script>

  <style scoped lang="scss">
.role-account{
  .page-title{
    font-size: 16px;
    margin-bottom: 10px;
  }
  .page-container{
    border: 1px solid #dfdfdf;
    display: flex;
  }
  .user-left{
    max-width: 400px;
    min-width: 350px;
    flex-shrink: 0;
  }
  .user-left,.user-right{
    height: 85vh;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .t-label{
    font-size: 16px;
    margin-bottom: 10px;
  }
  .user-right{
    flex-grow: 1;
    padding: 10px ;
    border-left: 1px solid #dfdfdf;
  }
  .user-col{
    min-height: 46px;
    margin: 0 5px;
    display: flex;
    padding: 0 5px;
    justify-content: space-between;
    align-items: center;
    &.active{
      background-color:#409EFF ;
      color: #fff;
    }
  }
  .add-btn{
    display: block;
    width: 120px;
    margin: 20px auto 10px;
  }
  .right-btns{
    display: flex;
    .el-button{
      padding: 5px 5px;
    }
  }
  .cursor-pointer{
    cursor: pointer;
  }
  .flex {
    display: flex;
    align-items: center;
    &.justify-bettwen{
      justify-content: space-between;
    }
  }
  .el-menu-vertical-demo{
    ::v-deep .el-submenu__icon-arrow{
      right: 96%;
      left:0;
      color: #333;
      font-size:16px
    }
    ::v-deep .el-menu-item.is-active{
      background: rgba(64, 158, 255,0.2);
    }
  }
}
  </style>
