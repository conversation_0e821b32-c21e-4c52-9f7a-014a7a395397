<template>
  <div class="ip-whitelist">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <span>IP白名单</span>
        <el-switch
          v-model="globalStatus"
          :active-value="1"
          :inactive-value="0"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="启用"
          inactive-text="禁用"
          @change="handleStatusChange"
        />
      </div>
      <!-- 新增IP输入框 -->
      <div class="add-ip-section">
        <el-input
          v-model="newIp"
          placeholder="请输入IP地址"
          class="add-ip-input"
        >
          <el-button slot="append" type="primary" @click="handleAddIp">添加</el-button>
        </el-input>
      </div>
      <div v-loading="loading" class="ip-list">
        <div v-for="(ip, index) in ipData.ips" :key="ip.id" class="ip-item">
          <template v-if="editingIndex === index">
            <el-input v-model="editingIp" class="edit-ip-input" />
            <div class="ip-actions">
              <el-button type="text" @click="handleSaveEdit">确认</el-button>
              <el-button type="text" @click="handleCancelEdit">取消</el-button>
            </div>
          </template>
          <template v-else>
            <span class="ip-address">{{ ip.ipAddress }}</span>
            <div class="ip-actions">
              <el-button type="text" @click="handleEdit(ip, index)">编辑</el-button>
              <el-button type="text" @click="handleDelete(ip.id)">删除</el-button>
            </div>
          </template>
        </div>
        <div v-if="!ipData.ips || ipData.ips.length === 0" class="empty-tip">
          暂无IP白名单数据
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getIpWhiteList, updateIpWhiteList, deleteIpWhiteList, getIpWhiteStatusApi, updateIpWhiteStatusApi } from '@/api/system'

export default {
  name: 'IpWhitelist',
  data() {
    return {
      loading: false,
      ipData: {
        ips: [],
        status: 1
      },
      globalStatus: 0,
      newIp: '',
      editingIndex: -1,
      editingIp: '',
      editingId: null,
      ipId: null
    }
  },
  created() {
    this.getIpWhiteStatus()
    this.fetchIpList()
  },
  methods: {
    // 获取IP列表
    async fetchIpList() {
      try {
        this.loading = true
        const res = await getIpWhiteList()
        if (res.code === 200) {
          this.ipData.ips = res.data || []
        } else {
          this.$message.error(res.msg || '获取IP白名单失败')
        }
      } catch (error) {
        console.error('获取IP白名单失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 获取状态
    async getIpWhiteStatus() {
      try {
        const { data } = await getIpWhiteStatusApi()
        this.globalStatus = data.status
        this.ipId = data.id
      } catch (err) {
        console.log(err)
      }
    },
    // 处理状态变更
    async handleStatusChange(status) {
      try {
        const res = await updateIpWhiteStatusApi({
          id: this.ipId,
          status: status
        })
        if (res.code === 200) {
          this.$message.success('更新成功')
          this.fetchIpList()
        }
      } catch (error) {
        this.$message.error('更新失败')
        // 恢复原状态
        this.globalStatus = !status
      }
    },
    // 添加IP
    async handleAddIp() {
      if (!this.newIp) {
        this.$message.warning('请输入IP地址')
        return
      }

      // 验证IP是否已存在
      if (this.ipData.ips.includes(this.newIp)) {
        this.$message.warning('该IP已存在')
        return
      }

      try {
        const res = await updateIpWhiteList({
          id: this.ipId,
          ipAddress: this.newIp
        })

        if (res.code === 200) {
          this.$message.success('添加成功')
          this.newIp = ''
          this.fetchIpList()
        }
      } catch (error) {
        this.$message.error('添加失败')
      }
    },
    // 开始编辑
    handleEdit(ip, index) {
      this.editingIndex = index
      this.editingId = ip.id
      this.editingIp = ip.ipAddress
    },
    // 保存编辑
    async handleSaveEdit() {
      if (!this.editingIp) {
        this.$message.warning('IP地址不能为空')
        return
      }

      // 验证IP是否已存在
      const existingIndex = this.ipData.ips.findIndex((ip, index) =>
        index !== this.editingIndex && ip === this.editingIp
      )
      if (existingIndex !== -1) {
        this.$message.warning('该IP已存在')
        return
      }

      try {
        const newIps = [...this.ipData.ips]
        newIps[this.editingIndex] = this.editingIp

        const res = await updateIpWhiteList({
          id: this.editingId,
          ipAddress: this.editingIp
        })

        if (res.code === 200) {
          this.$message.success('修改成功')
          this.editingIndex = -1
          this.editingIp = ''
          this.editingId = null
          this.fetchIpList()
        }
      } catch (error) {
        this.$message.error('修改失败')
      }
    },
    // 取消编辑
    handleCancelEdit() {
      this.editingIndex = -1
      this.editingIp = ''
      this.editingId = null
    },
    // 删除IP
    async handleDelete(id) {
      try {
        const res = await deleteIpWhiteList(id)

        if (res.code === 200) {
          this.$message.success('删除成功')
          this.fetchIpList()
        }
      } catch (error) {
        this.$message.error('删除失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ip-whitelist {
  padding: 20px;
  display: flex;
  justify-content: center;

  .box-card {
    width: 500px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .add-ip-section {
      margin-bottom: 16px;

      .add-ip-input {
        width: 100%;
      }
    }

    .ip-list {
      height: 500px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 16px 0;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }

      .ip-item {
        padding: 12px 20px;
        background-color: #f5f7fa;
        border-radius: 4px;
        transition: all 0.3s;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: #ecf5ff;
        }

        .ip-address {
          font-size: 14px;
          color: #606266;
        }

        .edit-ip-input {
          width: 300px;
        }

        .ip-actions {
          display: flex;
          gap: 8px;

          .el-button {
            padding: 0 5px;

            &:first-child {
              color: #409EFF;
            }

            &:last-child {
              color: #F56C6C;
            }
          }
        }
      }

      .empty-tip {
        text-align: center;
        color: #909399;
        font-size: 14px;
        padding: 32px 0;
      }
    }
  }
}
</style>
