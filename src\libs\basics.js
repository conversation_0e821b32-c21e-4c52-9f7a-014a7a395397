const basics = {
  install(Vue) {
    Vue.prototype.basics = basics
  },
  isNull(o) {
    return (o === null || o === '' || o === undefined)
  },
  isObj(o) { // 是否对象
    return Object.prototype.toString.call(o).slice(8, -1) === 'Object'
  },
  isObjNull(o) {
    return Object.keys(o).length === 0
  },
  isArray(o) { // 是否数组
    return (Object.prototype.toString.call(o) === '[object Array]')
  },
  isFunction(o) { // 是否函数
    return Object.prototype.toString.call(o).slice(8, -1) === 'Function'
  },
  isString(o) { // 是否字符串
    return Object.prototype.toString.call(o).slice(8, -1) === 'String'
  },
  isFile(o) { // 是否文件
    return Object.prototype.toString.call(o).slice(8, -1) === 'File'
  },
  isBoolean(o) {
    return Object.prototype.toString.call(o).slice(8, -1) === 'Boolean'
  },
  isArrNull(o) {
    return (JSON.stringify(o) === '[]')
  },
  isArrStr(arr) {
    var i = arr.length
    while (i--) {
      if (this.isString(arr[i])) {
        return true
      }
    }
    return false
  },
  isNumber(o) {
    return Object.prototype.toString.call(o).slice(8, -1) === 'Number'
  },
  objMosaic() { // 对象拼接
    var obj = {}
    for (var i = 0; i < arguments.length; i++) {
      for (var key in arguments[i]) {
        obj[key] = arguments[i][key]
      }
    }
    return obj
  },
  Array: {
    remove(arr, remove) { // 根据内容或下标删除
      for (var i = 0; i < arr.length; i++) {
        var el = arr[i]
        if (basics.isNumber(remove)) {
          el = i
        }

        if (el === remove) {
          for (var a = i; a < arr.length; a++) {
            arr[a] = arr[a + 1]
          }

          arr.length = arr.length - 1
        }
      }
      return arr
    }
  },
  Objcet: {
    delete(obj, str) {
      if (basics.isArray(str)) {
        str.forEach(item => {
          return this.delete(obj, item)
        })
      } else {
        if (!basics.isNull(obj) && !basics.isNull(obj[str])) {
          delete obj[str]
        }
      }
      return obj
    }
  },
  tools: {
    trim(str) { // 空格取消
      if (basics.isString(str)) {
        return str.replace(/(^\s*)|(\s*$)/g, '')
      }
      return str
    }
  },
  pickerOptions: (day = 7) => {
    let choiceDate = null
    return {
      onPick: ({ maxDate, minDate }) => {
        choiceDate = minDate.getTime()
        if (maxDate) choiceDate = ''
      },
      disabledDate: time => {
        if (choiceDate) {
          const one = day * 24 * 3600 * 1000
          const minTime = choiceDate - one
          const maxTime = choiceDate + one
          return (
            time.getTime() < minTime ||
            time.getTime() > maxTime ||
            time.getTime() > Date.now()
          )
        } else {
          return time.getTime() > Date.now()
        }
      }
    }
  }

}

export default basics
