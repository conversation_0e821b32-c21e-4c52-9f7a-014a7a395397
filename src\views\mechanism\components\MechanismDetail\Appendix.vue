<template>
  <div v-loading="!Object.keys(mechanismInfo).length" class="appendix">
    <Descriptions title="企业信息">
      <DescriptionsItem label="营业执照">
        <el-image class="appendix-image" :src="mechanismInfo.companyLicense" :preview-src-list="[mechanismInfo.companyLicense]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </DescriptionsItem>
      <DescriptionsItem label="办公场地视频">
        <video class="appendix-image" controls :src="mechanismInfo.companyOfficeVideo" />
      </DescriptionsItem>
      <DescriptionsItem label="前台logo照片">
        <el-image class="appendix-image" :src="mechanismInfo.companyLogo" :preview-src-list="[mechanismInfo.companyLogo]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </DescriptionsItem>
    </Descriptions>
    <Descriptions title="法人信息" style="margin-top: 20px">
      <DescriptionsItem label="法人身份证人像面">
        <el-image class="appendix-image" :src="mechanismInfo.legalCardFront" :preview-src-list="[mechanismInfo.legalCardFront]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </DescriptionsItem>
      <DescriptionsItem label="法人身份证国徽面">
        <el-image class="appendix-image" :src="mechanismInfo.legalCardBack" :preview-src-list="[mechanismInfo.legalCardBack]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </DescriptionsItem>
    </Descriptions>
    <div class="el-descriptions__title" style="margin-top: 20px">附件资料</div>
    <div style="margin-top: 20px">
      <div v-if="mechanismInfo.attachment && mechanismInfo.attachment.length" style="display: flex;gap: 10px;flex-wrap: wrap">
        <div v-for="item in mechanismInfo.attachment.split(',')" :key="item" class="appendix-image">
          <!--          渲染图片-->
          <el-image v-if="getName(item).ext !== 'pdf'" :src="item" class="appendix-image" :preview-src-list="[...filterPdfFile(mechanismInfo.attachment.split(','))]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
          <!--          渲染pdf-->
          <div v-else class="pdf-block" @click="openPdf(item)">
            <div class="txtMp"><span>{{ getName(item).nametxt1 }}</span>{{ getName(item).nametxt2 }}.{{ getName(item).ext }}</div>
          </div>
        </div>
      </div>
      <div v-else>暂未上传附件资料</div>
    </div>
  </div>
</template>

<script>
import Descriptions from '@/components/Descriptions'
import DescriptionsItem from '@/components/DescriptionsItem'

export default {
  name: 'Appendix',
  components: {
    Descriptions,
    DescriptionsItem
  },
  props: {
    mechanismInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    getName(url) {
      let name = url.split('/').at(-1)
      name = name.split('?')[0]
      const nametxt = name.split('.')[0]
      const ext = name.split('.')[1]
      return {
        nametxt1: nametxt.slice(0, nametxt.length - 3),
        nametxt2: nametxt.slice(nametxt.length - 3, nametxt.length),
        ext
      }
    },
    openPdf(url) {
      window.open(url)
    },
    // 附件资质生成大图预览
    filterPdfFile(list = []) {
      return list.filter(item => this.getName(item).ext !== 'pdf')
    }
  }
}
</script>
<style>
.image-slot{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
  i{
    font-size: 30px;
  }
}
</style>
<style scoped lang="scss">
.appendix{
  .appendix-image{
    width: 250px;
    height: 150px;
  }
  .pdf-block{
    width: 250px;
    height: 150px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      background-color: #999;
    }
  }
  ::v-deep .el-descriptions {
    .el-descriptions-item__label{
      min-width: 100px;
    }
  }
}
</style>
