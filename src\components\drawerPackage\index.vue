<!--
 * @Author: 陈小豆
 * @Date: 2024-06-04 09:29:14
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-04 09:31:51
-->
<template>
  <el-drawer
    v-if="drawer"
    :visible.sync="drawer"
    direction="rtl"
    size="50%"
    :with-header="false"
    :wrapper-closable="false"
  >
    <div class="close_button">
      <i class="el-icon-close" @click="drawer=false" />
    </div>
    <div class="drawer_package">
      <div class="drawer_title">
        <span>模型编辑/新增</span>
      </div>
      <div class="addForm_package" />
    </div>
    <slot />
  </el-drawer>
</template>

<style scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    position: relative;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
::v-deep .el-drawer__body{

}
.close_button{
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0,1);
    text-align: center;
    cursor: pointer;
i{
  color: white;
  line-height: 40px;
}
  }
.drawer_package{
  height: 100%;
  position: relative;

  .drawer_title{
    padding: 10px 20px 5px;
    vertical-align: middle;
    span{
      font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
    }

}
}

.addForm_package{
  background-color: rgb(189, 184, 184,0.1);
  padding: 15px;
  height: 100%;
  .demo-ruleForm{
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;
    .view_button{
      position: absolute;
      bottom: 0;
    }
  }
}
.view_button{
          background-color: #ffffff;
          padding: 20px;
          border-top: 1px dashed #000000;
          ::v-deep .el-button{
            margin: 0 10px;
          }
        }
.form_view{
        // margin: 0 0px 20px;
        background-color: #ffffff;
        // border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;

        .form_view_title{
            margin-bottom: 20px;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
</style>
