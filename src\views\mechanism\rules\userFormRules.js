import { validPassword, validPhone } from '@/utils/formValidationRules'

export const rules = {
  nickName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  mobileNo: [
    { required: true, validator: validPhone, trigger: 'blur' }
  ],
  userName: [
    { required: true, message: '请输入登录名', trigger: 'blur' }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, validator: validPassword, trigger: 'blur' }
  ]
}
