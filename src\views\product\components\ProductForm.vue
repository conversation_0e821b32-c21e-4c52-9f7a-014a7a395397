<template>
  <FormDialog
    ref="dialogFormRef"
    v-model="dialogVisible"
    :title="formId ? '编辑产品' : '新增产品'"
    :form-model="productForm"
    width="30%"
    label-width="170px"
    :rules="rules"
    @submit="handleSubmit"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form-item label="产品名称" prop="productName">
      <el-input v-model="productForm.productName" clearable :maxlength="30" placeholder="请输入产品名称" />
    </el-form-item>
    <el-form-item label="产品描述" prop="productDescribe">
      <el-input v-model="productForm.productDescribe" type="textarea" resize="none" clearable :maxlength="200" placeholder="请输入产品描述" />
    </el-form-item>
    <el-form-item label="产品报价(元)" prop="productPrice">
      <el-input-number v-model="productForm.productPrice" :precision="2" :min="0.01" :max="99999999" style="width: 100%" />
    </el-form-item>
    <el-form-item label="是否在抵抵押车" prop="carLoanStatus">
      <el-select v-model="productForm.carLoanStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="二要素校验是否通过" prop="twoElementsStatus">
      <el-select v-model="productForm.twoElementsStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="三要素校验是否通过" prop="threeElementsStatus">
      <el-select v-model="productForm.threeElementsStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="人车合一校验是否通过" prop="carPersonStatus">
      <el-select v-model="productForm.carPersonStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否有车牌" prop="carNumberStatus">
      <el-select v-model="productForm.carNumberStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="车牌有效校验是否通过" prop="carNumberValidityStatus">
      <el-select v-model="productForm.carNumberValidityStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否有车300报告" prop="carReportStatus">
      <el-select v-model="productForm.carReportStatus" style="width: 100%">
        <el-option
          v-for="item in YesNoList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
  </FormDialog>
</template>

<script>
import FormDialog from '@/components/FormDialog/index.vue'
import { YesNoList } from '@/enum'
import rules from '../rules/productFormRules'
import { createProductApi, editProductApi, getProductInfoApi } from '@/api/product'

const productFormState = {
  productName: '',
  productDescribe: '',
  productPrice: 0,
  carLoanStatus: null,
  twoElementsStatus: null,
  threeElementsStatus: null,
  carPersonStatus: null,
  carNumberStatus: null,
  carNumberValidityStatus: null,
  carReportStatus: null
}

export default {
  name: 'ProductForm',
  components: {
    FormDialog
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    formId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      productForm: { ...productFormState }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    YesNoList() {
      return YesNoList
    },
    rules() {
      return rules
    }
  },
  methods: {
    handleClose() {
      this.productForm = { ...productFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    handleOpen() {
      this.formId && this.$nextTick(() => {
        this.$refs.dialogFormRef.loadFormData(() => {
          return getProductInfoApi(this.formId)
        })
      })
    },
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editProductApi({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await createProductApi(params)
      }
    },
    handleSubmit(params, done) {
      this.$confirm('确认保存此产品？', '提示', {
        closeOnClickModal: false
      }).then(async() => {
        try {
          const { code } = await this.useHttpInterface(params)
          if (code !== 200) return false
          this.$message.success('操作成功')
          this.handleClose()
        } catch (err) {
          console.log(err)
        } finally {
          done()
        }
      }).catch(() => {
        done()
      })
    }
  }
}
</script>
