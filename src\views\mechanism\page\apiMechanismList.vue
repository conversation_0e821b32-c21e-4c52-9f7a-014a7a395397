<template>
  <div>
    <page :request="request" :list="list" table-title="API机构" />
    <MechanismDetail :id="mechanismId" v-model="drawerShow" />
  </div>
</template>

<script lang="jsx">
import page from '@/components/restructure/page/index.vue'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { MechanismTypeList } from '../enum'
import MechanismDetail from '../components/MechanismDetail/MechanismDetail.vue'
import { getApiMechanismListApi } from '@/api/mechanism'
import { hasPermission } from '@/utils/menuCodes'
import moment from 'moment/moment'

export default {
  name: 'apiMechanismList',
  components: {
    page,
    MechanismDetail
  },
  data() {
    return {
      mechanismId: null,
      drawerShow: false,
      listQuery: {
        createTimeStart: '',
        createTimeEnd: '',
        updateTimeStart: '',
        updateTimeEnd: '',
        // 机构信息：信用代码、名称、姓名、手机号
        keywords: '',
        // 机构类型
        agencyType: ''
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getApiMechanismListApi(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '机构信息',
          key: 'keywords',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '输入信用代码/名称'
          }
        },
        {
          title: '机构类型',
          key: 'agencyType',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismTypeList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '创建时间',
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['createTimeStart', 'createTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '更新时间',
          key: 'updateDate',
          type: formItemType.rangeDatePicker,
          childKey: ['updateTimeStart', 'updateTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '机构ID',
          key: 'id',
          width: 80,
          render: (_h, params) => {
            if (!hasPermission({ buttons: 'detail' })) {
              return <div>{params.data.row.id}</div>
            }
            return <el-button type='text' onClick={() => this.handleOpenDetail(params.data.row)}>{params.data.row.id}</el-button>
          }
        },
        {
          title: '统一社会信用代码',
          key: 'companyCreditCode'
        },
        {
          title: '机构名称',
          key: 'agencyName'
        },
        {
          title: '机构类型',
          key: 'agencyType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismTypeList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '是否撞库',
          key: 'cooperateIsMatch',
          render: (_h, params) => {
            return <span>{params.data.row.cooperateIsMatch === 1 ? '是' : '否'}</span>
          }
        },
        {
          title: '撞库地址',
          key: 'matchUrl'
        },
        {
          title: '通知地址',
          key: 'notifyUrl'
        },
        {
          title: '订单总数',
          key: 'orderTotalNum'
        },
        {
          title: '回调成功总数',
          key: 'callbackSuccessNum'
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 150,
          render: (_h, params) => {
            return <div>{moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm')}</div>
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 150,
          render: (_h, params) => {
            return <div>{moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm')}</div>
          }
        }
      ]
    }
  },
  methods: {
    // 打开详情
    handleOpenDetail(row) {
      this.mechanismId = row.id
      this.drawerShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
