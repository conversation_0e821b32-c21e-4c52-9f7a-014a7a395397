<template>
  <div style="height:100%;">
    <div id="leftMenue" class="menu">
      <el-scrollbar wrap-class="menu-wrap" style="height:100%">
        <el-menu
          ref="leftMenue"
          class="yc-menu-wrap el-menu-vertical-demo"
          @open="handleOpen"
          @close="handleClose"
          text-color="#656f7d"
          :active-text-color="activeColor"
          :unique-opened="true"
          :show-timeout="10"
          :hide-timeout="10"
          :active="navselected"
          :default-active="navselected"
          :default-openeds="openeds"
        >
          <!--首页不作权限控制-->
          <el-menu-item
            index="/home"
            key="/home"
            @click="handleRouter({ path: '/home' })"
          >
            <i class="el-icon-s-home"></i>
            <span style="font-size: 14px" slot="title" class="one-title"
              >首页</span
            >
          </el-menu-item>
          <!--一级菜单目录-->
          <template v-for="(one, index) in data">
            <template v-if="one.children">
              <el-submenu
                v-if="data && one.children.length > 0"
                :index="one.path"
                :key="index"
                @click="handleRouter(one)"
              >
                <template slot="title">
                  <i :class="one.icon"></i>
                  <span slot="title" class="one-title">{{ one.title }}</span>
                </template>
                <template v-for="(two, t_index) in one.children">
                  <template v-if="two.children">
                    <!-- 有三级菜单 -->
                    <el-submenu :index="two.path" :key="t_index">
                      <!-- 二级 -->
                      <template slot="title">
                        <span class="two-title">{{ two.title }}</span>
                      </template>
                      <!-- 三级 -->
                      <template v-for="(three, t_index) in two.children">
                        <el-menu-item
                          :index="three.path"
                          :key="t_index"
                          @click="handleRouter(three)"
                        >
                          <span slot="title" class="three-title">{{
                            three.title
                          }}</span>
                        </el-menu-item>
                      </template>
                    </el-submenu>
                  </template>
                  <!-- 无三级菜单 直接显示二级菜单-->
                  <template v-else>
                    <el-menu-item
                      :index="two.path"
                      :key="two.path"
                      @click="handleRouter(two)"
                    >
                      <span slot="title" class="two-title">{{
                        two.title
                      }}</span>
                    </el-menu-item>
                  </template>
                </template>
              </el-submenu>
            </template>
            <template v-else>
              <!--无二级菜单目录-->
              <el-menu-item
                :index="one.path"
                :key="one.path"
                @click="handleRouter(one)"
              >
                <span slot="title" class="one-title">{{ one.title }}</span>
              </el-menu-item>
            </template>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
export default {
  name: "yc-menu",
  data() {
    return {
      activeColor: "#3E96F0",
      navselected: "",
      openeds: []
    };
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      }
    },
    defaultActive: {
      type: String,
      default: "1000"
    }
  },
  watch: {
    $route: function(to, from) {
      let thar = this;
      this.activeColor = "#3E96F0";
      thar.navselected = to.meta.activeMenu ? to.meta.activeMenu : to.path;
      if (this.$utils.includeValue(this.$CONSTANT.noLoginMenu, to.path)) {
        thar.openeds = [];
        if (to.path !== "/home") {
          thar.activeColor = "#B7C3D1";
        }
      }
    }
  },
  mounted() {
    this.navselected = this.$route.path;
  },
  computed: {},
  methods: {
    handleRouter(item, type) {
      let thar = this;
      this.$router.push({
        path: item.path || item.uri
      });
      if (type === "parent") {
        thar.openeds = [];
      }
    },
    handleOpen() {},
    handleClose() {}
  }
};
</script>

<style lang="scss" scoped>
.yc-menu-wrap {
  border-right: none;
  /*min-height: 91vh;*/
}

#leftMenue {
  /*height: auto;*/
  /*max-height:calc(100% - 60px);*/
  /*border:solid 1px red;*/
}

.logo-title {
  width: auto;
  height: 40px;
  margin: 9px 20px;
  cursor: pointer;
}
.one-title {
  font-size: 14px;
  font-weight: bold;
  color: rgb(101, 111, 125);
  /*color:rgb(101, 111, 125);*/
}
.two-title {
  padding-left: 12px;
  font-size: 14px;
  /*color: rgb(69, 76, 85);*/
}
.three-title {
  font-size: 13px;
}
.menu {
  height: 100%;
  & ::v-deep.menu-wrap {
    overflow-x: hidden !important;
  }
}
</style>
