<template>
  <div>
    <Page :request="request" :list="list" table-title="数据概览">
      <div slot="titleContainer" class="title-container-box">
        <el-tabs v-model="listQuery.type" @tab-click="handleClick">
          <el-tab-pane
            v-for="(item, index) in tabs"
            :key="index"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <template v-slot:tableFooter>
        <div class="table-footer">总充值{{ tableData.totalRecharge }}元，总消耗{{ tableData.totalConsume }}元，推送{{ tableData.totalPush }}条</div>
      </template>
    </Page>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
export default {
  components: {
    Page
  },
  props: {
    type: {
      type: String,
      default: 'list'
    },
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabs: [{
        value: '0',
        label: '综合'
      },
      {
        value: '1',
        label: '1类'
      },
      {
        value: '2',
        label: '2类'
      },
      {
        value: '3',
        label: '3类'
      }
      ],
      listQuery: {
        startDate: this.detailInfo.startDate || moment(new Date(new Date().getFullYear(), new Date().getMonth(), 1)).format('YYYYMMDD'),
        endDate: this.detailInfo.endDate || moment().format('YYYYMMDD'),
        type: this.detailInfo?.type || '0',
        institution: this.detailInfo.institution || ''
      },
      tableData: {},
      request: {
        getListUrl: async data => {
          await new Promise((resolve, reject) => {
            setTimeout(() => {
              resolve({ totalRecharge: 0, totalConsume: 0, totalPush: 0 })
            })
          }).then(res => {
            this.tableData = res
          })
          return {
            data: {
              total: 0,
              rows: []
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '日期范围',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          search: true,
          val: [this.listQuery.startDate, this.listQuery.endDate],
          tableHidden: true
        },
        {
          title: '总充值',
          key: 'channelCode'
        },
        {
          title: '总消耗',
          key: 'channelCode1'
        },
        {
          title: '推送总数',
          key: 'apiCode'
        },
        {
          title: '平均客单价',
          key: 'landingPageId'
        },
        {
          title: '拒量数',
          key: 'newUser'
        },
        this.type === 'list' && {
          title: '操作',
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 160,
          fixed: 'right',
          activeType: [
            {
              text: '明细',
              key: 'detail',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$emit('goDetail', { ...params, ...this.listQuery, startDate: params.date, endDate: '' })
              }
            }
          ]
        }
      ]
    }

  },
  methods: {
    handleClick() {
      setTimeout(() => {
        this.$store.dispatch('tableRefresh', this)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title-container-box{
  justify-content: flex-start;
  ::v-deep .el-tabs__nav-wrap::after{
    background-color: transparent;
  }
}
.table-footer{
  margin-top: 15px;
}
</style>
