<template>
  <div class="screenfull-icon-wrap">
    <img @click="handleScreenFull" style="cursor: pointer;" :src="require(`@/assets/svg/${isScreenFull}_screenfull.svg`)"/>
  </div>
</template>

<script>
  import screenfull from 'screenfull'

  export default {
    data() {
      return {
        isScreenFull: 'open', //close
      }
    },
    methods: {
      handleScreenFull() {
        screenfull.toggle();
        return this.isScreenFull = !screenfull.isFullscreen ? 'close' : 'open';
      }
    }
  }
</script>
<style lang="scss" scoped>
  .screenfull-icon-wrap {
    display: inline-block;
    position: relative;
    width: 20px;
    height: 50px;
    vertical-align: middle;
    > img {
      position: absolute;
      width: 18px;
      height: 18px;
      top: 14px;

    }

  }
</style>
