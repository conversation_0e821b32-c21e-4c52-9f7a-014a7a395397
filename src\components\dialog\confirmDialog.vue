<template>
  <div :class="[options.className]" :style="{'--zIndex':options.customModal && options.customModalIndex?options.customModalIndex:1000}">
    <div v-if="options.customModal" class="modal" />
    <el-dialog
      :class="[!Object.prototype.hasOwnProperty.call(options, 'title') && 'notitle']"
      :title="options.title || '提示'"
      :before-close="handleClose"
      :visible.sync="centerDialogVisible"
      :width="options.width || '600px'"
      :modal="options.customModal?false:true"
      :modal-append-to-body="options.appendToBody?options.appendToBody:false"
      :show-close="Object.prototype.hasOwnProperty.call(options, 'showClose')?options.showClose:true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <component :is="customComponent" :params="params" :is-component="true" @watchDialogClose="watchDialogCloseFn" @action="actionFn" />
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'confirmDialog',
  props: {
    params: {
      type: Object,
      default: () => { }
    },
    options: {
      type: Object,
      default: () => { }
    },
    customComponent: {
      type: [Object, Function],
      default: () => { }
    }
  },
  data() {
    return {
      centerDialogVisible: false
    }
  },
  created() {
    setTimeout(() => {
      this.centerDialogVisible = true
    }, 50)
  },
  methods: {
    watchDialogCloseFn(cb) {
      if (cb) {
        this.dialogClose = cb
      }
    },
    handleClose() {
      this.actionFn('dialogClose')
    },
    actionFn() {
      this.dialogClose && this.dialogClose(true)
      // this.centerDialogVisible = false
      this.$emit('action', ...arguments)
    }
  }
}

</script>
<style lang="scss" scoped>
.modal{
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: var(--zIndex);
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
::v-deep .el-dialog {
  top: 50%;
  margin-top: 0 !important;
  transform: translateY(-50%);

  .el-dialog__header {
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1);
  }

  .el-dialog__body {
    max-height: calc(95vh - 60px);
    overflow: auto;
  }
}
::v-deep .notitle{
  .el-dialog__header{
   display: none;
  }
 }
</style>
