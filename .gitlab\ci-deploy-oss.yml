#定义执行步骤
stages:
  - build
  - deploy


发布:
  stage: build
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  retry:
    max: 2
    when:
      - script_failure
      - job_execution_timeout
  script:
    - |-
      
    - |-
      echo "------------------------------开始构建------------------------------"
      echo ${VUE_BUILD}:${DEPLOY_ENV}
      ${VUE_BUILD}:${DEPLOY_ENV}
      if [ $? == 0 ];then
        echo "------------------------------构建成功------------------------------"
      else
        echo "------------------------------构建失败------------------------------"
      fi 
      echo ${CI_JOB_STATUS}

    - |-
      echo "------------------------------上传OSS------------------------------"
      ossutil64  config -e oss-cn-hangzhou.aliyuncs.com -i $AccessKeyID -k $AccessKeySecret
      ossutil64 sync -f --exclude=index.html  dist/ oss://$OSS_BUCKET
      echo "已同步文件资源"
      ossutil64 sync -f --include=index.html  dist/ oss://$OSS_BUCKET
      echo "设置元属性"
      ossutil64 set-meta oss://$OSS_BUCKET/index.html Cache-Control:no-cache --update



