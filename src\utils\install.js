
export default {
  install(Vue, router, store) {
    Vue.prototype.$getQueryVariable = (variable) => {
      var vars = []
      // 如果存在#号
      if (window.location.hash != '') {
        // 取消#号
        const hashUrk = window.location.hash.replace('#', '')
        // 取值
        var query0 = hashUrk.includes('?') ? hashUrk.split('?')[1] : hashUrk
        const queryArray = query0.split('&').map(item => {
          if (item.includes('=')) {
            return item
          } else {
            return '#' + item + '=' + item
          }
        })
        if (query0) vars.push(...queryArray)
      }
      // 如果存在参数
      if (window.location.search != '') {
        var query = window.location.search.substring(1)
        if (query) vars.push(...query.split('&'))
      }
      const queryObject = {}
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split('=')
        queryObject[pair[0]] = pair[1]
      }
      if (variable) {
        if (queryObject[variable] && queryObject[variable] != '') {
          return queryObject[variable]
        } else {
          return false
        }
      } else {
        return queryObject
      }
    }
    Vue.prototype.$confirmDialog = async optionsData => {
      if (!window.confirmDialogObject) window.confirmDialogObject = {}
      const customDialogId = parseInt(Math.random() * 10000000) + '_' + new Date().getTime()
      const { component, params = {}, beforeClose = null, options = {}} = optionsData
      const { position = 'center' } = options
      let com = null
      if (position === 'center') {
        com = (await import('@/components/dialog/confirmDialog.vue')).default
      } else {
        com = (await import('@/components/dialog/confirmDrawer.vue')).default
      }
      const CustomDialog = new (Vue.extend(com))({
        el: document.createElement('div'),
        store,
        router,
        propsData: {
          params,
          options,
          customComponent: component
        }
      })
      document.body.appendChild(CustomDialog.$el)
      return new Promise((resolve) => {
        const close = (returnData = null) => {
          CustomDialog.centerDialogVisible = false
          setTimeout(() => {
            CustomDialog.$el.parentNode.removeChild(CustomDialog.$el)
          }, 100)
          resolve(returnData || actionObject)
        }
        let actionObject = {}
        // 监听方法：
        CustomDialog.$on('action', (res, data = null) => {
          actionObject = { action: res, params: data, closeId: customDialogId }
          if (beforeClose) {
            beforeClose({ ...actionObject, close, optionsInit: optionsData })
          } else {
            close()
          }
        })
        window.confirmDialogObject[customDialogId] = () => {
          const returnData = { ...actionObject, action: 'allClose' }
          close(returnData)
        }
      })
    }
    Vue.prototype.$downloadFile = (content, fileName = '') => {
      if (content instanceof Array) {
        content.forEach(item => {
          const aLink = document.createElement('a')
          aLink.download = fileName
          aLink.setAttribute('href', item)
          // a.href = url
          aLink.click()
        })
      } else {
        const aLink = document.createElement('a')
        aLink.download = fileName;
        aLink.setAttribute('href', content)
        // a.href = url
        aLink.click()
      }
    }
  }
}
