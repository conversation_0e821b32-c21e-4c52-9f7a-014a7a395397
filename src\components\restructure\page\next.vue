<script>
import Page from './index.vue'

function normalizeProps(vm, keys) {
  const { list, ...otherProps } = vm.props
  const listShow = filterTableHidden(list, keys)

  return {
    on: vm.listeners,
    props: { ...otherProps, list: listShow },
    scopedSlots: vm.scopedSlots
  }
}

function flat(data) {
  return data.reduce((pre, cur) => {
    const { children = [], ...i } = cur
    const childrenArr = children.map((child) => ({ ...child, isFamily: 2 }))
    return pre.concat(
      [{ ...i, ...(children.length > 0 ? { isFamily: 1 } : {}) }],
      flat(childrenArr)
    )
  }, [])
}

function getSetting(
  vm,
  all = false,
  pathPrefix = '@setting',
  isExpanded = false
) {
  if (!vm.props?.list) {
    return
  }

  const path = pathPrefix + vm.parent?.$route?.path
  const settingStr = localStorage.getItem(path)

  if (isExpanded) {
    return settingStr ? JSON.parse(settingStr) : []
  }

  if (!all && settingStr) {
    return JSON.parse(settingStr)
  }
  if (!all) {
    return flat(vm.props.list)
      .map((item) => !item.tableHidden && !item.isFamily && item.key)
      .filter((item) => item)
  }
  return flat(vm.props.list)
    .map((item) => !item.tableHidden && item.key)
    .filter((item) => item)
}

function setSetting(vm, setting, pathPrefix = '@setting') {
  if (!vm.props?.list) {
    return
  }
  const path = pathPrefix + vm.parent?.$route?.path
  if (
    (pathPrefix === '@setting' && setting.length === 0) ||
    !Array.isArray(setting)
  ) {
    return new Error('setting is empty')
  }
  localStorage.setItem(path, JSON.stringify(setting))
}

function resetSetting(vm, pathPrefix = '@setting') {
  const path = pathPrefix + vm.parent?.$route?.path
  localStorage.removeItem(path)
}

/**
 * 树形数组，过滤
 */
function filterTreeData(data, setting) {
  return data.reduce((pre, cur) => {
    if (cur.children) {
      cur.children = filterTreeData(cur.children, setting)
    }

    if (setting.includes(cur.key)) {
      return pre.concat([cur])
    }
    return pre
  }, [])
}
/**
 * 树形数组，根据过滤数组，对应数据添加tableHidden属性
 */
function filterTableHidden(data, setting) {
  return data.reduce((pre, cur) => {
    const { children = [], ...i } = cur
    if (
      children.length === 0 &&
      !setting.includes(i.key) &&
      !i.tableHidden &&
      !i.search
    ) {
      return pre
    }
    if (children.length > 0 && !setting.includes(i.key) && !i.tableHidden) {
      const child = filterTableHidden(children, setting)
      if (child.length > 0) {
        i.children = child
        return pre.concat([i])
      }
      return pre
    }
    return pre.concat([
      {
        ...i,
        children:
          children.length > 0
            ? filterTableHidden(children, setting)
            : undefined,
        tableHidden: !setting.includes(i.key)
      }
    ])
  }, [])
}

export default {
  name: 'PageNext',
  functional: true,
  render(h, ctx) {
    const data = ctx.props.list
    const defaultCheckedKeys = getSetting(ctx)
    const allKeys = getSetting(ctx, true)
    const dataShow = filterTreeData(JSON.parse(JSON.stringify(data)), allKeys)
    const defaultExpandedKeys = getSetting(ctx, true, '@expanded', true)

    return h(Page, normalizeProps(ctx, defaultCheckedKeys), [
      h(
        'div',
        {
          slot: 'titleContainer',
          class: 'title-container--next'
        },
        [
          h(
            'el-popover',
            {
              props: {
                'visible-arrow': false,
                width: '300px',
                maxHeight: '300px',
                'popper-class': 'el-popper--page-next'
              }
            },
            [
              h(
                'el-row',
                {
                  props: {
                    type: 'flex',
                    justify: 'end'
                  },
                  style: { padding: '10px' }
                },
                [
                  h(
                    'el-col',
                    {
                      props: { span: 12 },
                      style: {
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                      }
                    },
                    []
                  ),
                  h(
                    'el-col',
                    {
                      props: { span: 12 },
                      style: {
                        display: 'flex',
                        justifyContent: 'flex-end'
                      }
                    },
                    [
                      h(
                        'el-button',
                        {
                          props: {
                            type: 'primary',
                            size: 'mini'
                          },
                          on: {
                            click: () => {
                              resetSetting(ctx)
                              ctx.parent.$forceUpdate()
                            }
                          }
                        },
                        '重置'
                      )
                    ]
                  )
                ]
              ),
              h(
                'div',
                {
                  style: {
                    overflow: 'auto',
                    maxHeight: '400px'
                  }
                },
                [
                  h('el-tree', {
                    style: {
                      width: '300px'
                    },
                    props: {
                      ref: 'tree',
                      data: dataShow,
                      'show-checkbox': true,
                      'node-key': 'key',
                      'highlight-current': true,
                      'check-on-click-node': true,
                      'default-checked-keys': defaultCheckedKeys,
                      'expand-on-click-node': false,
                      'default-expanded-keys': defaultExpandedKeys,
                      props: {
                        label: 'title',
                        children: 'children'
                      }
                    },
                    on: {
                      check: function(...args) {
                        setTimeout(() => {
                          const keys = [...args[1].checkedKeys]
                          setSetting(ctx, keys)
                          ctx.parent.$forceUpdate()
                        }, 0)
                      },
                      'node-expand': function(a) {
                        const keys = getSetting(ctx, true, '@expanded', true)
                        if (keys.includes(a.key)) {
                          return
                        }
                        keys.push(a.key)

                        setSetting(ctx, keys, '@expanded')
                        ctx.parent.$forceUpdate()
                      },
                      'node-collapse': function(a) {
                        let keys = getSetting(ctx, true, '@expanded', true)

                        keys = keys.filter((item) => item !== a.key)

                        setSetting(ctx, keys, '@expanded')
                        ctx.parent.$forceUpdate()
                      }
                    }
                  })
                ]
              ),

              h(
                'el-button',
                {
                  props: {
                    size: 'small',
                    icon: 'el-icon-setting'
                  },
                  slot: 'reference'
                },
                '列设置'
              )
            ]
          )
        ]
      )
    ])
  }
}
</script>
<style lang="scss">
.el-popper--page-next {
  padding: 0px !important;
}

.el-tree-node__content {
  height: 33px;
}
.title-container--next {
  text-align: right;
  margin-bottom: 10px;
}
</style>
