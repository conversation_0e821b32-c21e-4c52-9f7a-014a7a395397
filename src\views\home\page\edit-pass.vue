<template>
  <el-row class="edit-pass-wrap">
    <el-col :offset="9">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        status-icon
        :rules="rules"
        label-width="80px"
        class="ruleForm"
      >
        <el-form-item label="原密码" prop="pass">
          <el-input
            v-model="ruleForm.pass"
            type="password"
            show-password
            autocomplete="off"
            placeholder="请输入原密码"
            style="width: 370px"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPass">
          <el-input
            v-model="ruleForm.newPass"
            type="password"
            show-password
            autocomplete="off"
            :placeholder="this.$utils.pass_errorText"
            style="width: 370px"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="checkPass">
          <el-input
            v-model="ruleForm.checkPass"
            type="password"
            show-password
            autocomplete="off"
            :placeholder="this.$utils.pass_errorText"
            style="width: 370px"
          />
        </el-form-item>
        <el-form-item style="margin-top: 60px;">
          <el-button @click="handleCancelPass">取消</el-button>
          <el-button type="primary" :loading="btn_disabled" :disabled="btn_disabled" @click="submitForm('ruleForm')">确认</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script>
import { editPassword } from '@/api/user'

export default {
  name: 'edit-pass',
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else if (!this.$utils.verificationPass(value)) {
        callback(new Error(this.$utils.pass_errorText))
      } else {
        callback()
      }
    }
    const validateCheckPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (this.ruleForm.newPass !== this.ruleForm.checkPass) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    return {
      ruleForm: {
        pass: '',
        newPass: '',
        checkPass: ''
      },
      duration: 0,
      rules: {
        pass: [
          { required: true, message: '原密码不能为空', trigger: 'blur' }
        ],
        newPass: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        checkPass: [
          { required: true, validator: validateCheckPass, trigger: 'blur' }
        ]
      },
      btn_disabled: false
    }
  },
  methods: {
    handleCancelPass() {
      this.$router.go(-1)
    },
    submitForm(formName) {
      const thar = this

      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.btn_disabled = true
          editPassword({
            origin_password: this.ruleForm.pass,
            new_password: this.ruleForm.newPass,
            confirm_password: this.ruleForm.checkPass
          }).then(res => {
            this.btn_disabled = false
            if (res.code == 200) {
              this.$message({
                showClose: true,
                message: '密码修改成功，即将跳转到登录页！',
                type: 'success',
                duration: 3000,
                onClose() {
                  thar.$router.push({
                    path: '/'
                  })
                }
              })
            } else {
              this.$message({
                showClose: true,
                message: res.message,
                type: 'error'
              })
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: '请检查输入是否完整',
            type: 'error'
          })
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .edit-pass-wrap {
    padding-top: 40px;
  }
</style>
