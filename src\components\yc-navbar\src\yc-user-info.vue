<template>
  <div class="avatar-wrap">
    <yc-screenfull/>
    <p class="text" v-if="userData.roleName">角色：{{userData.roleName}}</p>
    <!--<p class="text" v-if="userData.userName">姓名：{{userData.userName}}</p>-->
    <el-dropdown
      trigger="click"
      @command="handleCommand">
      <span class="el-dropdown-link">
        <p class="text handle">账号：{{userData.userName}}<i class="el-icon-caret-bottom yc-icon"></i></p>
        <!--<img class="avatar" src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80"/><i class="el-icon-caret-bottom yc-icon"></i>-->
      </span>
      <el-dropdown-menu slot="dropdown"  style="width: 100px">
        <el-dropdown-item command="edit_pass">修改密码</el-dropdown-item>
        <el-dropdown-item command="login_out">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
  import ycScreenfull from './yc-screenfull';
  import { logout } from "@/api/user";
  import Store from "@/store";
  import Route from "@/router"
  export default {
    name: "yc-user-info",
    data(){
      return{
        // userData: this.$utils.getUserData()
      }
    },
    computed: {
      userData () {
        return this.$store.getters.userInfo
      }
    },
    created(){
    },
    components: {
      ycScreenfull
    },
    methods: {
      handleCommand(cmd) {
        // 下拉菜单指令处理
        if (cmd === 'edit_pass') return this.handleEditPass();
        if (cmd === 'login_out') return this.handleLoginOut();
      },
      handleLoginOut() {
        logout().then(res => {
          if(res.code == 200){
            this.$utils.setUserData('');
            this.$utils.remMenus();
            this.$utils.setToken('');
            Store.commit("user/authorization", '');
            Route.push({
              path: "/login"
            });
          }
        })
      },
      handleEditPass() {
        this.$router.push({
          path: '/edit_pass'
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .avatar-wrap {
    display: inline-block;
    vertical-align: middle;
    .yc-icon {
      margin-left: 12px;
    }
    .text {
      display: inline-block;
      font-size: 14px;
      padding-left: 12px;
      /*color: #666;*/
      color: #fff;
    }
    .handle {
      cursor: pointer;
    }
  }

</style>
