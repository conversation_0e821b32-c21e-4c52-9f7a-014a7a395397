<!--新增|编辑用户-->
<template>
  <FormDialog
    ref="dialogFormRef"
    v-model="dialogVisible"
    :title="formId ? '编辑用户' : '新增用户'"
    width="480px"
    :form-model="userForm"
    label-width="100px"
    :rules="rules"
    @submit="handleSubmit"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form-item label="姓名" prop="nickName">
      <el-input v-model="userForm.nickName" clearable placeholder="请输入姓名" />
    </el-form-item>
    <el-form-item label="手机号" prop="mobileNo">
      <el-input v-model="userForm.mobileNo" maxlength="11" clearable placeholder="请输入手机号" />
    </el-form-item>
    <el-form-item label="登录名" prop="userName">
      <el-input v-model="userForm.userName" clearable placeholder="请输入登录名" />
    </el-form-item>
    <el-form-item label="角色" prop="roleId">
      <el-select v-model="userForm.roleId" clearable filterable placeholder="请选择角色" style="width: 100%" disabled>
        <el-option
          v-for="item in roleOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item v-if="!formId" label="密码" prop="password">
      <el-input v-model="userForm.password" maxlength="60" show-password clearable placeholder="请输入密码" />
    </el-form-item>
  </FormDialog>
</template>

<script>
import { rules } from '@/views/mechanism/rules/userFormRules'
import { createSassAccountApi, editSassAccountApi, getSassRoleListApi, getSassUserInfoApi } from '@/api/mechanism'
import FormDialog from '@/components/FormDialog/index.vue'

const userFormState = {
  nickName: '',
  mobileNo: '',
  userName: '',
  roleId: null,
  password: ''
}

export default {
  name: 'UserForm',
  components: {
    FormDialog
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    agenciesId: {
      type: Number,
      default: null
    },
    formId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      submitLoading: false,
      rules,
      userForm: { ...userFormState },
      roleOptions: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editSassAccountApi({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await createSassAccountApi(params)
      }
    },
    handleOpen() {
      this.getSassRoleList()

      this.formId && this.$nextTick(() => {
        this.$refs.dialogFormRef.loadFormData(() => {
          return getSassUserInfoApi({
            id: this.formId
          })
        })
      })
    },
    // 获取角色
    async getSassRoleList() {
      try {
        const { data } = await getSassRoleListApi({
          agenciesId: this.agenciesId
        })

        this.roleOptions = data
        this.userForm.roleId = data.find(item => item.code === 'SUPER_ROLE').id
      } catch (err) {
        console.log(err)
      }
    },
    handleClose() {
      this.userForm = { ...userFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    async handleSubmit(formParams, done) {
      try {
        const params = { ...formParams, agenciesId: this.agenciesId }
        const { code } = await this.useHttpInterface(params)

        if (code !== 200) return false

        this.$message.success('操作成功')
        this.handleClose()
      } catch (err) {
        console.log(err)
      } finally {
        done()
      }
    }
  }
}
</script>
