<template>
  <el-form
    ref="ruleForm"
    :model="ruleForm"
    :rules="rules"
    :show-close="false"
    label-width="100px"
    class="demo-ruleForm"
  >
    <el-form-item label="部门名称" prop="branchName" :rules="rules.common">
      <el-input v-model="ruleForm.branchName" placeholder="请输入用户名" maxlength="15" style="width:320px;" />
    </el-form-item>
    <el-form-item label="上级部门" prop="parentBranch">
      <el-cascader ref="departmentRef" v-model="ruleForm.parentBranch" style="width:320px;" clearable placeholder="请选择上级部门" :show-all-levels="false" :options="departmentOptions" :props="{value:'id',label:'branchName',children:'childrenBranch',checkStrictly:true}" />
    </el-form-item>
    <el-form-item label="部门主管" prop="branchManager">
      <el-select v-model="ruleForm.branchManager" style="width:320px;" clearable placeholder="请选择">
        <el-option
          v-for="item in allUserData"
          :key="item.id"
          :label="item.username"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button
        type="primary"
        :loading="btnDisabled"
        :disabled="btnDisabled"
        @click="roleUpdated('ruleForm')"
      >确 定</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { get_branchManger } from '@/api/system'
import { commonNullCharacterReg } from '@/libs/validate'
import utils from '@/libs/utils'
const isEmpty = utils.isEmpty
import { copy } from '@/config/basicsMethods'

export default {
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    btnDisabled: {
      type: Boolean,
      default: false
    },
    isType: {
      type: String,
      default: 'add'
    },
    departmentList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      ruleForm: {
        branchManager: '',
        parentBranch: '',
        branchName: ''
      },
      rules: {
        common: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          { required: true, message: '此项不能为空', trigger: 'change' },
          { validator: commonNullCharacterReg('此项不能为空'), trigger: 'blur' }
        ]
      },
      allUserData: []
    }
  },
  computed: {
    departmentOptions() {
      const departmentList = copy(this.departmentList)
      const filterCurId = (item, id) => {
        if (item.id === id) {
          return {
            ...item,
            disabled: true,
            childrenBranch: null
          }
        } else if (item.childrenBranch && item.childrenBranch.length > 0) {
          item.childrenBranch = item.childrenBranch.map(el => {
            el = filterCurId(el, id)
            return el
          })
        }
        return { ...item, disabled: false }
      }
      return isEmpty(this.ruleForm.id) ? departmentList : departmentList?.reduce((pre, cur) => {
        const curd = filterCurId(cur, this.ruleForm.id)
        curd && pre.push(curd)
        return pre
      }, []) ?? []
    }
  },
  created() {
    this.ruleForm = { ...this.ruleForm, ...this.formData }
    this.getManager()
  },
  methods: {
    handleChangeBranch() {
      const isActive = this.allUserData.some(item => item.id == this.ruleForm.branchManager)
      !isActive && (this.ruleForm.branchManager = '')
    },
    getManager() {
      get_branchManger({ branchId: this.ruleForm.id }).then(res => {
        if (res.code == 200) {
          this.allUserData = res.data
          this.handleChangeBranch()
        } else {
          this.allUserData = []
          this.handleChangeBranch()
        }
      })
    },
    roleUpdated(formName) {
      const flag = this.departmentList && this.departmentList.length > 0 && (!this.ruleForm.parentBranch || this.ruleForm.parentBranch.length == 0)
      if (flag) {
        this.$message.error('请选择上级部门')
        return
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          // 处理上级部门参数
          const parentBranch = Array.isArray(this.ruleForm.parentBranch) ? this.ruleForm.parentBranch[this.ruleForm.parentBranch.length - 1] : this.ruleForm.parentBranch
          this.$emit('submit', { ...this.ruleForm, parentBranch: parentBranch || '' })
        }
      })
    }
  }
}

</script>
