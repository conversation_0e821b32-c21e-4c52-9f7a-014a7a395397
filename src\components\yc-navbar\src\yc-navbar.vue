<template>
  <div class="yc-navbar-wrap">
    <div class="right-menu">
<!--      <yc-breadcrumb />-->
    </div>
    <div class="right-menu">
      <yc-user-info/>
    </div>
  </div>
</template>

<script>
  import ycUserInfo from './yc-user-info';
  import ycBreadcrumb from './yc-breadcrumb';
  export default {
    name: 'yc-navbar',
    components: {
      ycUserInfo,
      ycBreadcrumb
    },
    methods: {
      handleGoHome() {
        this.$router.push({
          name: 'home'
        })
      },
      changeCollapse(){
        this.$emit('changeCollapse')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .yc-navbar-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    .logo-title {
      width: auto;
      height: 40px;
      margin: 5px 0;
      cursor: pointer;
    }
    .right-menu {
      width: auto;
      float: right;
      height: 50px;
      font-size: 0;
      line-height: 50px;
    }
  }

</style>
