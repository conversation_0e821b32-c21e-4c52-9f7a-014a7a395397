// 分发策略接口
import { get, post } from '@/libs/axios.package'

// 获取总策略列表
export const getStrategyListApi = obj => {
  return get('/distribute/strategy/page', obj)
}
// 获取总策略详情
export const getStrategyDetailApi = id => {
  return get(`/distribute/strategy/getInfo?id=${id}`)
}
// 新增总策略
export const addStrategyApi = obj => {
  return post('/distribute/strategy/saveStrategy', obj)
}
// 修改总策略
export const editStrategyApi = obj => {
  return post('/distribute/strategy/updateStrategy', obj)
}
// 改变总策略状态
export const changeStrategyStatusApi = obj => {
  return post('/distribute/strategy/enableOrDisable', obj)
}
// 开关总策略默认策略
export const changeStrategyDefaultApi = obj => {
  return post('/distribute/strategy/updateDefault', obj)
}
// 获取机构策略列表
export const getMechanismStrategyListApi = obj => {
  return get('/distribute/agenciesStrategy/page', obj)
}
// 新增机构策略
export const addMechanismStrategyApi = obj => {
  return post('/distribute/agenciesStrategy/saveAgenciesStrategy', obj)
}
// 修改机构策略
export const editMechanismStrategyApi = obj => {
  return post('/distribute/agenciesStrategy/updateAgenciesStrategy', obj)
}
// 删除机构策略
export const deleteMechanismStrategyApi = id => {
  return post(`/distribute/agenciesStrategy/delete?id=${id}`)
}
// 更改机构策略状态
export const changeMechanismStrategyStatusApi = obj => {
  return post('/distribute/agenciesStrategy/enableOrDisable', obj)
}
// 开关机构策略默认策略
export const changeMechanismStrategyDefaultApi = obj => {
  return post('/distribute/agenciesStrategy/updateDefault', obj)
}
// 获取机构策略详情
export const getMechanismStrategyDetailApi = id => {
  return get(`/distribute/agenciesStrategy/getInfo?id=${id}`)
}
// 获取机构策略排序
export const getMechanismStrategySortApi = () => {
  return get('/distribute/agenciesStrategy/getInfo')
}
// 总策略是否有默认策略
export const getStrategyDefaultApi = id => {
  return get(`/distribute/strategy/haveDefault?id=${id}`)
}
// 机构策略是否有默认策略
export const getMechanismStrategyDefaultApi = id => {
  return get(`/distribute/agenciesStrategy/haveDefault?id=${id}`)
}
