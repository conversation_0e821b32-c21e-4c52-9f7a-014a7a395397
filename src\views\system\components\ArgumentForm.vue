<template>
  <FormDialog
    ref="dialogFormRef"
    v-model="dialogVisible"
    :title="formId ? '编辑参数' : '新增参数'"
    :form-model="argumentForm"
    width="25%"
    :rules="rules"
    @submit="handleSubmit"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form-item label="参数名称" prop="paramName">
      <el-input v-model="argumentForm.paramName" clearable :maxlength="30" placeholder="请输入产品名称" />
    </el-form-item>
    <el-form-item label="参数说明" prop="paramDesc">
      <el-input v-model="argumentForm.paramDesc" type="textarea" resize="none" clearable :maxlength="200" placeholder="请输入产品描述" />
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-radio-group v-model="argumentForm.status">
        <el-radio :label="1">启用</el-radio>
        <el-radio :label="0">禁用</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="参数配置" prop="paramValue">
      <el-input v-model="argumentForm.paramValue" clearable :maxlength="200" placeholder="请输入产品描述" />
    </el-form-item>
  </FormDialog>
</template>

<script>
import FormDialog from '@/components/FormDialog/index.vue'
import { postSystemDetail, createSystemDetailApi, getSystemDetailApi } from '@/api/system'

const argumentFormState = {
  paramName: '',
  paramDesc: '',
  status: 1,
  paramValue: ''
}
const rules = {
  paramName: [
    { required: true, message: '请输入参数名称', trigger: 'blur' }
  ],
  paramValue: [
    { required: true, message: '请输入参数配置', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  paramDesc: [
    { required: true, message: '请输入参数说明', trigger: 'blur' }
  ]
}

export default {
  name: 'ArgumentForm',
  components: {
    FormDialog
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    formId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      argumentForm: { ...argumentFormState }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    rules() {
      return rules
    }
  },
  methods: {
    handleClose() {
      this.$refs.dialogFormRef.resetForm()
      this.argumentForm = { ...argumentFormState }
      this.$emit('change', false)
      this.$emit('close')
    },
    handleOpen() {
      this.formId && this.getArgumentDetail()
    },
    // 获取参数详情
    async getArgumentDetail() {
      const res = await getSystemDetailApi(this.formId)
      if (res.code === 200) {
        this.argumentForm = res.data
      }
    },
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await postSystemDetail({
          ...params,
          id: this.formId
        })
      } else {
        // 新增
        return await createSystemDetailApi(params)
      }
    },
    async handleSubmit(params, done) {
      try {
        const { code } = await this.useHttpInterface(params)
        if (code !== 200) return false
        this.$message.success('操作成功')
        this.handleClose()
      } catch (err) {
        console.log(err)
      } finally {
        done()
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
