<template>
  <div class="statistics-data">
    <!-- 菜单栏 -->
    <div class="menu-box">
      <div class="title">数据统计</div>
      <div class="menu-list">
        <div v-for="item in menuList" :key="item.id" class="menu-item" :class="curMenuId === item.id ? 'active' : ''" @click="handleMenuClick(item.id)">{{ item.label }}</div>
      </div>
    </div>
    <div class="content">
      <GeneralOverview v-if="curMenuId === 1" />
      <DistributionOverview v-if="curMenuId === 2" />
    </div>
  </div>
</template>

<script>
import DistributionOverview from '../components/DistributionOverview.vue'
import GeneralOverview from '../components/GeneralOverview.vue'

export default {
  components: {
    DistributionOverview,
    GeneralOverview
  },
  data() {
    return {
      menuList: [
        {
          id: 1,
          label: '数据概览'
        },
        {
          id: 2,
          label: '分发记录概览'
        }
      ],
      curMenuId: 1
    }
  },
  methods: {
    handleMenuClick(id) {
      this.curMenuId = id
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics-data{
  display: flex;
  gap: 10px;
  height: 88vh;
  align-items: flex-start;
  .menu-box{
    width: 15%;
    box-shadow: 0 0 5px 0 #d7d7d7;
    border-radius: 5px;
    height: 100%;
    padding: 20px 10px;
    .title{
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #c1c1c1;
    }
    .menu-list{
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      gap: 5px;
      height: 100%;
      .menu-item{
        cursor: pointer;
        width: 100%;
        padding: 13px 10px;
        transition: 0.3s;
        border-radius: 5px;
        &.active{
          background-color: #f5f5f5;
          color: #409eff;
        }
        &:hover{
          background-color: #f5f5f5;
        }
      }
    }
  }
  .content{
    flex: 1;
    box-shadow: 0 0 5px 0 #d7d7d7;
    border-radius: 5px;
    padding: 20px 10px;
    height: 100%;
  }
}
</style>
