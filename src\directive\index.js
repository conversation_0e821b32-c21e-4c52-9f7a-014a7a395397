import Vue from 'vue'
import { getCurrentPathName, rolesButtons } from '../utils/menuCodes'
function checkPermission(el, binding) {
  const roles = rolesButtons()
  let { value } = binding
  const path = getCurrentPathName()
  if (value) {
    value = path + '/' + value
    const permissionRoles = value
    const hasPermission = roles?.some(role => {
      return role.includes(permissionRoles) || permissionRoles.includes(role)
    })
    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`need roles! Like v-permission="['admin','editor']"`)
  }
}
Vue.directive('error', {
  inserted(e) {
    if (!e.src) {
      e.src = require('../assets/img/imgerror.jpg')
    }
    e.onerror = function() {
      e.src = require('../assets/img/imgerror.jpg')
    }
  }
})

/*
 * 防止重复点击
 * */
Vue.directive('preventReClick', {
  inserted(el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.disabled = true
        setTimeout(() => {
          el.disabled = false
        }, binding.value || 2000)
      }
    })
  }
})
/*
 * 权限
 * */
Vue.directive('permission', {
  inserted(el, binding) {
    checkPermission(el, binding)
  },
  update(el, binding) {
    checkPermission(el, binding)
  }
})

