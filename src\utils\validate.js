/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}
/**
 * @param {number} toFixNumbers 小数保留
 * @param {number} maxVal 最大值
 * @param {number} minVal 最小值
 * @param val 输入值
 */
export function validNumbers(toFixNumbers = 2, minVal, maxVal, val) {
  const valToNum = Number(val)
  const pattern = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g
  if (val || val === 0 || val === '0') {
    if (!pattern.test(valToNum)) {
      return '请输入数字'
    }
    if (toFixNumbers === 0 && !/^\+?[1-9][0-9]*$/.test(valToNum)) {
      return '请输入整数'
    }
    console.log(toFixNumbers, 'toFixNumberstoFixNumbers')
    if (toFixNumbers > 0) {
      var reg = new RegExp(
        '^(([1-9][0-9]*)|(([0]\\.\\d{1,' + toFixNumbers + '}|[1-9][0-9]*\\.\\d{1,' + toFixNumbers + '})))$'
      )
      if (String(valToNum).includes('.') && toFixNumbers > 0 && !reg.test(valToNum)) {
        return `最多输入${toFixNumbers}位小数`
      }
    }
    if (valToNum > maxVal) {
      return `不能大于${maxVal}`
    }
    if (valToNum < minVal) {
      return `不能小于${minVal}`
    }
  }
  return ''
}
