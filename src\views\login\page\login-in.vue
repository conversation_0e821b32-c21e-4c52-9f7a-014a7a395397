<template>
  <div class="login">
    <div class="login-in-box">
      <div class="login-logo">
        <img src="@/assets/img/login/login-logo.png" alt="Logo">
      </div>
      <div class="login-form-wrap">
        <div class="subtitle-wrapper">
          <p class="login-subtitle">手机号登录</p>
          <div class="login-title-underline" />
        </div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-position="top"
          hide-required-asterisk
          @keyup.enter="submitForm('ruleForm')"
        >
          <el-form-item prop="mobileNo" class="phone-container">
            <el-input
              ref="mobileNoInput"
              v-model="ruleForm.mobileNo"
              type="text"
              placeholder="请输入手机号"
              maxlength="11"
              class="phone"
              @input="handleMobileNoInput"
              @change="loginPhoneCache"
            />
          </el-form-item>

          <el-form-item prop="smsCode" class="code-container">
            <div class="verification-code-input">
              <el-input
                ref="smsCodeInput"
                v-model="ruleForm.smsCode"
                placeholder="输入验证码"
                class="verification-input"
                @input="validateNumeric('smsCode')"
                @keyup.enter.native="submitForm('ruleForm')"
              />
              <span
                class="verification-code-btn"
                :class="{ 'disabled': showTime }"
                @click="!showTime && getCode()"
              >
                {{ showTime ? `已发送${createTime}s` : '获取验证码' }}
              </span>
            </div>
          </el-form-item>
          <el-form-item style="margin-top: -10px;margin-bottom: 20px;">
            <el-checkbox v-model="rememberMe" class="remember-me">记住账号</el-checkbox>
          </el-form-item>
        </el-form>
        <el-button
          size="medium"
          type="primary"
          class="login-btn"
          :loading="loadingFlag"
          @click="submitForm('ruleForm')"
        >
          登录
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { loginIn, user_me, get_code } from '@/api/user'
import Store from '@/store'
import moment from 'moment'
import utils from '@/libs/utils'
import { validPhone } from '@/utils/formValidationRules'
export default {
  name: 'login-In',
  data() {
    return {
      ruleForm: {
        mobileNo: '',
        smsCode: ''
      },
      rules: {
        mobileNo: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^[0-9]*$/, message: '手机号只能输入数字', trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { pattern: /^[0-9]*$/, message: '验证码只能输入数字', trigger: 'blur' }
        ]
      },
      loadingFlag: false,
      showTime: false,
      createTime: 60,
      rememberMe: true
    }
  },
  watch: {
    ruleForm: {
      handler(newValue, oldValue) {
        if (newValue.code) {
          this.ruleForm.code = this.ruleForm.code.replace(/[\D]/g, '')
        }
      },
      deep: true
    },
    rememberMe: {
      handler() {
        this.loginPhoneCache()
      }
    }
  },
  mounted() {
    // 页面加载后自动聚焦手机号输入框
    const mobileNo = localStorage.getItem('loginMobileNo')
    if (mobileNo) {
      (this.ruleForm.mobileNo = mobileNo)
    } else {
      this.rememberMe = true
    }
    this.$nextTick(() => {
      !this.ruleForm.mobileNo && this.$refs.mobileNoInput.focus()
    })
  },
  methods: {
    loginPhoneCache() {
      if (!this.rememberMe) {
        localStorage.removeItem('loginMobileNo')
        return
      }
      const callback = () => {
        localStorage.setItem('loginMobileNo', this.ruleForm.mobileNo)
      }
      validPhone(null, this.ruleForm.mobileNo, callback)
    },
    handleMobileNoInput(value) {
      // 先执行数字验证
      this.validateNumeric('mobileNo')

      // 当手机号输入达到11位时，自动聚焦到验证码输入框
      if (value.length === 11) {
        this.$nextTick(() => {
          this.$refs.smsCodeInput.focus()
        })
      }
    },
    validateNumeric(field) {
      if (this.ruleForm[field]) {
        this.ruleForm[field] = this.ruleForm[field].replace(/\D/g, '')
      }
    },
    getCode() {
      if (this.ruleForm.mobileNo) {
        get_code({
          mobileNo: this.ruleForm.mobileNo
        }).then(res => {
          if (res.code == 200) {
            this.createTime = 60
            this.showTime = true
            this.Timeout()
            this.$message({
              message: '验证码已发送到您账号绑定的手机号！',
              type: 'success'
            })
          }
        })
      } else {
        this.$message.error('手机号不能为空')
      }
    },
    Timeout() {
      if (this.createTime === 0) return
      const inter = setInterval(() => {
        this.createTime--
        if (this.createTime <= 0) {
          this.showTime = false
          clearInterval(inter)
        }
      }, 1000)
    },

    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loadingFlag = true
          loginIn({
            mobileNo: this.ruleForm.mobileNo,
            smsCode: this.ruleForm.smsCode
          })
            .then(res => {
              Store.commit(
                'user/SET_LAST_LOGIN_TIME',
                moment().format('YYYY-MM-DD HH:mm')
              )
              if (res.code === 200) {
                this.$utils.setToken(res.data.access_token)
                Store.commit('user/authorization', res.data.access_token)
                user_me().then(res => {
                  if (res.code === 200) {
                    this.$message({
                      showClose: true,
                      message: '登录成功',
                      type: 'success'
                    })

                    const user = {
                      userId: res.data.id,
                      userName: res.data.userName,
                      roleName: res.data.roleName,
                      localIp: res.data.ip,
                      buttons: res.data?.buttons
                    }
                    Store.commit('user/SET_USER_INFO', user) // 存储用户信息
                    this.loadingFlag = false
                    const formatMenu = utils.formatMenu(res.data.menus)
                    this.$refs[formName].resetFields()
                    Store.commit('user/SET_NAME', user.userName)
                    Store.commit('user/SET_ROLE_NAME', user.roleName)
                    Store.commit('user/SET_LOGIN', true)
                    Store.commit('user/SET_MENUS', formatMenu)
                    // Store.commit('SET_MENUS', this.$CONSTANT.menuData) // TODO:使用本地菜单

                    this.$router.push({
                      path: '/home'
                    })
                  } else {
                    this.loadingFlag = false
                    this.$refs[formName].resetFields()
                    this.$message.error(res.message)
                  }
                }).catch(err => {
                  console.log(err)
                })
              } else {
                this.loadingFlag = false
                this.$message.error(res.message)
              }
            })
            .catch(err => {
              this.$message.error(err)
              this.loadingFlag = false
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100vh;
  background: rgba(235, 244, 255, 1);
  background-image: url("~@/assets/img/login/login-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10%;
}

.login-in-box {
  width: 460px;
  height: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 32px 30px;
}

.login-logo {
  text-align: center;
  margin-bottom: 32px;

  img {
    height: 32px;
    object-fit: contain;
  }
}

.login-form-wrap {
  width: 100%;

  & ::v-deep .el-form-item__label {
    line-height: 1;
    padding: 0 0 15px;
  }

  & ::v-deep .el-input__inner {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #333;
    font-weight: 400;
    background: #fff;
    border: 1px solid #D0D2D9;
    height: 48px;
    border-radius: 8px;
    padding-left: 15px;
  }

  & ::v-deep .el-form-item.is-error .el-input__inner {
    border-color: #f56c6c;
  }

  .login-btn {
    width: 100%;
    height: 50px;
    background: #1677FF;
    box-shadow: 0px 2px 5px 0px rgba(47,121,255,0.4);
    border-radius: 8px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #fff;
    line-height: 25px;
    font-size: 16px !important;
    border: none;

    & ::v-deep span {
      font-size: inherit !important;
    }
  }
}

.title-wrapper {
  text-align: center;
  margin-bottom: 25px;
}

.subtitle-wrapper {
  position: relative;
  margin-bottom: 24px;
}

.login-subtitle {
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #1677FF;
}

.login-title-underline {
  width: 34px;
  height: 2px;
  background: linear-gradient(270deg, rgba(22,119,255,0) 0%, #1677FF 100%);
  border-radius: 1px;
  margin-top: 5px;
}

.phone {
  width: 100%;
}

.phone-container {
  margin-bottom: 20px;
  position: relative;
}

.code-container {
  width: 100%;
  margin-bottom: 25px;
  position: relative;
}

.verification-code-input {
  position: relative;
  width: 100%;
}

.verification-input {
  width: 100%;
}

.verification-code-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #1677FF;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  background: transparent;
  border: none;
  z-index: 2;
  padding-left: 10px;
  border-left: 1px solid #D0D2D9;
  height: 20px;
  line-height: 20px;

  &.disabled {
    color: #999;
    cursor: not-allowed;
  }
}

.el-form-item {
  margin-bottom: 30px;
}

.login-form-wrap ::v-deep {
  .el-form-item__error {
    position: absolute;
    top: 100%;
    left: 0;
    padding-top: 6px;
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
  }

  .el-form-item.is-error .el-input__inner {
    border-color: #f56c6c;
  }
}
</style>
