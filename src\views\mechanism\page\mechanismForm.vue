<!--新增|编辑机构-->
<template>
  <div v-loading="mechanismFormLoading" class="mechanism-form">
    <div class="header">
      <span class="el-descriptions__title">{{ mechanismId ? '编辑' : '创建' }}机构</span>
      <el-button type="text" icon="el-icon-arrow-left" @click="backPage">返回上一页</el-button>
    </div>
    <el-form ref="mechanismFormRef" inline :model="mechanismForm" :rules="rules" label-width="140px" size="small">
      <!--      企业信息-->
      <div class="el-descriptions__title" style="margin-bottom: 20px">企业信息</div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="营业执照" prop="companyLicense">
            <uploadFile
              v-model="mechanismForm.companyLicense"
              drag
              :size="5 * 1024 * 1024"
              :width="300"
              :height="180"
              is-draggable
              :send-url="FILE_UPLOAD_URL"
              :multiple="false"
              :limit="1"
            >
              <div class="addDiv">
                <i class="el-icon-plus" />
                <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
              </div>
            </uploadFile>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办公场地视频" prop="companyOfficeVideo">
            <uploadFile
              v-model="mechanismForm.companyOfficeVideo"
              drag
              :accept-array="['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'webm', 'mpeg', 'video', 'audio']"
              :width="300"
              :size="50 * 1024 * 1024"
              :send-url="FILE_UPLOAD_URL"
              :height="180"
              :multiple="false"
              :limit="1"
            >
              <div class="addDiv">
                <i class="el-icon-plus" />
                <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
              </div>
            </uploadFile>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="前台logo" prop="companyLogo">
            <uploadFile
              v-model="mechanismForm.companyLogo"
              drag
              :width="300"
              :size="5 * 1024 * 1024"
              :send-url="FILE_UPLOAD_URL"
              :height="180"
              :multiple="false"
              :limit="1"
            >
              <div class="addDiv">
                <i class="el-icon-plus" />
                <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
              </div>
            </uploadFile>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="统一社会信用代码" prop="companyCreditCode">
            <el-input v-model="mechanismForm.companyCreditCode" maxlength="18" placeholder="请输入统一社会信用代码" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="机构名称" prop="agencyName">
            <el-input v-model="mechanismForm.agencyName" placeholder="请输入机构名称" maxlength="30" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="机构类型" prop="agencyType">
            <el-select v-model="mechanismForm.agencyType" clearable placeholder="请选择" style="width: 300px">
              <el-option
                v-for="item in MechanismTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="企业地址" prop="companyAddress">
            <city-selector
              v-model="mechanismForm.companyAddress"
              placeholder="请选择省市区"
              style="width: 300px"
              @change="handleCityChange"
            />
          </el-form-item>
          <el-form-item prop="companyAddressDetail">
            <el-input v-model="mechanismForm.companyAddressDetail" maxlength="35" placeholder="请输入详细地址" clearable style="width: 350px" />
          </el-form-item>
        </el-col>
      </el-row>
      <!--      法人信息-->
      <div class="el-descriptions__title" style="margin-bottom: 20px">法人信息</div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="法人身份证人像面" prop="legalCardFront">
            <uploadFile
              v-model="mechanismForm.legalCardFront"
              drag
              :width="300"
              :send-url="FILE_UPLOAD_URL"
              :size="5 * 1024 * 1024"
              :height="180"
              :multiple="false"
              :limit="1"
            >
              <div class="addDiv">
                <i class="el-icon-plus" />
                <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
              </div>
            </uploadFile>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人身份证国徽面" prop="legalCardBack">
            <uploadFile
              v-model="mechanismForm.legalCardBack"
              :send-url="FILE_UPLOAD_URL"
              drag
              :width="300"
              :size="5 * 1024 * 1024"
              :height="180"
              :multiple="false"
              :limit="1"
            >
              <div class="addDiv">
                <i class="el-icon-plus" />
                <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
              </div>
            </uploadFile>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="法人姓名" prop="legalName">
            <el-input v-model="mechanismForm.legalName" placeholder="请输入法人姓名" maxlength="25" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人身份证号" prop="legalCardNumber">
            <el-input v-model="mechanismForm.legalCardNumber" placeholder="请输入法人身份证号" maxlength="18" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人手机号" prop="legalMobile">
            <el-input v-model="mechanismForm.legalMobile" placeholder="请输入法人手机号" maxlength="11" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
      </el-row>
      <!--      联系人信息-->
      <div class="c_header">
        <div class="el-descriptions__title">联系人信息</div>
        <span class="tips">默认以联系人创建SAAS超级管理员账号</span>
      </div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="法人是否联系人" prop="legalIsContact">
            <el-select v-model="mechanismForm.legalIsContact" style="width: 300px" @change="handleLegalIsContactChange">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input v-model="mechanismForm.contactName" placeholder="请输入联系人姓名" maxlength="25" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人手机号" prop="contactMobile">
            <el-input v-model="mechanismForm.contactMobile" placeholder="请输入联系人手机号" maxlength="11" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="联系人邮箱" prop="contactEmail">
            <el-input v-model="mechanismForm.contactEmail" placeholder="请输入联系人邮箱" maxlength="124" clearable style="width: 300px" />
          </el-form-item>
        </el-col>
      </el-row>
      <!--合作信息-->
      <div class="el-descriptions__title" style="margin-bottom: 20px">合作信息</div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="有效期" prop="cooperateValidity">
            <el-date-picker
              v-model="mechanismForm.cooperateValidity"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 300px"
              @change="handleCooperateValidityChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否API机构" prop="cooperateIsApi">
            <el-select v-model="mechanismForm.cooperateIsApi" style="width: 300px">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否撞库" prop="cooperateIsMatch">
            <el-select v-model="mechanismForm.cooperateIsMatch" style="width: 300px" @change="handleCooperateIsMatchChange">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="撞库地址" prop="matchUrl">
            <el-input v-model="mechanismForm.matchUrl" placeholder="请输入撞库地址" clearable style="width: 860px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="通知地址" prop="notifyUrl">
            <el-input v-model="mechanismForm.notifyUrl" placeholder="请输入通知地址" clearable style="width: 860px" />
          </el-form-item>
        </el-col>
      </el-row>
      <!--      附件资料-->
      <div class="el-descriptions__title" style="margin-bottom: 20px">附件资料</div>
      <div class="attachments-upload">
        <div class="tips">资质认证合同证明(合同需公司与资方盖齐缝章)</div>
        <uploadFile
          v-model="mechanismForm.attachment"
          :send-url="FILE_UPLOAD_URL"
          drag
          :width="120"
          :height="120"
          :accept-array="['png', 'jpg', 'jpeg', 'gif', 'pdf']"
          :size="10 * 1024 * 1024"
          :limit="10"
        >
          <div class="addDiv">
            <i class="el-icon-plus" />
            <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
          </div>
        </uploadFile>
        <div class="tips">请上传PDF,JPG,PNG,JPEG等，不超过10M</div>
      </div>

      <div class="submit">
        <el-form-item>
          <el-button @click="backPage">取消</el-button>
          <el-button type="primary" @click="handleConfirm">提交</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import CitySelector from '@/components/CitySelector/index.vue'
import uploadFile from '@/components/yc-upload/handleUploadImage'
import { MechanismTypeList } from '../enum'
import { rules } from '../rules/mechanismFormRules'
import { createMechanismApi, editMechanismApi } from '@/api/mechanism'
import CONSTANT from '@/config/constant.conf'
import { getMechanismInfoApi } from '@/api/mechanism'

const mechanismFormState = {
  // 营业执照
  companyLicense: '',
  // 办公场地视频
  companyOfficeVideo: '',
  // 前台logo
  companyLogo: '',
  // 统一社会信用代码
  companyCreditCode: '',
  // 机构名称
  agencyName: '',
  // 机构类型
  agencyType: null,
  // 企业地址 省市区临时字段
  companyAddress: null,
  // 企业地址 省市区code
  companyAddressIds: null,
  // 企业地址 省市区name
  companyAddressName: '',
  // 详细地址
  companyAddressDetail: '',
  // 法人身份证人像面
  legalCardFront: '',
  // 法人身份证国徽面
  legalCardBack: '',
  // 法人姓名
  legalName: '',
  // 法人身份证号
  legalCardNumber: '',
  // 法人手机号
  legalMobile: '',
  // 法人是否联系人
  legalIsContact: null,
  // 联系人姓名
  contactName: '',
  // 联系人手机号
  contactMobile: '',
  // 联系人邮箱
  contactEmail: '',
  // 合作有效期
  cooperateValidity: null,
  // 有效期
  cooperateStartDate: '',
  cooperateEndDate: '',
  // 是否api机构
  cooperateIsApi: null,
  // 是否撞库
  cooperateIsMatch: null,
  // 撞库地址
  matchUrl: '',
  // 通知地址
  notifyUrl: '',
  // 附件资料
  attachment: ''
}

export default {
  name: 'MechanismForm',
  components: { CitySelector, uploadFile },
  data() {
    return {
      // loading
      mechanismFormLoading: false,
      // 文件上传url
      FILE_UPLOAD_URL: `${CONSTANT.publicPath}/upload/file`,
      rules: {
        ...rules,
        matchUrl: [
          {
            required: false,
            validator(rule, value, callback) {
              // 注意：this 指向 window，不能用 this 访问数据，必须传入 context
              callback() // 默认通过
            },
            trigger: 'blur'
          }
        ]
      },
      mechanismForm: { ...mechanismFormState },
      mechanismId: null
    }
  },
  computed: {
    MechanismTypeList() {
      return MechanismTypeList
    }
  },
  mounted() {
    // 获取url参数
    // 机构id 编辑使用
    const { id } = this.$route.query
    this.mechanismId = id

    id && this.getMechanismInfo()
  },
  methods: {
    // 获取机构基本信息
    async getMechanismInfo() {
      this.mechanismFormLoading = true
      try {
        const { data } = await getMechanismInfoApi(this.mechanismId, 2)
        this.mechanismForm = { ...data }
        // 处理省市区
        const companyAddressIdsArray = data.companyAddressIds?.split(',')
        this.mechanismForm.companyAddress = companyAddressIdsArray[companyAddressIdsArray.length - 1]
        // 处理有效期
        this.$set(this.mechanismForm, 'cooperateValidity', [data.cooperateStartDate, data.cooperateEndDate])

        // 处理撞库地址校验
        this.handleCooperateIsMatchChange(data.cooperateIsMatch)
      } catch (err) {
        console.log(err)
      } finally {
        this.mechanismFormLoading = false
      }
    },
    // 选择地址change
    handleCityChange(_e, data) {
      if (data) {
        this.mechanismForm.companyAddressIds = data.code
        this.mechanismForm.companyAddressName = data.label
      } else {
        this.mechanismForm.companyAddressIds = ''
        this.mechanismForm.companyAddressName = ''
      }
    },
    // 若法人是联系人，联系人姓名和联系人手机号则为法人姓名和手机号
    handleLegalIsContactChange(e) {
      if (e === 1 && !this.mechanismForm.contactName) {
        this.mechanismForm.contactName = this.mechanismForm.legalName
      }
      if (e === 1 && !this.mechanismForm.contactMobile) {
        this.mechanismForm.contactMobile = this.mechanismForm.legalMobile
      }
    },
    // 处理有效期开始结束字段
    handleCooperateValidityChange(e) {
      if (e) {
        this.mechanismForm.cooperateStartDate = e[0]
        this.mechanismForm.cooperateEndDate = e[1]
      } else {
        this.mechanismForm.cooperateStartDate = ''
        this.mechanismForm.cooperateEndDate = ''
      }
    },
    // 是否撞库为是，撞库地址为必填，反之为非必填
    handleCooperateIsMatchChange(e) {
      this.rules.matchUrl[0].required = e === 1
      this.rules.matchUrl[0].validator = (rule, value, callback) => {
        if (e === 1) {
          if (!value) {
            return callback(new Error('地址不能为空'))
          }

          const reg = /^https?:\/\/[\w.-]+(?:\.[\w.-]+)+[/#?]?.*$/
          if (!reg.test(value)) {
            callback(new Error('请输入正确的地址'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }

      this.$nextTick(() => {
        this.$refs.mechanismFormRef.clearValidate(['matchUrl'])
      })
    },
    // 判断使用新增、编辑接口
    async useHttpInterface(params) {
      if (params.id) {
        // 编辑
        return await editMechanismApi({
          ...params,
          id: this.mechanismId
        })
      } else {
        // 新增
        return await createMechanismApi(params)
      }
    },
    handleConfirm() {
      this.$refs.mechanismFormRef.validate((valid) => {
        if (!valid) return false

        //   二次确认
        this.$confirm('是否确认提交？', '操作提示', { closeOnClickModal: false })
          .then(async() => {
            try {
              const params = {
                ...this.mechanismForm
              }
              // 删除临时字段
              delete params.companyAddress

              const { code } = await this.useHttpInterface(params)
              if (code !== 200) return
              this.$message.success('提交成功')
              this.backPage()
            } catch (err) {
              console.log(err)
            }
          })
      })
    },
    backPage() {
      this.$router.back()
    },
    getData(e, key) {
      this.mechanismForm[key] = e[0]
      console.log(this.mechanismForm[key])
    }
  }
}
</script>

<style scoped lang="scss">
.mechanism-form{
  .header {
    display: flex;
    align-items: center;
    gap: 20px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
  }
  .c_header{
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
    .tips{
      color: #999;
    }
  }
  .submit{
    display: flex;
    justify-content: center;
  }
  .attachments-upload{
    display: flex;
    flex-direction: column;
    gap: 10px;
    .tips{
      color: #999;
    }
  }
  .addDiv {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .el-icon-plus{
      font-size: 16px;
      color: #409EFF;
    }
    .txt {
      width: 100%;
      height: 20px;
      font-size: 12px;
      color: #999999;
      text-align: center;
      line-height: 1.5;
      transform: scale(0.8);
      span{
        color: #409EFF;
      }
    }
  }
}
</style>
