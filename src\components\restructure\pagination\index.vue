<!--
 * @Author: 陈小豆
 * @Date: 2023-07-21 14:56:17
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-27 11:56:21
-->
<template>
  <div class="pagination-container">
    <el-pagination
      :page-sizes="fy?fy:[10, 20, 50, 100, 500]"
      :page-size.sync="getPageSize"
      :current-page.sync="getCurrent"
      layout="total, sizes, prev, pager, next, jumper"
      class="pagination"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'Index',
  props: {
    total: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
    current: {
      type: Number,
      default: 1
    },
    fy:{
      type: Array,
      default: null
    },
  },
  data() {
    return {
    }
  },
  computed: {
    getPageSize: {
      get() {
        return this.pageSize
      },
      set(val) {
        this.$emit('update:pageSize', val)
      }
    },
    getCurrent: {
      get() {
        return this.current
      },
      set(newVal) {
        this.$emit('update:current', newVal)
      }
    }
  },
  methods: {
    handleSizeChange(val) { /* 条数切换触发*/
      this.$emit('handleSizeChange', val)
      scrollTo(0, 0)
    },
    handleCurrentChange(val) { /* 页数切换触发*/
      this.$emit('handleCurrentChange', val)
      scrollTo(0, 0)
    }
  }
}
</script>

<style scoped>
  .pagination-container {
    background: #fff;
    /* padding: 32px 16px; */
  }
  .pagination-container.hidden {
    display: none;
  }
  .pagination {
    text-align: right;
  }
</style>

