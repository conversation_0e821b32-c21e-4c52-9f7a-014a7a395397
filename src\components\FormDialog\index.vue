<template>
  <el-dialog
    v-bind="$attrs"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :before-close="beforeClose"
    v-on="$listeners"
    @close="handleClose"
  >
    <el-form ref="formRef" v-loading="formLoading" :model="formModel" :rules="rules" :label-width="labelWidth" v-bind="formProps">
      <slot />

      <el-form-item>
        <el-button v-if="showCancel" :disabled="submitLoading" @click="$emit('change', false)">{{ cancelText }}</el-button>
        <el-button v-if="showConfirm" :loading="submitLoading" type="primary" @click="handleConfirm">{{ confirmText }}</el-button>
        <slot name="footer-button" />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'FormDialog',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 弹窗显示|隐藏
    value: Boolean,
    // 表单原数据
    formModel: {
      type: Object,
      default: () => {}
    },
    // 表单校验规则
    rules: {
      type: Object,
      default: () => {}
    },
    // 表单label宽度
    labelWidth: {
      type: String,
      default: '100px'
    },
    // 其他表单配置项
    formProps: {
      type: Object,
      default: () => {}
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: '确认'
    },
    // 是否显示确认按钮
    showConfirm: {
      type: Boolean,
      default: true
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      formLoading: false,
      submitLoading: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    beforeClose(done) {
      if (this.formLoading) return false

      done()
    },
    handleClose() {
      this.resetForm()
      // 关闭loading
      this.submitLoading && this.formDone()
      this.formLoading && (this.formLoading = false)
      // 抛出事件
      this.$emit('close')
    },
    // 编辑数据回显
    async loadFormData(fetchFn, processor) {
      this.formLoading = true
      try {
        const { code, data } = await fetchFn()
        if (code !== 200) return false

        const processed = processor ? processor(data) : data
        Object.assign(this.formModel, processed)
      } catch (err) {
        console.log(err)
      } finally {
        this.formLoading = false
      }
    },
    handleConfirm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return false
        this.submitLoading = true
        this.$emit('submit', this.formModel, this.formDone)
      })
    },
    // 提交完成
    formDone() {
      this.submitLoading = false
    },
    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
    }
  }
}
</script>
