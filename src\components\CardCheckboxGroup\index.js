import CardCheckboxGroup from './index.vue'
import CardCheckboxGroups from './group.vue'

// 可以单独引入
export { CardCheckboxGroup, CardCheckboxGroups }

// 批量注册
const components = [
  CardCheckboxGroup,
  CardCheckboxGroups
]

// install 方法
const install = function(Vue) {
  components.forEach(component => {
    Vue.component(component.name, component)
  })
}

// 自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  install,
  CardCheckboxGroup,
  CardCheckboxGroups
} 