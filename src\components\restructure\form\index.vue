<template>
  <el-form
    ref="ruleForm"
    :model="resData"
    :inline="displayState"
    :label-width="labelWidth"
    class="demo-ruleForm"
    :label-position="labelPosition"
    :size="size"
  >
    <el-form-item
      v-for="(item,index) in copy(formItemList)"
      :key="index"
      :label="item.titleHidden?'':item.options && item.options.title?item.options.title:item.title"
      :rules="getReg(item,index)"
      :prop="item.key"
      :label-width="item.labelWidth"
    >
      <SRender :results="results" :item="item" :val.sync="resData[item.key]">
        <div v-if="item.slot" :slot="item.slot">
          <slot :name="item.slot" />
        </div>
      </SRender>
    </el-form-item>
    <slot name="formContainer" />
  </el-form>
</template>
<script>
import { regular } from '@/libs/validate'
import { keyWord } from '@/config/sysConfig'
import SRender from './components/render/index'
import mixins from './mixins'
import { copy } from '@/config/basicsMethods'

export default {
  components: {
    SRender
  },
  mixins: [mixins],
  props: {
    size: {
      type: String,
      default: 'small'
    },
    labelPosition: {
      type: String,
      default: 'right'
    },
    formItemList: {
      type: Array,
      default: () => []
    },
    displayState: {
      type: Boolean,
      default: false
    },
    regState: {
      type: Boolean,
      default: true
    },
    labelWidth: {
      type: String,
      default: '80px'
    },
    results: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      copy: copy,
      trigger: ['blur'],
      resData: {},
      // 原始值
      originData: {}
    }
  },
  watch: {
    formItemList: {
      handler(to) {
        this.ruleForm()
      },
      deep: true
    },
    resData: {
      handler(to) {
        this.$emit('changeForm', to)
      },
      deep: true
    }
  },
  created() {
    this.initForm()
    this.$emit('formMount')
  },
  methods: {
    initForm() {
      this.ruleForm(async(item, index) => {
        if (item.requestList && (this.basics.isNull(item.list) || this.basics.isArrNull(item.list))) {
          item.list = []
          await this.request_list(item, index)
        }
      })
    },
    ruleForm(fn) {
      const tempObj = {}
      this.formItemList.map((item, index) => {
        if (!item.slot && !this.basics.isNull(item.key)) {
          const getKey = item.key
          item.val = this.basics.isNull(item.val) ? this.setItemVal(this.formItemList[index]) : this.getItemVal(this.formItemList[index], item.val)
          item.list = item.list ? item.list : this.$set(this.formItemList[index], 'list', [])
          const getVal = item.val
          if (!this.basics.isNull(getKey)) {
            if (this.basics.isNull(this.resData[getKey]) || (this.basics.isArray(this.resData[getKey]) && this.basics.isArrNull(this.resData[getKey]))) {
              tempObj[getKey] = this.basics.isNull(getVal) ? '' : getVal
            } else {
              tempObj[getKey] = this.resData[getKey]
            }
          }
        }
        if (fn) fn(item, index)
      })
      this.resData = tempObj
      if (!Object.keys(this.originData).length) {
        this.originData = copy(this.resData)
      }
    },

    setItemVal(item) {
      if (item.type.indexOf(keyWord.multiple) >= 0) {
        return []
      } else {
        return ''
      }
    },

    getItemVal(item, val) {
      if (item.type.indexOf(keyWord.multiple) >= 0 && this.basics.isString(val)) {
        return val.split(keyWord.relatedWords)
      } else {
        return val
      }
    },
    request_list(item, index) {
      item.requestList().then(msg => {
        this.$set(this.formItemList[index], 'list', msg.data)
      })
    },

    getReg(item, index) {
      if (!this.regState) return []
      const itemReg = item.reg
      const itmeType = item.key
      let tempRegObj = []
      if (this.basics.isNull(itemReg)) {
        return tempRegObj
      } else if (this.basics.isString(itemReg)) {
        tempRegObj = this.setReg([itemReg], itmeType)
      } else if (this.basics.isArray(itemReg)) {
        tempRegObj = this.setReg(itemReg, itmeType)
      } else {
        throw new Error('不支持当前验证类型')
      }
      return tempRegObj
    },

    setReg(regStrArr, type) {
      return regStrArr.map(item => {
        if (this.basics.isObj(item)) {
          return item
        } else {
          if (item === 'required') return { required: true, message: '此项不能为空', trigger: this.trigger }
          return Object.assign({}, { trigger: this.trigger, validator: regular(item) })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 处理表单数据
    handleFormData(data) {
      const params = copy(this.resData)
      for (const i in params) {
        for (const item in this.formItemList) {
          const current = this.formItemList[item]
          if (i === current.key) {
            const data = params[i]
            if (current.childKey && this.basics.isArray(current.childKey)) {
              current.childKey.forEach((item, index) => {
                params[item] = data[index] || ''
              })
              delete params[i]
            } else if (this.basics.isArray(data)) {
              params[i] = data.join(keyWord.relatedWords)
            }
          }
        }
      }

      return params
    },
    clearForm() {
      this.resData = copy(this.originData)
      return this.handleFormData(this.resData)
    }
  }
}
</script>
