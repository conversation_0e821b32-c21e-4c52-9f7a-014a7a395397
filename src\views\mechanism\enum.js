// 机构管理枚举

// 机构类型
export const MechanismTypeEnum = {
  // 小贷
  SMALL: 1,
  // 融资租赁
  LEASING: 2,
  // 典当
  PAWN: 3,
  // 消金
  CONSUMER_FINANCE: 4,
  // 银行
  BANK: 5,
  // 融资担保
  FINANCING_GUARANTEE: 6,
  // 中介
  AGENCY: 7,
  // 代理
  AGENT: 8
}
export const MechanismTypeMap = {
  [MechanismTypeEnum.SMALL]: '小贷',
  [MechanismTypeEnum.LEASING]: '融资租赁',
  [MechanismTypeEnum.PAWN]: '典当',
  [MechanismTypeEnum.CONSUMER_FINANCE]: '消金',
  [MechanismTypeEnum.BANK]: '银行',
  [MechanismTypeEnum.FINANCING_GUARANTEE]: '融资担保',
  [MechanismTypeEnum.AGENCY]: '中介',
  [MechanismTypeEnum.AGENT]: '代理'
}
export const MechanismTypeList = Object.keys(MechanismTypeEnum).map(key => ({
  id: MechanismTypeEnum[key],
  name: MechanismTypeMap[MechanismTypeEnum[key]]
}))

// api机构
export const ApiMechanismEnum = {
  // 是
  YSE: 1,
  // 否
  NO: 0
}
export const ApiMechanismMap = {
  [ApiMechanismEnum.YSE]: '是',
  [ApiMechanismEnum.NO]: '否'
}
export const ApiMechanismList = Object.keys(ApiMechanismEnum).map(key => ({
  id: ApiMechanismEnum[key],
  name: ApiMechanismMap[ApiMechanismEnum[key]]
}))

// 合作状态
export const CooperationStatusEnum = {
  // 合作中
  COOPERATING: 1,
  // 已停用
  STOP: 0
}
export const CooperationStatusMap = {
  [CooperationStatusEnum.COOPERATING]: '合作中',
  [CooperationStatusEnum.STOP]: '已停用'
}
export const CooperationStatusList = Object.keys(CooperationStatusEnum).map(key => ({
  id: CooperationStatusEnum[key],
  name: CooperationStatusMap[CooperationStatusEnum[key]]
}))

// 机构状态
export const MechanismStatusEnum = {
  // 启用
  ENABLE: 1,
  // 禁用
  DISABLE: 0
}
export const MechanismStatusMap = {
  [MechanismStatusEnum.ENABLE]: '启用',
  [MechanismStatusEnum.DISABLE]: '禁用'
}
export const MechanismStatusList = Object.keys(MechanismStatusEnum).map(key => ({
  id: MechanismStatusEnum[key],
  name: MechanismStatusMap[MechanismStatusEnum[key]]
}))

// 交易类型
export const TransactionTypeEnum = {
  // 充值
  RECHARGE: 1,
  //   扣款
  DEDUCTION: 2,
  // 保证金充值
  MARGIN_RECHARGE: 3,
  //   赠送
  GIFT: 4
}
export const TransactionTypeMap = {
  [TransactionTypeEnum.RECHARGE]: '充值',
  [TransactionTypeEnum.DEDUCTION]: '扣款',
  [TransactionTypeEnum.MARGIN_RECHARGE]: '保证金充值',
  [TransactionTypeEnum.GIFT]: '赠送'
}
export const TransactionTypeList = Object.keys(TransactionTypeEnum).map(key => ({
  id: TransactionTypeEnum[key],
  name: TransactionTypeMap[TransactionTypeEnum[key]]
}))
