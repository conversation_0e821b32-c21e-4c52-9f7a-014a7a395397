<template>
  <div style="background: #fff; padding: 30px 25px">
    <div style="margin-bottom: 25px; display: flex;">
      <el-radio-group v-model="searchDay" style="margin-right: 15px" @change="getData">
        <el-radio-button label="7">7天</el-radio-button>
        <el-radio-button label="30">30天</el-radio-button>
        <el-radio-button label="90">90天</el-radio-button>
      </el-radio-group>
      <el-date-picker
        v-model="timeArr"
        :picker-options="basics.pickerOptions()"
        type="daterange"
        range-separator="~"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :clearable="true"
        value-format="yyyy-MM-dd"
        style="width: 290px;margin-right: 15px"
      />
      <el-button type="primary" @click="getData">确定</el-button>
    </div>
    <section class="color-tips">
      <el-tabs type="card" @tab-click="handleClick">
        <el-tab-pane label="H5渠道">
          <el-radio-group v-model="searchType" @change="getData">
            <el-radio-button label="/statistic/h5/visit">H5访问PV/UV</el-radio-button>
            <el-radio-button label="/statistic/h5/buycard">购卡用户数</el-radio-button>
            <el-radio-button label="/statistic/h5/cardamount">购卡金额</el-radio-button>
            <el-radio-button label="/statistic/h5/keywordCount">关键数据指标</el-radio-button>
          </el-radio-group>
        </el-tab-pane>
        <el-tab-pane label="APP站内">
          <el-radio-group v-model="searchType" @change="getData">
            <el-radio-button label="/statistic/app/loginsign">APP登录用户数</el-radio-button>
            <el-radio-button label="/statistic/app/orderUserCount">下单用户数</el-radio-button>
            <el-radio-button label="/statistic/app/ordinaryUserCount">APP登录普通用户</el-radio-button>
            <el-radio-button label="/statistic/app/activateUserCount">激活用户数</el-radio-button>
            <el-radio-button label="/statistic/app/rebateOrderCount">返利订单数</el-radio-button>
            <el-radio-button label="/statistic/app/rebateUserCount">返利下单用户</el-radio-button>
            <el-radio-button label="/statistic/app/rebateProfitAmountCount">返利商品收益</el-radio-button>
            <el-radio-button label="/statistic/app/rightsOrderCount">权益订单数</el-radio-button>
            <el-radio-button label="/statistic/app/rightsUserCount">权益下单用户数</el-radio-button>
            <el-radio-button label="/statistic/app/rightsProfitCount">权益商品收益</el-radio-button>
            <el-radio-button label="/statistic/app/arpuCount">arpu值</el-radio-button>
            <el-radio-button label="/statistic/app/average">人均笔数</el-radio-button>
          </el-radio-group>
        </el-tab-pane>
      </el-tabs>
    </section>

    <div v-loading="loading" class="yc-home-content">
      <div ref="lineChart" class="chart-content" />
    </div>
  </div>
</template>

<script>
// 引入 ECharts 主模块
import echarts from 'echarts'
import { get } from '@/libs/axios.package'
import moment from 'moment'

export default {
  name: 'line-chart',
  data() {
    return {
      option: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          right: '2%',
          data: []
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '15%',
          containLabel: true
        },
        color: ['#FA7A42', '#358EF3', '#99CC66', '#663300'],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [] //
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          type: 'value'
        },
        dataZoom: [
          {
            type: 'inside'
          },
          {
            handleIcon:
              'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ],
        series: []
      },
      timeArr: [
        moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD')
      ],
      searchDay: '7',
      searchType: '/statistic/h5/visit',
      chart: null,
      loading: false
    }
  },
  mounted() {
    const that = this
    this.$nextTick(() => {
      this.chart = echarts.init(this.$refs.lineChart)
      window.addEventListener('resize', function() {
        that.chart.resize()
      })
      this.getData()
    })
  },
  methods: {
    handleClick(tab) {},
    getData() {
      this.loading = true
      let data = {}
      if (this.timeArr && this.timeArr.length > 0) {
        data = {
          startTime: this.timeArr[0],
          endTime: this.timeArr[1],
          day: this.searchDay
        }
      } else {
        data = {
          startTime: '',
          endTime: '',
          day: this.searchDay
        }
      }
      this.option.series = []
      this.option.xAxis.data = []
      switch (this.searchType) {
        case '/statistic/h5/visit':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.pv)
              this.option.series[0].name = 'PV'
              this.option.series[1].data.push(item.uv)
              this.option.series[1].name = 'UV'
              this.option.legend.data = ['PV', 'UV']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/h5/buycard':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[0].name = '新'
              this.option.series[1].data.push(item.total)
              this.option.series[1].name = '总'
              this.option.legend.data = ['新', '总']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/h5/cardamount':
          for (let i = 0; i < 1; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.amount)
              this.option.series[0].name = '购卡金额'
              this.option.legend.data = ['购卡金额']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/h5/keywordCount':
          for (let i = 0; i < 3; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.h5BuyCardRate)
              this.option.series[0].name = '下单转化'
              this.option.series[1].data.push(item.buyCardActiveRate)
              this.option.series[1].name = '激活转化'
              this.option.series[2].data.push(item.channelBuyCardLoginRate)
              this.option.series[2].name = 'APP登录转化'
              this.option.legend.data = ['下单转化', '激活转化', 'APP登录转化']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/loginsign':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[0].name = '新'
              this.option.series[1].data.push(item.total)
              this.option.series[1].name = '总'
              this.option.legend.data = ['新', '总']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/orderUserCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.newUserOrderCount)
              this.option.series[0].name = '下单新用户数'
              this.option.series[1].data.push(item.totalUserCount)
              this.option.series[1].name = '下单总用户数'
              this.option.legend.data = ['下单新用户数', '下单总用户数']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/ordinaryUserCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[0].name = 'APP登录普通用户(新)'
              this.option.series[1].data.push(item.total)
              this.option.series[1].name = '激活用户数(总)'
              this.option.legend.data = ['APP登录普通用户(新)', 'APP登录普通用户(总)']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/activateUserCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[0].name = '激活用户数(新)'
              this.option.series[1].data.push(item.total)
              this.option.series[1].name = '激活用户数(总)'
              this.option.legend.data = ['激活用户数(新)', '激活用户数(总)']
            })
            console.log(this.option)
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rebateOrderCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.rebateCount)
              this.option.series[0].name = '返利订单数'
              this.option.series[1].data.push(item.cancelRebateCount)
              this.option.series[1].name = '返利订单取消数'
              this.option.legend.data = ['返利订单数', '返利订单取消数']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rebateUserCount':
          for (let i = 0; i < 3; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[0].name = '返利下单用户(新)'
              this.option.series[1].data.push(item.total)
              this.option.series[1].name = '返利下单用户(总)'
              this.option.series[2].data.push(item.cancel)
              this.option.series[2].name = '返利取消用户'
              this.option.legend.data = ['返利下单用户(新)', '返利下单用户(总)', '返利取消用户']
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rebateProfitAmountCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.userProfitAmount)
              this.option.series[1].data.push(item.profitAmount)
              this.option.series[0].name = '用户返利金额'
              this.option.series[1].name = '返利商品收益'
              this.option.legend.data = [
                '用户返利金额',
                '返利商品收益'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rightsOrderCount':
          for (let i = 0; i < 2; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.num)
              this.option.series[1].data.push(item.amount)
              this.option.series[0].name = '权益订单数'
              this.option.series[1].name = '权益下单金额'
              this.option.legend.data = [
                '权益订单数',
                '权益下单金额'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rightsUserCount':
          for (let i = 0; i < 4; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.newOrderUserCount)
              this.option.series[1].data.push(item.totalOrderUserCount)
              this.option.series[2].data.push(item.newPayUserCount)
              this.option.series[3].data.push(item.totalPayUserCount)
              this.option.series[0].name = '权益下单用户(新)'
              this.option.series[1].name = '权益下单用户(总)'
              this.option.series[2].name = '权益支付成功用户(新)'
              this.option.series[3].name = '权益支付成功用户(总)'
              this.option.legend.data = [
                '权益下单用户(新)',
                '权益下单用户(总)',
                '权益支付成功用户(新)',
                '权益支付成功用户(总)'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/rightsProfitCount':
          for (let i = 0; i < 1; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.amount)
              this.option.series[0].name = '权益商品收益'
              this.option.legend.data = [
                '权益商品收益'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/arpuCount':
          for (let i = 0; i < 1; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.arpu)
              this.option.series[0].name = 'arpu值'
              this.option.legend.data = [
                'arpu值'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
        case '/statistic/app/average':
          for (let i = 0; i < 1; i++) {
            this.option.series.push({
              name: '',
              type: 'line',
              smooth: true,
              data: []
            })
          }
          get(this.searchType, data).then(res => {
            res.data.map(item => {
              this.option.xAxis.data.push(item.date)
              this.option.series[0].data.push(item.average)
              this.option.series[0].name = '平均笔数'
              this.option.legend.data = [
                '平均笔数'
              ]
            })
            this.chart.clear()
            this.chart.setOption(this.option)
            this.loading = false
          })
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-content {
  width: 100%;
  height: 400px;
}

.yc-home-content {
  border-bottom: 1px solid #dfdfdf;
  padding: 20px;
}

.home-sub-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(51, 54, 60, 1);
  line-height: 22px;
  padding-left: 10px;
  margin: 25px 0;
  position: relative;

  &:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 5px;
    background: #34a0ff;
  }
}

.item-title-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: auto;
  border-radius: 4px 4px 0 0;
}

.colorWrap {
  display: inline-flex;

  .clr {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
  }

  .clr:last-child {
    margin-right: 0;
  }

  .color {
    width: 40px;
    height: 20px;
    display: inline-block;
    margin-right: 10px;
    border-radius: 2px;
  }

  .name {
    font-size: 14px;
    color: #666;
  }
}

.color-tips {
  display: flex;
  justify-content: space-between;
}
</style>
