<!--账号管理-->
<template>
  <div>
    <el-dialog
      title="账号管理"
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @open="handleOpen"
      @close="handleDialogClose"
    >
      <page v-if="roleDefaults" ref="pageRef" :request="request" :list="list">
        <div slot="searchContainer" style="display: inline-block">
          <el-button size="small" type="primary" @click="handleCreate(null)">新增</el-button>
        </div>
      </page>
    </el-dialog>
    <UserForm v-model="userFormShow" :agencies-id="id" :form-id="userId" @close="handleClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import moment from 'moment'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { MechanismStatusList } from '@/views/mechanism/enum'
import UserForm from './UserForm.vue'
import { getSassAccountListApi, getSassRoleListApi, changeSassAccountStatusApi, resetSassAccountPwdApi } from '@/api/mechanism'

export default {
  name: 'AccountMan',
  components: { page, UserForm },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      roleDefaults: null,
      userId: null,
      roleOptions: [],
      userFormShow: false,
      listQuery: {
        agenciesId: this.id,
        startDate: moment().subtract(5, 'days').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        keywords: '',
        roleId: this.roleDefaults,
        status: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getSassAccountListApi({
            ...this.listQuery,
            agenciesId: this.id
          })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    list() {
      return [
        {
          title: '账号信息',
          key: 'keywords',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          val: '',
          options: {
            placeholder: '请输入账号/手机号/姓名'
          }
        },
        {
          title: '角色',
          key: 'roleId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.roleOptions,
          disabled: true,
          val: this.roleDefaults,
          options: {
            placeholder: '请选择角色'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismStatusList,
          options: {
            placeholder: '请选择状态'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '序号',
          render: (_h, params) => {
            return <div>{params.data.$index + 1}</div>
          },
          width: 80
        },
        {
          title: '用户ID',
          key: 'id'
        },
        {
          title: '用户姓名',
          key: 'nickName'
        },
        {
          title: '登录名',
          key: 'userName'
        },
        {
          title: '手机号码',
          key: 'mobileNo'
        },
        {
          title: '角色',
          key: 'roleId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.roleOptions,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 170,
          activeType: [
            {
              text: '编辑',
              key: 'detail',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleCreate(params.id)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              render: (_h, params) => {
                return <el-button type='primary' plain onClick={() => this.changeMechanismStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            },
            {
              text: '重置密码',
              key: 'qualifications',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.resetPassword(params)
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    // 重置密码
    resetPassword(row) {
      this.$prompt('最少8位,不允许输入空格，数字、特殊字符、大小写字母都可以,最大60位', '重置密码', {
        closeOnClickModal: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /^[^\s]{8,60}$/,
        inputErrorMessage: '请输入正确的密码'
      }).then(async({ value }) => {
        try {
          await resetSassAccountPwdApi({
            id: row.id,
            password: value
          })

          this.$message({
            type: 'success',
            message: '操作成功'
          })
        } catch (err) {
          console.log(err)
        }
      })
    },
    // 改变账户状态
    changeMechanismStatus(row) {
      this.$confirm(`用户编号：${row.id}`, `确认${row.status === 1 ? '禁用' : '启用'}此账号？`, {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async() => {
        try {
          await changeSassAccountStatusApi({
            id: row.id,
            status: row.status === 1 ? 0 : 1
          })

          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    },
    handleCreate(id) {
      this.userId = id
      this.userFormShow = true
    },
    async handleOpen() {
      await this.getSassRoleList()
      this.$nextTick(() => {
        this.$refs.pageRef.exposeReset()
      })
    },
    handleDialogClose() {
      this.roleDefaults = null
    },
    handleClose() {
      this.userId = null
      this.$store.dispatch('tableRefresh', this)
    },
    // 获取角色
    async getSassRoleList() {
      try {
        const { data } = await getSassRoleListApi({
          agenciesId: this.id
        })

        this.roleOptions = data
        this.roleDefaults = data.find(item => item.code === 'SUPER_ROLE').id
        this.listQuery.roleId = this.roleDefaults
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>
