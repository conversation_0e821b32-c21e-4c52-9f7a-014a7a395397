<template>
  <div>
    <div v-if="dialogVisible">
      <el-dialog :top="top" :close-on-click-modal="closeOnClickModal" :close-on-press-escape="closeOnPressEscape" :width="data.width||'30%'" :fullscreen="data.fullscreen || false" :title="data.title||''" :visible.sync="state" :append-to-body="data.appendToBody||false">
        <slot />
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Index',
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {
          width: '30%'
        }
      }
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    top: {
      type: String,
      default: '20vh'
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.dialogFormVisible,
      state: this.dialogFormVisible
    }
  },
  watch: {
    dialogVisible(to) {
      if (!to) {
        this.$emit('update:dialogFormVisible', to)
      }
      setTimeout(() => {
        this.state = to
      })
    },
    dialogFormVisible(to) {
      this.dialogVisible = to
    },
    state(to) {
      if (!to) {
        setTimeout(() => {
          this.dialogVisible = to
        })
      }
    }
  },
  destroyed() {
    this.$emit('close')
  },
  mounted() {
    this.$emit('open')
  }
}
</script>

<style scoped>

</style>
