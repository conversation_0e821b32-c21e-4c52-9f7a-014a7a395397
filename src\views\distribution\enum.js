// 分发策略枚举

// 机构策略状态
export const ApiMechanismStrategyStatusEnum = {
  // 启用
  Enable: 1,
  // 禁用
  Disable: 0
}
export const ApiMechanismStrategyStatusMap = {
  [ApiMechanismStrategyStatusEnum.Enable]: '启用',
  [ApiMechanismStrategyStatusEnum.Disable]: '禁用'
}
export const ApiMechanismStrategyStatusList = Object.keys(ApiMechanismStrategyStatusEnum).map(key => ({
  id: ApiMechanismStrategyStatusEnum[key],
  name: ApiMechanismStrategyStatusMap[ApiMechanismStrategyStatusEnum[key]]
}))

// 机构策略类型
export const ApiMechanismStrategyTypeEnum = {
  // 指定优先
  SpecifiedPriority: 1,
  // 竞价模式
  BiddingMode: 2,
  // 指定城市
  SpecifiedCity: 3,
  // 顺序分发
  SequentialDistribution: 4
}
export const ApiMechanismStrategyTypeMap = {
  [ApiMechanismStrategyTypeEnum.SpecifiedPriority]: '指定优先',
  [ApiMechanismStrategyTypeEnum.BiddingMode]: '竞价模式',
  [ApiMechanismStrategyTypeEnum.SpecifiedCity]: '指定城市',
  [ApiMechanismStrategyTypeEnum.SequentialDistribution]: '顺序分发'
}
export const ApiMechanismStrategyTypeList = Object.keys(ApiMechanismStrategyTypeEnum).map(key => ({
  id: ApiMechanismStrategyTypeEnum[key],
  name: ApiMechanismStrategyTypeMap[ApiMechanismStrategyTypeEnum[key]]
}))

// 总策略线索来源
export const OverallStrategyClueSourceEnum = {
  // 优易润
  YouYiRun: 1,
  // 数信
  ShunXin: 2
}
export const OverallStrategyClueSourceMap = {
  [OverallStrategyClueSourceEnum.YouYiRun]: '优易润',
  [OverallStrategyClueSourceEnum.ShunXin]: '数信'
}
export const OverallStrategyClueSourceList = Object.keys(OverallStrategyClueSourceEnum).map(key => ({
  id: OverallStrategyClueSourceEnum[key],
  name: OverallStrategyClueSourceMap[OverallStrategyClueSourceEnum[key]]
}))

// 不接收重复线索类型枚举
export const NotReceiveRepeatClueTypeEnum = {
  // 秒
  Second: 1,
  // 分
  Minute: 2,
  // 小时
  Hour: 3,
  // 天
  Day: 4
}
export const NotReceiveRepeatClueTypeMap = {
  [NotReceiveRepeatClueTypeEnum.Second]: '秒',
  [NotReceiveRepeatClueTypeEnum.Minute]: '分',
  [NotReceiveRepeatClueTypeEnum.Hour]: '小时',
  [NotReceiveRepeatClueTypeEnum.Day]: '天'
}
export const NotReceiveRepeatClueTypeList = Object.keys(NotReceiveRepeatClueTypeEnum).map(key => ({
  id: NotReceiveRepeatClueTypeEnum[key],
  name: NotReceiveRepeatClueTypeMap[NotReceiveRepeatClueTypeEnum[key]]
}))

// 推送排序枚举
export const PushSortEnum = {
  // 自营-资方-机构
  SelfOperating: 1
}
export const PushSortMap = {
  [PushSortEnum.SelfOperating]: '自营-资方-机构'
}
export const PushSortList = Object.keys(PushSortEnum).map(key => ({
  id: PushSortEnum[key],
  name: PushSortMap[PushSortEnum[key]]
}))

// 分发条件枚举
export const DistributionConditionEnum = {
  // 拒量分发
  RejectDistribution: 1
}
export const DistributionConditionMap = {
  [DistributionConditionEnum.RejectDistribution]: '拒量分发'
}
export const DistributionConditionList = Object.keys(DistributionConditionEnum).map(key => ({
  id: DistributionConditionEnum[key],
  name: DistributionConditionMap[DistributionConditionEnum[key]]
}))

// 分发模式枚举
export const DistributionModeEnum = {
  // 均分分发
  EqualDistribution: 1,
  // 随机分发
  RandomDistribution: 2
}
export const DistributionModeMap = {
  [DistributionModeEnum.EqualDistribution]: '均分分发',
  [DistributionModeEnum.RandomDistribution]: '随机分发'
}
export const DistributionModeList = Object.keys(DistributionModeEnum).map(key => ({
  id: DistributionModeEnum[key],
  name: DistributionModeMap[DistributionModeEnum[key]]
}))

// 策略点类型枚举
export const StrategyPointTypeEnum = {
  // 产品模式
  ProductMode: 1,
  // 竞价模式
  BiddingMode: 2,
  // 在线模式
  OnlineMode: 3
}
export const StrategyPointTypeMap = {
  [StrategyPointTypeEnum.ProductMode]: '产品模式',
  [StrategyPointTypeEnum.BiddingMode]: '竞价模式',
  [StrategyPointTypeEnum.OnlineMode]: '在线模式'
}

// 产品模式类型枚举
export const ProductModeTypeEnum = {
  // 按产品优先级
  ByProductPriority: 1
}
export const ProductModeTypeMap = {
  [ProductModeTypeEnum.ByProductPriority]: '按产品优先级'
}
export const ProductModeTypeList = Object.keys(ProductModeTypeEnum).map(key => ({
  id: ProductModeTypeEnum[key],
  name: ProductModeTypeMap[ProductModeTypeEnum[key]]
}))

// 竞价模式类型枚举
export const BiddingModeTypeEnum = {
  // 报价由高到低
  QuoteFromHighToLow: 1
}
export const BiddingModeTypeMap = {
  [BiddingModeTypeEnum.QuoteFromHighToLow]: '报价由高到低'
}
export const BiddingModeTypeList = Object.keys(BiddingModeTypeEnum).map(key => ({
  id: BiddingModeTypeEnum[key],
  name: BiddingModeTypeMap[BiddingModeTypeEnum[key]]
}))

// 在线模式枚举
export const OnlineModeEnum = {
  // 在线优先
  OnlinePriority: 1,
  // 不优先
  NotPriority: 2
}
export const OnlineModeMap = {
  [OnlineModeEnum.OnlinePriority]: '在线优先',
  [OnlineModeEnum.NotPriority]: '不优先'
}
export const OnlineModeList = Object.keys(OnlineModeEnum).map(key => ({
  id: OnlineModeEnum[key],
  name: OnlineModeMap[OnlineModeEnum[key]]
}))
