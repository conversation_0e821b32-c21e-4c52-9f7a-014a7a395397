<template>
  <div style="display: flex;align-items: center;">
    <div class="breadcrumb-wrap">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ name: 'home' }"><span>首页</span></el-breadcrumb-item>
        <el-breadcrumb-item v-for="(bread,index) in breadList" :key="index">
          <router-link :to="{path:bread.path}" v-if="index !== 0 && index+1 !== breadList.length">{{bread.title}}
          </router-link>
          <span v-else class="no-redirect">{{bread.title}}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script>

  export default {
    name: 'breadcrumb',
    data() {
      return {
        levelList: null,
        breadList: []
      }
    },
    watch: {
      $route(to) {
        this.getBreadList();
      },
    },
    created() {
      this.getBreadList();
    },
    methods: {
      getBreadList() {
        let thar = this;
        thar.breadList = [];
        if (!this.$utils.includeValue(this.$CONSTANT.noLoginMenu, this.$route.path)) {
          for (const routeInfo of this.$route.matched) {
            let path = routeInfo.path;
            if (routeInfo.path === this.$route.path) {
              path = this.$route.fullPath;
            }
            thar.breadList.push({
              title: routeInfo.meta.title,
              path: path
            });
          }
          if (this.$route.meta.activeMenu) {
            thar.breadList.splice(thar.breadList.length - 1, 0, {
              title: this.$route.meta.parentTitle,
              path: this.$route.meta.activeMenu
            });
          }
        }
      },
      onCollapse(){
        this.$emit('changeCollapse')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .breadcrumb-wrap {
    display: inline-block;
    width: 400px;
    height: 50px;

    padding-top: 20px;

    * {
      font-size: 14px;
    }

    .no-redirect {
      color: #656f7d;
      cursor: text;
    }
  }

</style>
