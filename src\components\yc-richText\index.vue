<template lang="">
  <div class="rich-text">
    <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editor"
        :default-config="toolbarConfig"
        :mode="mode"
      />
      <Editor
        v-model="html"
        style="height: 400px; overflow-y: hidden"
        :default-config="editorConfig"
        :mode="mode"
        @onCreated="onCreated"
      />
    </div>
  </div>
</template>
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import GLOBAL from '@/config/constant.conf'

export default {
  name: 'RichText',
  desc: '富文本编辑器',
  components: {
    Editor, Toolbar
  },
  props: {
    mode: {
      default: 'default' // or 'simple'
    }
  },
  data() {
    return {
      editor: null,
      html: '',
      toolbarConfig: {},
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: { // 配置文档 https://www.wangeditor.com/v5/toolbar-config.html
          uploadImage: {
            server: `${GLOBAL.publicPath}/upload/image`,
            fieldName: 'file',
            headers: { Authorization: `${this.$store.getters.authorization}` },
            withCredentials: true, // 跨域是否传递 cookie ，默认为 false
            timeout: 5 * 1000, // 超时时间，默认为 10 秒
            async customInsert(file, insertFn) {
              insertFn(file.data)
            }
          }
        }
      }
    }
  },

  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    }
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style lang="">
</style>
