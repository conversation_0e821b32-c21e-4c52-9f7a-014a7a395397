<template>
  <el-form ref="form" :model="ruleForm" :rules="addRules" label-width="80px" class="demo-ruleForm">
    <el-form-item :label="moneyLabel" prop="rechargeAmount" :rules="addRules.money">
      <el-input v-model="ruleForm.rechargeAmount" maxlength="11" type="text" oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')" @blur="ruleForm.rechargeAmount = $event.target.value">
        <template slot="suffix">元</template>
      </el-input>
    </el-form-item>
    <el-form-item label="备注" maxlength="200" prop="remark" :rules="addRules.common">
      <el-input v-model="ruleForm.remark" type="textarea" />
    </el-form-item>
    <el-form-item>
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  name: 'addMoneyForm',
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: null
    }
  },

  data() {
    var validMax = (rule, value, callback) => {
      if (Number(value) > 99999999.99) {
        callback(new Error('金额不能大于99999999.99'))
      } else {
        callback()
      }
    }
    return {
      ruleForm: { rechargeAmount: '', remark: '' },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        money: [{ required: true, message: '此项不能为空', trigger: 'blur' },
          { required: true, message: '此项不能为空', trigger: 'change' },
          { validator: validMax, trigger: 'blur' }]
      }
    }
  },
  computed: {
    moneyLabel() {
      return this.type === 'deduct' ? '扣除金额' : '充值'
    }
  },

  methods: {
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.$emit('submit', { ...this.ruleForm, id: this.id })
        }
      })
    }
  }
}
</script>
  <style lang="scss" scoped></style>
