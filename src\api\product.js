// 产品管理接口
import { get, post, put } from '@/libs/axios.package'

// 获取产品类型list
export const getProductTypeListApi = () => {
  return get('/product/name')
}
// 获取产品管理list
export const getProductListApi = obj => {
  return get('/product/page', obj)
}
// 新增产品
export const createProductApi = obj => {
  return post('/product', obj)
}
// 编辑产品
export const editProductApi = obj => {
  return put('/product', obj)
}
// 更新产品状态
export const changeProductStatusApi = (id, status) => {
  return put(`/product/${id}/${status}`)
}
// 查询产品详情
export const getProductInfoApi = id => {
  return get(`/product/${id}`)
}
