export default {
  state: {
    searchData: JSON.parse(localStorage.getItem('searchList')) || {}
  },
  mutations: {
    GET_SEARCH_DATA(state, payload) {
      const data = JSON.parse(localStorage.getItem('searchList')) || ''
      if (data) {
        state.searchData = data
      } else {
        localStorage.setItem('searchList', JSON.stringify({
          channelPayCard: 100, // 渠道购卡量
          channelComplaint: '', // 渠道投诉量
          mediaPayCard: 100, // 媒体购卡量
          mediaComplaint: '', // 媒体投诉量
          advertPayCard: 100, // 广告购卡量
          advertComplaint: '', // 广告投诉量
          chartPayCard: 100, // 圆环购卡量
          chartComplaint: ''// 圆环投诉量
        }))
        state.searchData = JSON.parse(localStorage.getItem('searchList')) || {}
      }
    },
    SET_SEARCH_DATA(state, payload) {
      state.searchData = { ...state.searchData, ...payload }
      localStorage.setItem('searchList', JSON.stringify(state.searchData))
    }
  }
}
