<template>
  <div>
    <SForm
      ref="formNode"
      :results="data"
      :label-width="labelWidth"
      :form-item-list="formItemListCopy"
      :display-state="displayState"
      @formMount="getById"
      @changeForm="changeForm"
    >
      <!--<div v-for="(i,index) in slotList" :slot="i" :key="index">
            <slot :name="i"></slot>
        </div>-->
    </SForm>
  </div>
</template>

<script>
import SForm from '../../index'
import { Message } from 'element-ui'
import { error, submission, updateIgnoreKey } from '@/config/sysConfig'
import dialogFormMixins from '../dialog/mixins/index'
import { copy } from '@/config/basicsMethods'

export default {
  name: 'Index',
  components: {
    SForm
  },
  mixins: [dialogFormMixins],
  data() {
    return {
      loading: false,
      dialogVisible: this.dialogFormVisible,
      formItemListCopy: copy(this.formItemList),
      slotList: [],
      syncData: {}
    }
  },
  computed: {
    getSubHttp: function() {
      if (this.basics.isNull(this.urls.insertHttp) && this.basics.isNull(this.urls.updateHttp)) return () => Promise.resolve()
      return this.$store.state.submission.submitType === submission.insert ? this.urls.insertHttp : this.urls.updateHttp
    }
  },
  watch: {
    data(to) {
      this.$nextTick(() => {
        this.getById()
      })
    },
    'formItemList': {
      handler(to) {
        this.formItemListCopy = copy(to)
      },
      deep: true
    }
  },
  methods: {
    changeForm(data) {
      this.syncData = data
    },
    getSyncData() {
      return this.syncData
    },
    submit() {
      return new Promise((res) => {
        this.$refs.formNode.submitForm().then(data => {
          this.loading = true
          let getData = { ...{}, ...this.data, ...data }
          updateIgnoreKey.forEach(item => {
            delete getData[item]
          })
          getData = { ...getData, ...this.beforeUpdate(copy(getData)) }
          res(getData)
        }, msg => {
          Message({
            message: '格式填写错误',
            type: 'error'
          })
        }).catch(msg => {
          error(msg)
        })
      })
    },
    /*
      * 获取詳情
      * */
    getById() {
      if (this.$store.state.submission.submitType !== submission.update) {
        return false
      }
      const http = this.data
      if (this.basics.isFunction(this.urls.getByIdHttp)) {
        this.urls.getByIdHttp().then(msg => {
          this.getByIdData(msg)
        })
      } else if (this.basics.isObj(http) && !this.basics.isObjNull(http)) {
        this.getByIdData(http)
      } else {
        if (this.basics.isFunction(http)) {
          http().then(msg => {
            this.getByIdData(msg)
          })
        }
      }
    },
    /*
      * 获取详情遍历
      * */
    getByIdData(setData) {
      this.formItemListCopy.forEach(item => {
        if (item.slot) {
          this.slotList.push(item.slot)
        }
        if (item.childKey && this.basics.isArray(item.childKey)) {
          item.childKey.map((childKeyItem, index) => {
            for (const i in setData) {
              if (childKeyItem === i) {
                item.val[index] = setData[i]
              }
            }
          })
        } else {
          for (const i in setData) {
            if (item.key === i) {
              item.val = setData[i]
            }
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
