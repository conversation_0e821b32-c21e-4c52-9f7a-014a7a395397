<template>
  <div>
    <page :request="request" :list="list" table-title="机构策略">
      <template #searchContainer>
        <el-button v-permission="'create'" plain size="small" type="primary" @click="handleOpenForm(null)">创建策略</el-button>
      </template>
    </page>
    <InstitutionalStrategyForm v-model="institutionalStrategyFormModel" :form-id="institutionalStrategyId" @close="handleClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { ApiMechanismStrategyStatusList, OverallStrategyClueSourceList } from '../enum'
import InstitutionalStrategyForm from '../components/InstitutionalStrategyForm.vue'
import {
  getMechanismStrategyListApi,
  changeMechanismStrategyStatusApi,
  changeMechanismStrategyDefaultApi,
  deleteMechanismStrategyApi,
  getMechanismStrategyDefaultApi
} from '@/api/distribution'
import { hasPermission } from '@/utils/menuCodes'

export default {
  name: 'institutionalStrategy',
  components: { page, InstitutionalStrategyForm },
  data() {
    return {
      institutionalStrategyId: null,
      institutionalStrategyFormModel: false,
      listQuery: {},
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getMechanismStrategyListApi({ ...this.listQuery })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '策略名称',
          key: 'strategyName',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '请输入策略名称'
          }
        },
        {
          key: 'clueType',
          title: '数据来源',
          search: true,
          tableHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: OverallStrategyClueSourceList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            placeholder: '请选择'
          }
        },
        {
          title: '创建时间',
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['createTimeStart', 'createTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '更新时间',
          key: 'updateDate',
          type: formItemType.rangeDatePicker,
          childKey: ['updateTimeStart', 'updateTimeEnd'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: ApiMechanismStrategyStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '序号',
          render: (_h, params) => {
            return <div>{params.data.$index + 1}</div>
          },
          width: 80
        },
        {
          key: 'clueType',
          title: '数据来源',
          tableView: tableItemType.tableView.text,
          list: OverallStrategyClueSourceList,
          width: 100,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '策略名称',
          key: 'strategyName',
          width: 150
        },
        {
          title: '策略描述',
          key: 'description'
        },
        {
          key: 'defaultType',
          title: '是否默认策略',
          render: (h, params) => {
            return (
              <el-switch
                v-model={params.data.row.defaultType}
                active-value={1}
                disabled={!params.data.row.status}
                inactive-value={0}
                onChange={(e) => this.handleDefaultChange(e, params.data.row)}
              />
            )
          },
          width: 120
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: ApiMechanismStrategyStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 80
        },
        {
          title: '创建人',
          key: 'createByName',
          render: (_h, params) => {
            return <div style='text-align: center;'>{params.data.row.createByName} {params.data.row.createByMobile}</div>
          }
        },
        {
          title: '更新人',
          key: 'updateByName',
          render: (_h, params) => {
            return <div style='text-align: center;'>{params.data.row.updateByName} {params.data.row.updateByMobile}</div>
          }
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 120
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 120
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 220,
          fixed: 'right',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'edit' }),
              click: (_$index, _item, params) => {
                this.handleOpenForm(params.id)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'status' }),
              render: (_h, params) => {
                return <el-button type='primary' size='mini' plain disabled={params.data.defaultType === 1} onClick={() => this.changeStrategyStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            },
            {
              text: '移除',
              key: 'detail',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'remove' }),
              render: (_h, params) => {
                return <el-button type='primary' size='mini' plain disabled={params.data.defaultType === 1} onClick={() => this.handleDeleteStrategy(params.data)}>移除</el-button>
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleDeleteStrategy(row) {
      this.$confirm('是否删除此策略？', '操作提示', {
        type: 'warning'
      }).then(async() => {
        try {
          const { code } = await deleteMechanismStrategyApi(row.id)
          if (code === 200) {
            this.$message.success('移除成功')
            this.$store.dispatch('tableRefresh', this)
          }
        } catch (err) {
          console.log(err)
        }
      })
    },
    async handleDefaultChange(e, row) {
      const revert = () => (row.defaultType ^= 1)
      const request = async() => {
        try {
          const { code } = await changeMechanismStrategyDefaultApi({ id: row.id, status: row.defaultType })
          code === 200 ? (this.$message.success('操作成功'), this.$store.dispatch('tableRefresh', this)) : revert()
        } catch { revert() }
      }

      const confirm = msg =>
        // eslint-disable-next-line no-sequences
        this.$confirm(msg, '提示', { closeOnClickModal: false }).then(request).catch(() => (this.$message('取消操作'), revert()))

      if (e === 1) {
        try {
          const { code, data } = await getMechanismStrategyDefaultApi(row.id)
          return code === 200 && data.haveDefault
            ? confirm(`是否将${row.strategyName}改为默认策略？`)
            : request()
        } catch { revert() }
      } else {
        return confirm('确认关闭默认策略吗？')
      }
    },
    handleOpenForm(id) {
      this.institutionalStrategyId = id
      this.institutionalStrategyFormModel = true
    },
    changeStrategyStatus(row) {
      this.$confirm(`确认${row.status === 1 ? '禁用' : '启用'}此策略？`, '', {
        type: 'warning'
      }).then(async() => {
        try {
          await changeMechanismStrategyStatusApi({
            id: row.id,
            status: row.status === 1 ? 0 : 1
          })

          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    },
    handleClose() {
      this.institutionalStrategyId = null
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>
