<template>
  <div>
    <el-tabs v-model="tabActiveName" type="card" @tab-click="handleClick">
      <el-tab-pane
        v-for="(i,index) in list"
        :key="index"
        :label="options.value==='item'?String(i):String(i[options.value])"
        :name="getValue(i,index)"
      />
    </el-tabs>
    <div
      v-for="(i,index) in list"
      v-if="tabActiveName===getValue(i,index)"
      :key="index+'item'"
      class="template"
    >
      <slot v-if="tabType===tabTypeConfig.own" :name="index">
        {{ index }}
      </slot>
      <slot v-else-if="tabType===tabTypeConfig.common" :name="tabTypeConfig.common">
        {{ index+tabTypeConfig.common }}
      </slot>
    </div>
  </div>
</template>

<script>
import { tabType } from '@/config/sysConfig'

export default {
  name: 'Index',
  props: {
    tabsList: { /* 循环列表 如果item===string tabs name为index 如果item===Object tabs name为Object.value*/
      type: Array,
      default: () => []
    },
    tabType: {
      type: String,
      default: tabType.own
    },
    activeName: { /* 默认选中参数*/
      type: String,
      default: ''
    },
    request: {
      type: Function
    },
    options: {
      type: Object,
      default: () => ({
        key: 'index',
        value: 'item'
      })
    }
  },
  data() {
    return {
      tabActiveName: this.basics.isNull(this.activeName) ? this.getValue(this.tabsList[0], 0) : this.activeName,
      list: this.tabsList,
      tabTypeConfig: tabType
    }
  },
  watch: {
    tabActiveName(to) {
      this.$emit('update:activeName', to)
    }
  },
  mounted() {
    if (!this.basics.isNull(this.request)) {
      this.request().then(mgs => {
        this.list = mgs
        this.tabActiveName = this.getValue(this.list[0], 0)
      })
    }
  },
  methods: {
    getValue(item, index) {
      if (this.basics.isNull(item)) return
      return this.options.value === 'item' ? String(index) : String(item[this.options.key])
    },
    handleClick() {
      this.$emit('tabClick', this.tabActiveName)
    }
  }
}
</script>

<style scoped>
  .template {
    animation-duration: 0.3s;
    min-height: 700px;
  }
</style>
