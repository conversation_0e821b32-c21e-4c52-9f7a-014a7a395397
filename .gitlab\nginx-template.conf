server {
        listen       80;
        server_name  localhost;


        location / {
            root   /data/project/${SERVER_NAME};
            index  index.html index.htm;
            add_header Cache-Control 'no-cache';

            if ($request_method = 'OPTIONS') {
                        add_header Access-Control-Allow-Origin *;
                        add_header Access-Control-Allow-Methods 'GET, POST, PATCH, PUT, OPTIONS, DELETE';
                        add_header Access-Control-Expose-Headers 'Authorization,authenticated,ticket';
                        add_header Access-Control-Expose-Headers '*';
                        add_header Access-Control-Allow-Credentials true;
                        add_header Access-Control-Allow-Headers '*';
                        return 200;
                }

            try_files $uri $uri/ /index.html;

        }



        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

