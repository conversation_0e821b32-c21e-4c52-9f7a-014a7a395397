// 线索管理枚举

// 线索状态枚举
export const ClueStatusEnum = {
  // 可分发
  DISPATCHABLE: 1,
  // 待分发
  WAIT_DISPATCH: 2,
  // 已分发
  DISPATCHED: 3,
  // 分发失败
  FAIL_DISPATCH: 4,
  // 取消分发
  CANCEL_DISPATCH: 5
}
export const ClueStatusMap = {
  [ClueStatusEnum.DISPATCHABLE]: '可分发',
  [ClueStatusEnum.WAIT_DISPATCH]: '待分发',
  [ClueStatusEnum.DISPATCHED]: '已分发',
  [ClueStatusEnum.CANCEL_DISPATCH]: '取消分发',
  [ClueStatusEnum.FAIL_DISPATCH]: '分发失败'
}
export const ClueStatusList = Object.keys(ClueStatusEnum).map(key => ({
  id: ClueStatusEnum[key],
  name: ClueStatusMap[ClueStatusEnum[key]]
}))

// 车辆状态枚举
export const VehicleStatusEnum = {
  // 全款车
  FULL_PAYMENT: 0,
  // 抵押中
  MORTGAGE: 1
}
export const VehicleStatusMap = {
  [VehicleStatusEnum.FULL_PAYMENT]: '全款车',
  [VehicleStatusEnum.MORTGAGE]: '抵押中'
}
export const VehicleStatusList = Object.keys(VehicleStatusEnum).map(key => ({
  id: VehicleStatusEnum[key],
  name: VehicleStatusMap[VehicleStatusEnum[key]]
}))

// 授权状态枚举
export const AuthorizationStatusEnum = {
  // 未授权
  WAIT_AUTHORIZATION: 0,
  // 已授权
  AUTHORIZED: 1
}
export const AuthorizationStatusMap = {
  [AuthorizationStatusEnum.WAIT_AUTHORIZATION]: '未授权',
  [AuthorizationStatusEnum.AUTHORIZED]: '已授权'
}
export const AuthorizationStatusList = Object.keys(AuthorizationStatusEnum).map(key => ({
  id: AuthorizationStatusEnum[key],
  name: AuthorizationStatusMap[AuthorizationStatusEnum[key]]
}))

// 撞库状态枚举
export const CollisionStatusEnum = {
  // 撞库成功
  SUCCESS: 1,
  // 撞库失败
  FAIL: 2
}
export const CollisionStatusMap = {
  [CollisionStatusEnum.SUCCESS]: '撞库成功',
  [CollisionStatusEnum.FAIL]: '撞库失败'
}
export const CollisionStatusList = Object.keys(CollisionStatusEnum).map(key => ({
  id: CollisionStatusEnum[key],
  name: CollisionStatusMap[CollisionStatusEnum[key]]
}))

// 撞库类型枚举
export const CollisionTypeEnum = {
  // 用户撞库
  USER: 1,
  // 机构撞库
  ORGANIZATION: 2
}
export const CollisionTypeMap = {
  [CollisionTypeEnum.USER]: '用户撞库',
  [CollisionTypeEnum.ORGANIZATION]: '机构撞库'
}
export const CollisionTypeList = Object.keys(CollisionTypeEnum).map(key => ({
  id: CollisionTypeEnum[key],
  name: CollisionTypeMap[CollisionTypeEnum[key]]
}))
