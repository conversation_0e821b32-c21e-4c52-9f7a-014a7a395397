/*
 * @Author: 蒋雪 <EMAIL>
 * @Date: 2022-07-28 16:51:14
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-07-19 17:42:03
 * @FilePath: \qtsh-sale-boss-frontend\src\config\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import CONSTANT from '@/config/constant.conf'
import '@/scss/reset.scss'
import '@/scss/common.scss'
import '@/scss/resetElement.scss'
import utils from '@/libs/utils'
import {
  formatDate,
  substr,
  strDefault,
  parseTimeTwo,
  parseAmount,
  parseTAmount,
  subscriptionPeriodsStr
} from '@/libs/filter'
import 'viewerjs/dist/viewer.css'
import Viewer from 'v-viewer'
Vue.use(Viewer)
Viewer.setDefaults({
  // inline: true,
  // button: true,
  // navbar: true,
  // title: true,
  // toolbar: true,
  // tooltip: true,
  // movable: true,
  // zoomable: true,
  // rotatable: true,
  // scalable: true,
  // transition: true,
  // fullscreen: true,
  // keyboard: true,
  // url: 'data-source',
  // zIndexInline: 3000,
  zIndex: 9999
})

/*
 * 打印当前版本号
 * */
utils.beautifulConsoleVersion()

/*
 * vue挂载对象
 * */
Vue.prototype.$utils = utils
Vue.prototype.$CONSTANT = CONSTANT

/*
 * filter
 * */
Vue.filter('subStr', (text, num, end_num) => {
  return substr(text, num, end_num)
})
Vue.filter('formatDate', (val, fmt) => {
  return formatDate(val, fmt)
})
Vue.filter('strDefault', (val, placeholder) => {
  return strDefault(val, placeholder)
})
Vue.filter('parseTimeTwo', (time, fmt) => {
  return parseTimeTwo(time, fmt)
})
Vue.filter('parseAmount', amount => {
  return parseAmount(amount)
})
Vue.filter('parseTAmount', amount => {
  return parseTAmount(amount)
})
Vue.filter('subscriptionPeriodsStr', value => {
  return subscriptionPeriodsStr(value)
})
/**
 * mixin
 */
