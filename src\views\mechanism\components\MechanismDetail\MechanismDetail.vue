<!--机构详情-->
<template>
  <Drawer
    :visible.sync="drawer"
    size="60%"
    @opened="handleTabsChange"
    @close="handleDrawerClose"
  >
<!--    <i class="el-icon-close close" @click="$emit('change', false)" />-->
    <div v-if="drawer" class="mechanism-detail">
      <el-tabs v-model="activeName" @tab-click="handleTabsChange">
        <el-tab-pane label="机构基本信息" name="baseInfo">
          <BaseInfo :mechanism-info="mechanismInfo" />
        </el-tab-pane>
        <el-tab-pane label="授权账号" name="account">
          <Account ref="accountRef" :agencies-id="id" />
        </el-tab-pane>
        <el-tab-pane label="财务信息" name="finance">
          <Finance :id="accountId" ref="financeRef" />
        </el-tab-pane>
        <el-tab-pane label="交易记录" name="trade">
          <Trade :id="accountId" ref="tradeRef" />
        </el-tab-pane>
        <el-tab-pane label="相关附件" name="appendix">
          <Appendix :mechanism-info="mechanismInfo" />
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="operate">
          <Operate :operate-record-list="operateRecordList" :loading="operateLoading" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </Drawer>
</template>

<script>
import Drawer from '@/components/Drawer/FormSection2.vue'
import BaseInfo from './BaseInfo.vue'
import Account from './Account.vue'
import Finance from './Finance.vue'
import Trade from './Trade.vue'
import Appendix from './Appendix.vue'
import Operate from './Operate.vue'
import { getMechanismInfoApi, getOperateRecordApi } from '@/api/mechanism'

export default {
  name: 'MechanismDetail',
  components: {
    BaseInfo,
    Account,
    Finance,
    Trade,
    Appendix,
    Operate,
    Drawer
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    id: {
      type: Number,
      default: null
    },
    accountId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      activeName: 'baseInfo',
      // 机构基本信息
      mechanismInfo: {},
      // 操作记录
      operateRecordList: [],
      // 操作记录loading
      operateLoading: false
    }
  },
  computed: {
    drawer: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    handleDrawerClose() {
      this.resetData()
      this.$emit('change', false)
    },
    // 重置数据
    resetData() {
      this.mechanismInfo = {}
    },
    // 根据tabs获取对应接口
    async handleTabsChange() {
      try {
        this.resetData()
        // 交易记录、授权账号、财务信息数据组件内单独获取
        if (['account', 'trade', 'finance'].includes(this.activeName)) {
          // 交易记录
          if (this.activeName === 'trade') {
            this.$refs.tradeRef.refreshList()
          }
          // 财务信息
          if (this.activeName === 'finance') {
            this.$refs.financeRef.refreshList()
          }
          // 授权账号
          if (this.activeName === 'account') {
            this.$refs.accountRef.refreshList()
          }
        } else {
          const requestMap = {
            'baseInfo': this.getMechanismInfo,
            'appendix': this.getMechanismInfo,
            'operate': this.getOperateRecord
          }

          await requestMap[this.activeName]()
        }
      } catch (err) {
        console.log(err)
      }
    },
    // 获取操作记录
    async getOperateRecord() {
      // 触发loading
      this.operateLoading = true
      this.operateRecordList = []
      try {
        const { data } = await getOperateRecordApi({
          agencyId: this.id,
          packageName: 'com.base.server.cms.controller.agencies'
        })

        this.operateRecordList = data
      } catch (err) {
        console.log(err)
      } finally {
        this.operateLoading = false
      }
    },
    // 获取机构基本信息
    async getMechanismInfo() {
      try {
        const { data } = await getMechanismInfoApi(this.id, 1)
        this.mechanismInfo = data
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.close{
  position: absolute;
  right: 20px;
  top: 30px;
  font-size: 18px;
  cursor: pointer;
  z-index: 99;
}
.mechanism-detail{
  padding: 20px;
  ::v-deep .el-tabs__item:focus {
    outline: none !important;
    box-shadow: none !important;
  }
  ::v-deep .el-tabs__content{
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
}
</style>
