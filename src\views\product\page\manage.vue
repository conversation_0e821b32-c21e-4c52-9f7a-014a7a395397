<template>
  <div>
    <page :request="request" :list="list" table-title="产品管理">
      <template #searchContainer>
        <el-button v-permission="'create'" plain size="small" type="primary" @click="handleOpenForm(null)">新增产品</el-button>
      </template>
    </page>
    <ProductForm v-model="productFormModel" :form-id="productId" @close="handleClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { YesNoList } from '@/enum'
import { ProductStatusList } from '../enum'
import ProductForm from '../components/ProductForm.vue'
import { getProductListApi, changeProductStatusApi, editProductApi } from '@/api/product'
import moment from 'moment/moment'
import _ from 'lodash'
import { hasPermission } from '@/utils/menuCodes'

export default {
  name: 'list',
  components: { page, ProductForm },
  data() {
    return {
      productId: null,
      productFormModel: false,
      listQuery: {
        // 产品名称
        productName: '',
        // 状态
        status: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getProductListApi({ ...this.listQuery })
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '产品名称',
          key: 'productName',
          search: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '请输入产品名称'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: ProductStatusList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '序号',
          render: (_h, params) => {
            return <div>{params.data.$index + 1}</div>
          },
          width: 80
        },
        {
          title: '产品名称',
          key: 'productName'
        },
        {
          title: '产品描述',
          key: 'productDescribe'
        },
        {
          title: '产品报价(元)',
          key: 'productPrice',
          width: 100
        },
        {
          title: '抵押车是否在抵',
          key: 'carLoanStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 120
        },
        {
          title: '二要素校验是否通过',
          key: 'twoElementsStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 130
        },
        {
          title: '三要素校验是否通过',
          key: 'threeElementsStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 130
        },
        {
          title: '人车合一校验是否通过',
          key: 'carPersonStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 150
        },
        {
          title: '是否有车牌',
          key: 'carNumberStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 120
        },
        {
          title: '车牌有效校验是否通过',
          key: 'carNumberValidityStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 150
        },
        {
          title: '是否有车300报告',
          key: 'carReportStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: YesNoList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          width: 120
        },
        {
          title: '排序',
          key: 'sort',
          width: 100,
          render: (_h, params) => {
            return <el-input v-model={params.data.row.sort} size='mini' onInput={() => this.handleSortChange(params.data.row)}></el-input>
          }
        },
        {
          title: '创建人',
          key: 'createByName',
          width: 150,
          render: (_h, params) => {
            return <div>{params.data.row.createByName} {params.data.row.createMobile}</div>
          }
        },
        {
          title: '最近修改人',
          key: 'updateByName',
          width: 150,
          render: (_h, params) => {
            return <div>{params.data.row.updateByName} {params.data.row.updateMobile}</div>
          }
        },
        {
          title: '创建时间',
          key: 'createTime',
          width: 120,
          render: (_h, params) => {
            return <span>{moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm')}</span>
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          width: 120,
          render: (_h, params) => {
            return <span>{moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm')}</span>
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: ProductStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 150,
          fixed: 'right',
          activeType: [
            {
              text: '编辑',
              key: 'detail',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'edit' }),
              click: (_$index, _item, params) => {
                this.handleOpenForm(params.id)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: 'status' }),
              render: (_h, params) => {
                return <el-button type='primary' size='mini' plain onClick={() => this.changeProductStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleOpenForm(id) {
      this.productId = id
      this.productFormModel = true
    },
    handleClose() {
      this.productId = null
      this.$store.dispatch('tableRefresh', this)
    },
    changeProductStatus(row) {
      this.$confirm(`确认${row.status === 1 ? '禁用' : '启用'}此产品？`, '', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await changeProductStatusApi(row.id, row.status === 1 ? 0 : 1)

          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    },
    handleSortChange: _.debounce(async function(params) {
      try {
        await editProductApi({
          id: params.id,
          sort: Number(params.sort)
        })
      } catch (err) {
        console.log(err)
      }
    }, 500)
  }
}
</script>
