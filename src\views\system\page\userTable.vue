<template>
  <div class="account-management">
    <Page :request="request" :list="list" table-title="账户管理">
      <div slot="searchContainer" style="display: inline-block">
        <el-button v-permission="'addUser'" type="primary" size="small" plain icon="el-icon-circle-plus-outline" @click="linkDetails('add')">
          添加用户
        </el-button>
      </div>
    </Page>
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="{width:dialogOps.width,title:`${dialogOps.title}用户`}" :close-on-click-modal="false">
      <editAccount v-if="dialogFormVisible" :form-data="ruleForm" :type="dialogOps.title==='修改'?'edit':'add'" :roles-list="rolesList" :btn-disabled="btn_disabled" @submit="submit" @close="dialogFormVisible=false" />
    </SDialog>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { hasPermission } from '@/utils/menuCodes'
import editAccount from '../modules/editAccount'
import { getRoleLists } from './useUsers'
import SDialog from '@/components/restructure/dialog'
import { updateUserStatus } from '@/api/system'
import {
  get_admin_list,
  add_admin,
  put_admin
} from '@/api/system'
export default {
  components: {
    Page,
    editAccount,
    SDialog
  },
  props: {
    curDepartmentId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      btn_disabled: false,
      listQuery: {
        account: '',
        roleId: '',
        status: ''
      },
      dialogOps: {
        width: '480px',
        title: ''
      },
      ruleForm: {

      },
      request: {
        getListUrl: async data => {
          if (!this.curDepartmentId) {
            return {
              data: {
                total: 0,
                rows: []
              }
            }
          }
          this.listQuery = { ...this.listQuery, ...data, status: data.status === '-999' ? '' : data.status, ...(this.curDepartmentId ? { branchId: this.curDepartmentId } : {}) }
          const res = await get_admin_list(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      },
      rolesList: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '用户ID',
          key: 'id'
        },
        {
          title: '用户信息',
          options: {
            placeholder: '请输入手机号/姓名'
          },
          key: 'account',
          type: formItemType.input,
          tableView: tableItemType.tableView.text,
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '用户名称',
          key: 'username'
        },
        {
          title: '手机号码',
          key: 'mobileNo'
        },
        {
          title: '角色',
          key: 'roleId',
          searchKey: 'roleId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.rolesList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.role,
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '角色',
          key: 'roleName'
        },
        {
          title: '直属上级',
          key: 'directSuperiorStr'
        },
        {
          title: '状态',
          key: 'status',
          searchKey: 'status',
          type: formItemType.select,
          search: true,
          list: [
            {
              label: '不限',
              value: -999
            },
            {
              label: '启用',
              value: 0
            },
            {
              label: '禁用',
              value: 1
            }
          ],
          tableHidden: true

        },
        {
          title: '状态',
          key: 'locked',
          render: (_h, params) => {
            const status = params?.data?.row?.locked
            return <div>{status === 1 ? '禁用' : status === 0 ? '启用' : '-'}</div>
          }
        },
        {
          title: '创建人',
          key: 'createByStr'
        },
        {
          title: '创建时间',
          key: 'createTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '操作',
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 180,
          fixed: 'right',
          activeType: [
            {
              key: 'accountEditUser',
              name: '编辑'
            },
            {
              key: 'accountEditStatus',
              name: '状态'
            }
          ].map(buttons => {
            const handleButton = () => {
              return {
                click: ($index, item, params) => {
                  const { key } = buttons
                  if (key === 'accountEditUser') {
                    this.linkDetails('edit', params)
                  } else if (key === 'accountEditStatus') {
                    this.changeStatus(params)
                  }
                }
              }
            }
            const renderButton = () => {
              return {
                render: (h, params) => {
                  const { key } = buttons
                  if (key === 'accountEditStatus') {
                    const status = params.data.locked
                    return <el-button size='mini' type='primary' plain onClick={() => { this.changeStatus(params.data) }}>{status === 0 ? '禁用' : status === 1 ? '启用' : '启用/禁用'}</el-button>
                  }
                }
              }
            }
            return {
              text: buttons.name,
              key: buttons.key,
              type: tableItemType.activeType.event,
              hidden: !hasPermission({ buttons: buttons.key }),
              ...(buttons.key === 'accountEditUser' ? handleButton() : renderButton())
            }
          })
        }
      ]
    }

  },
  watch: {
    curDepartmentId: {
      handler(val) {
        if (val) {
          this.$store.dispatch('tableRefresh', this)
        }
      }
    }
  },
  created() {
    this.getRoleList()
  },
  methods: {
    linkDetails(type, item) {
      this.dialogFormVisible = true
      this.dialogOps.title = type === 'add' ? '新增' : '修改'
      if (type === 'edit') {
      // 获取详情
        this.ruleForm = { ...item }
      } else {
        this.ruleForm = { branchId: this.curDepartmentId || '' }
      }
    },

    /**
       * 获取角色
       */
    async getRoleList() {
      this.rolesList = await getRoleLists()
    },
    refreshUserData() {
      this.$store.dispatch('tableRefresh', this)
      this.$emit('refreshUserData')
    },
    // 【用户提交】 新增 | 修改
    submit(data) {
      this.btn_disabled = true
      let api
      this.dialogOps.title === '新增' ? api = add_admin : api = put_admin
      api(data).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: this.dialogOps.title + '成功'
          })
          this.dialogFormVisible = false
          this.refreshUserData()
        }
      }).finally(() => {
        this.btn_disabled = false
      })
    },
    changeStatus(data) {
      const { locked } = data
      this.$confirm(`确定要${locked === 0 ? '禁用' : '启用'}此账号<br>用户编号${data.id}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        updateUserStatus(data.id).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.refreshUserData()
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.account-management{
    .title-container-box{
        justify-content: flex-start;
        width: 100%;
        border: none;
        padding: 0 0;
      }
      .title-table-list{
        display: flex;
        border: 1px solid #dfdfdf;
      }
      .title-item{
        flex-shrink: 0;
        height: 40px;
        line-height: 40px;
        padding: 0 15px;
        border-right:1px solid #dfdfdf; ;
      }
      .pd-10px{
        padding: 10px ;
      }
}
</style>
