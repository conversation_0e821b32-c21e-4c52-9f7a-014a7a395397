<template>
  <div v-loading="loading">
    <el-timeline v-if="operateRecordList.length" style="margin-top: 20px;padding-left: 10px">
      <el-timeline-item
        v-for="(activity, index) in operateRecordList"
        :key="index"
        :timestamp="handleTimePickerHandle(activity.createTime)"
        placement="top"
        type="primary"
      >
        {{ activity.operation }}：操作人：[{{ activity.userNane }}]
      </el-timeline-item>
    </el-timeline>
    <Empty v-else description="暂无操作记录" />
  </div>
</template>

<script>
import moment from 'moment'
import Empty from '@/components/Empty'

export default {
  name: 'Operate',
  components: {
    Empty
  },
  props: {
    operateRecordList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 处理时间戳格式化
    handleTimePickerHandle(e) {
      return moment(e).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>

<style scoped lang="scss">

</style>
