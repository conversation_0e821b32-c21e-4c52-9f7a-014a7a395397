import { get, post, put } from '@/libs/axios.package'
import qs from 'qs'
import CONSTANT from '@/config/constant.conf'

// 获取机构列表
export const getMechanismListApi = obj => {
  return get('/agencies/page', obj)
}
// 创建机构
export const createMechanismApi = obj => {
  return post('/agencies/save', obj)
}
// 编辑机构
export const editMechanismApi = obj => {
  return post('/agencies/update', obj)
}
// 改变机构状态
export const changeMechanismStatusApi = obj => {
  return post('/agencies/enableOrDisable', obj)
}
// 获取机构基本信息
// type 类型：1-机构详情，敏感信息脱敏 2-资质管理详情回显，敏感信息不脱敏
export const getMechanismInfoApi = (id, type) => {
  return get(`/agencies/getInfo?id=${id}&type=${type}`)
}
// 获取api机构list
export const getApiMechanismListApi = obj => {
  return get('/agencies/api/page', obj)
}
// 获取产品配置list
export const getProductConfigListApi = obj => {
  return get('/api/agencies-product/page', obj)
}
// 新增报价
export const createQuoteApi = obj => {
  return post('/api/agencies-product', obj)
}
// 编辑报价
export const editQuoteApi = obj => {
  return put('/api/agencies-product', obj)
}
// 查询报价详情
export const getQuoteInfoApi = id => {
  return get(`/api/agencies-product/${id}`)
}
// 改变报价状态
export const changeQuoteStatusApi = (id, status) => {
  return put(`/api/agencies-product/${id}/${status}`)
}
// 获取交易记录
export const getTransactionRecordApi = obj => {
  return get('/api/agencies-account-detail/page', obj)
}
// 交易记录导出
export const exportTransactionRecordApi = obj => `${CONSTANT.publicPath}/api/agencies-account-detail/export?${qs.stringify(obj)}`
// 获取账户
export const getAccountApi = obj => {
  return get('/api/agencies-account/page', obj)
}
// 获取sass账号列表
export const getSassAccountListApi = obj => {
  return get('/agencies/sassAccount/page', obj)
}
// 获取sass角色列表
export const getSassRoleListApi = obj => {
  return get('/agencies/sassRole/list', obj)
}
// 新增sass账号
export const createSassAccountApi = obj => {
  return post('/agencies/sassAccount/save', obj)
}
// 编辑sass账号
export const editSassAccountApi = obj => {
  return post('/agencies/sassAccount/update', obj)
}
// 获取sass用户信息
export const getSassUserInfoApi = obj => {
  return get('/agencies/sassAccount/getInfo', obj)
}
// 改变sass账户状态
export const changeSassAccountStatusApi = obj => {
  return post('/agencies/sassAccount/enableOrDisable', obj)
}
// 重置sass账户密码
export const resetSassAccountPwdApi = obj => {
  return post('/agencies/sassAccount/resetPassword', obj)
}
// 获取操作记录
export const getOperateRecordApi = obj => {
  return get('/syslog/page/agency', obj)
}
