<template>
  <el-dialog title="自定义字段" :visible.sync="showDialog">
    <div v-if="showExpandButton && showExpandButton.length > 0" class="group-btns">
      <button
        v-for="(item, index) in showExpandButton"
        :key="index"
        :class="[item, 'btn']"
        @click="handleCheckBox({ buttonType: item })"
      >
        <template v-if="item === 'allChecked'">全选</template>
        <template v-if="item === 'allNoChecked'">全不选</template>
        <template v-if="item === 'defaultKeyChecked'">默认</template>
      </button>
    </div>
    <template v-for="(item, index) in treeData">
      <div v-if="item.isCustom" :key="index" :class="['list-item', !item.children && 'list-item--inline']">
        <div v-if="item.type!='active'" style="width: 100%;">
          <template v-if="item.children">
            <span style="margin-right: 10px;">{{ item.title }}</span>
            <el-checkbox
              v-model="item.checked"
              :class="['left-item', 'my-checkbox']"
              :indeterminate="item.isIndeterminate"
              @change="handleCheckAllChange($event, index)"
            >
              全选
            </el-checkbox>
          </template>
          <el-checkbox
            v-else
            v-model="item.checked"
            :class="['left-item', 'my-checkbox']"
            :indeterminate="item.isIndeterminate"
            @change="handleCheckAllChange($event, index)"
          >
            <span style="margin-right: 10px;">{{ item.title }}</span>
          </el-checkbox>
        </div>
        <div>
          <template v-if="item.children">
            <el-checkbox-group
              v-model="item.childChecked"
              class="right-item"
              @change="handleCheckedCitiesChange($event, index)"
            >
              <template v-for="it in item.children">
                <el-checkbox v-if="it.type!='active'" :key="it.key" class="my-checkbox" :label="it.key">{{
                  it.title
                }}</el-checkbox>
              </template>
            </el-checkbox-group>
          </template>
        </div>
      </div>
    </template>
    <el-row type="flex" justify="end" style="padding-bottom: 10px">
      <el-button size="small" type="warning" @click="reset">重置</el-button>
      <el-button size="small" type="primary" @click="submit">保存</el-button>
    </el-row>
  </el-dialog>
</template>
<script>
/**
 * {
      isCustom: true,//在弹窗展示
      title: '广告侧',
      tableHidden: false,
      key: 'ggc',
      children: [
        {
          key: 'deu',
          title: 'DEU（新/总）',
          defaultShowKey: true,//默认勾选
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, `观看广告设备数；当日激活设备记为新，总=新+老`)
          }
        }
      ]
  }
  renderHeaders(h, column, text) {
      return (
        <div>
          <span>{column.label}</span>
          <el-tooltip content={text}>
            <i class='el-icon-question' style='color:#409eff;margin-left:5px;font-size: 16px;'></i>
          </el-tooltip>
        </div>
      )
  }
 *
*/
import { Loading } from 'element-ui'
export default {
  name: 'CheckboxPage',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      default: false
    },
    showExpandButton: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeData: [],
      allKeys: [],
      allNoKeys: [],
      defaultKeys: [],
      showDialog: false
    }
  },
  watch: {
    show: {
      handler(newVal) {
        this.showDialog = newVal
      },
      deep: true,
      immediate: true
    },
    showDialog(value) {
      this.$emit('changeShow', value)
    },
    defaultCheckedKeys: {
      handler(newVal) {
        const list = this.data
        const defaultCheckedKeys = newVal
        this.treeData = this.listAddChildChecked(list, defaultCheckedKeys)
        this.allNoKeys = this.getNoCustomAllKeys()
        this.allKeys = this.getCustomAllKeys()
        this.defaultKeys = this.getDefaultKeys()
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    handleCheckAllChange(val, index) {
      const item = this.treeData[index]
      const allKeys = item.children?.map(e => e.key) || []
      this.treeData.splice(index, 1, {
        ...item,
        ...(item.children && { childChecked: val ? allKeys : [] }),
        isIndeterminate: false
      })
    },
    handleCheckedCitiesChange(value, index) {
      const checkedCount = value.length
      const item = this.treeData[index]
      this.treeData.splice(index, 1, {
        ...item,
        ...{ checked: checkedCount !== 0 },
        isIndeterminate: checkedCount > 0 && checkedCount < item.children.length
      })
    },
    listAddChildChecked(list, defaultCheckedKeys = []) {
      return list.map(item => {
        if (item.children?.length) {
          const childKeys = item.children.map(e => e.key)
          const childChecked = childKeys.filter(e => defaultCheckedKeys.includes(e))
          const checkedCount = childChecked.length
          return {
            ...item,
            checked: defaultCheckedKeys.includes(item.key),
            childChecked,
            children: this.listAddChildChecked(item.children, defaultCheckedKeys),
            isIndeterminate: checkedCount > 0 && checkedCount < childKeys.length
          }
        }
        return { ...item, checked: defaultCheckedKeys.includes(item.key) }
      })
    },
    handleCheckBox({ buttonType = '' } = {}) {
      if (!buttonType) {
        return
      }
      const list = this.data
      let checkedKeys = []
      switch (buttonType) {
        case 'allChecked':
          checkedKeys = [...this.allNoKeys, ...this.allKeys]
          break
        case 'allNoChecked':
          checkedKeys = [...this.allNoKeys]
          break
        case 'defaultKeyChecked':
          checkedKeys = [...this.defaultKeys]
          break
      }
      this.treeData = this.listAddChildChecked(list, checkedKeys)
    },
    getCustomAllKeys() {
      return this.data.reduce((pre, cur) => {
        if (cur.key && cur.isCustom) {
          pre.push(cur.key)
        }
        if (cur.children && cur.isCustom) {
          const array = cur.children.map(item => item.key)
          pre.push(...array)
        }
        return pre
      }, [])
    },
    getNoCustomAllKeys() {
      return this.data.reduce((pre, cur) => {
        if (cur.key && !cur.isCustom && !cur.otherScreen) {
          pre.push(cur.key)
        }
        if (cur.children && !cur.isCustom) {
          const array = cur.children.map(item => item.key)
          pre.push(...array)
        }
        return pre
      }, [])
    },
    getDefaultKeys() {
      const list = this.data.reduce((pre, cur) => {
        if (!(cur.defaultShowKey === false)) {
          pre.push(cur.key)
        }
        if (cur.children) {
          const array = cur.children.reduce((arr, _c) => {
            if (!(_c.defaultShowKey === false)) {
              arr.push(_c.key)
            }
            return arr
          }, [])
          pre.push(...array)
        }
        return pre
      }, [])
      return list
    },
    reset() {
      const loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.8)', text: '加载中...', spinner: 'el-icon-loading' })
      this.$forceUpdate()
      setTimeout(() => {
        this.$nextTick(() => {
          this.$emit('reset', loadingInstance)
          this.showDialog = false
        })
      }, 100)
    },
    submit() {
      const loadingInstance = Loading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.8)', text: '加载中...', spinner: 'el-icon-loading' })
      this.$forceUpdate()
      setTimeout(() => {
        this.$nextTick(() => {
          const allKeys = []
          const list = this.treeData
          list.forEach(element => {
            if (element.checked) allKeys.push(element.key)
            if (element.children) allKeys.push(...element.childChecked)
          })
          this.$emit('change', allKeys, loadingInstance)
          this.showDialog = false
        })
      }, 100)
    }
  }
}
</script>
<style lang="scss">
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.list-item {
  display: flex;
  margin: 4px 0;
  padding: 0 8px;
  flex-wrap: wrap;
  .left-item {
    min-width: 120px;
  }
  .right-item {
    flex: 1;
  }
  .my-checkbox {
    margin: 6px 6px 6px 0 !important;
  }
}
.list-item--inline {
  display: inline-block;
  width: auto;
  min-width: unset;
}
.group-btns{
  display: flex;
  justify-content: flex-end;
  .btn{
    margin-left: 10px;
    height: 36px;
    line-height: 36px;
    padding: 0 10px;
    cursor: pointer;
    border-radius: 4px;
    color: #fff;
    background: #409EFF;
  }
}

</style>
