<template>
    <div class="card-checkbox-container">
      <div class="card-checkbox-section">
        <div class="card-checkbox-header">
          <el-checkbox 
            v-model="isAllChecked" 
            :indeterminate="isIndeterminate"
            :disabled="disabled"
            @change="handleCheckAllChange">
            {{ title }}
          </el-checkbox>
        </div>
        <div class="card-checkbox-content">
          <el-checkbox-group v-model="selectedItems" @change="handleCheckboxChange">
            <el-checkbox 
              v-for="item in options" 
              :key="item.value || item"
              :label="item.value || item"
              :disabled="disabled">
              {{ item.label || item }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'CardCheckboxGroup',
    props: {
      title: {
        type: String,
        required: true,
        default: '标题'
      },
      options: {
        type: Array,
        required: true,
        default: () => []
      },
      value: {
        type: Array,
        required: true,
        default: () => []
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isAllChecked: false,
        isIndeterminate: false,
        selectedItems: []
      }
    },
    watch: {
      value: {
        handler(val) {
          this.selectedItems = [...val]
          this.updateCheckAllStatus()
        },
        immediate: true
      }
    },
    methods: {
      // 处理全选变化
      handleCheckAllChange(val) {
        const allValues = this.options.map(item => item.value || item)
        this.selectedItems = val ? allValues : []
        this.isIndeterminate = false
        this.$emit('input', this.selectedItems)
        this.$emit('change', this.selectedItems)
      },
      // 处理选项变化
      handleCheckboxChange(val) {
        const allValues = this.options.map(item => item.value || item)
        this.isIndeterminate = val.length > 0 && val.length < allValues.length
        this.isAllChecked = val.length === allValues.length
        this.$emit('input', val)
        this.$emit('change', val)
      },
      // 更新全选状态
      updateCheckAllStatus() {
        const allValues = this.options.map(item => item.value || item)
        this.isAllChecked = this.selectedItems.length === allValues.length
        this.isIndeterminate = this.selectedItems.length > 0 && this.selectedItems.length < allValues.length
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .card-checkbox-container {
    width: 100%;
  }
  
  .card-checkbox-section {
    width: 100%;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 16px;
    box-sizing: border-box;
    background-color: #FFFFFF;
    margin-bottom: 0;
  
    .card-checkbox-header {
      margin-bottom: 16px;
      padding-left: 5px;
      
      ::v-deep .el-checkbox {
        .el-checkbox__input {
          margin-right: 8px;
        }
        
        .el-checkbox__label {
          font-weight: 500;
          font-size: 14px;
          color: #333;
        }
      }
    }
  
    .card-checkbox-content {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      padding-left: 0;
  
      ::v-deep .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        width: 100%;
      }
  
      ::v-deep .el-checkbox {
        margin-right: 0;
        margin-left: 0;
        height: 36px;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        margin-bottom: 0;
        padding: 0 16px 0 32px;
        box-sizing: border-box;
        position: relative;
        background-color: #FFFFFF;
        cursor: pointer;
        
        .el-checkbox__input {
          position: absolute;
          left: 10px;
          top: 50%;
          transform: translateY(-50%);
        }
  
        &.is-checked {
          background-color: #F5F7FA;
          border-color: #409EFF;
        }
  
        &:hover {
          border-color: #409EFF;
        }
  
        .el-checkbox__label {
          padding-left: 0;
          line-height: 36px;
          font-size: 13px;
          white-space: nowrap;
          color: #606266;
        }
      }
    }
  }
  </style> 