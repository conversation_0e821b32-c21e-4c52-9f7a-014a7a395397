<template>
  <div>
    <el-dialog
      append-to-body
      width="30%"
      :title="getTitle"
      :visible.sync="dialogVisible"
      :close-on-click-modal="closeOnClickModal"
    >
      <SForm ref="formNode" :results="data" :label-width="labelWidth" :form-item-list="formItemListCopy" @formMount="getById" />
      <div slot="footer" class="dialog-footer">
        <div v-if="dialogFooterState===dialogFooterStateConfig.common">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
        </div>
        <div v-if="dialogFooterState===dialogFooterStateConfig.close" style="text-align: center;">
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SForm from '../../index'
import { Message } from 'element-ui'
import { dialogFooterState, error, submission, updateIgnoreKey } from '@/config/sysConfig'
import dialogFormMixins from './mixins/index'
import { copy } from '@/config/basicsMethods'

export default {
  name: 'Index',
  components: {
    SForm
  },
  mixins: [dialogFormMixins],
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFooterStateConfig: dialogFooterState,
      loading: false,
      dialogVisible: this.dialogFormVisible,
      formItemListCopy: copy(this.formItemList),
      closeOnClickModal: false
    }
  },
  computed: {
    getSubHttp: function() {
      if (this.basics.isNull(this.urls.insertHttp) && this.basics.isNull(this.urls.updateHttp)) return () => Promise.resolve()
      return this.$store.state.submission.submitType === submission.insert ? this.urls.insertHttp : this.urls.updateHttp
    },
    getTitle: function() {
      return this.$store.state.submission.submitType === submission.insert ? this.title + '添加' : this.title + '详情'
    }
  },
  watch: {
    dialogVisible(to) {
      if (!to) {
        this.$emit('update:dialogFormVisible', to)
      }
    },
    dialogFormVisible(to) {
      this.dialogVisible = to
    },
    data(to) {
      this.getById()
    },
    formItemList(to) {
      this.formItemListCopy = copy(to)
    }
  },
  methods: {
    submit() {
      this.$refs.formNode.submitForm().then(data => {
        this.loading = true
        let getData = { ...{}, ...this.data, ...data }
        updateIgnoreKey.forEach(item => {
          delete getData[item]
        })
        getData = { ...getData, ...this.beforeUpdate(copy(getData)) }
        this.getSubHttp(getData).then(msg => {
          this.loading = false
          this.dialogVisible = false
          this.$emit('getSubSuccess', true)
        }).catch(msg => {
          this.loading = false
        })
      }, msg => {
        Message({
          message: '格式填写错误',
          type: 'error'
        })
      }).catch(msg => {
        error(msg)
      })
    },
    /*
      * 获取詳情
      * */
    getById() {
      if (this.$store.state.submission.submitType !== submission.update) {
        return false
      }
      const http = this.data
      if (this.basics.isObj(http)) {
        this.getByIdData(http)
      } else {
        if (this.basics.isFunction(http)) {
          http().then(msg => {
            this.getByIdData(msg)
          })
        }
      }
    },
    /*
      * 获取详情遍历
      * */
    getByIdData(setData) {
      this.formItemListCopy = copy(this.formItemList)
      this.formItemListCopy.forEach(item => {
        if (item.childKey && this.basics.isArray(item.childKey)) {
          item.childKey.map((childKeyItem, index) => {
            item.val[index] = this.basics.isNull(setData[childKeyItem]) ? '' : setData[childKeyItem]
          })
        } else {
          item.val = this.basics.isNull(setData[item.key]) ? '' : setData[item.key]
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
