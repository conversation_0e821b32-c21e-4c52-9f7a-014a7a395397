import moment from 'moment'

/**
 * 将数据存储到localStorage，并设置过期时间
 * @param {String} key 存储的键
 * @param {any} value 存储的值
 * @param {Number} days 过期天数，默认7天
 * @param {Boolean} expireAtDayEnd 是否在当天结束时过期（0点），优先级高于days
 */
export function setLocalStorage(key, value, days = 7, expireAtDayEnd = false) {
  if (!key) return

  let expiry

  if (expireAtDayEnd) {
    // 设置过期时间为今天的23:59:59.999
    expiry = moment().endOf('day').valueOf()
  } else {
    // 否则使用天数作为过期时间
    expiry = moment().add(days, 'days').valueOf()
  }

  const item = {
    value,
    expiry
  }

  localStorage.setItem(key, JSON.stringify(item))
}

/**
   * 从localStorage获取数据，如果过期则删除并返回null
   * @param {String} key 存储的键
   * @returns {any} 存储的值，如果过期则返回null
   */
export function getLocalStorage(key) {
  if (!key) return null

  const itemStr = localStorage.getItem(key)
  if (!itemStr) return null

  try {
    const item = JSON.parse(itemStr)
    const now = moment().valueOf()

    // 检查是否过期
    if (now > item.expiry) {
      // 如果过期，删除该项并返回null
      localStorage.removeItem(key)
      return null
    }

    return item.value
  } catch (e) {
    // 如果解析失败，删除该项并返回null
    localStorage.removeItem(key)
    return null
  }
}

/**
   * 从localStorage中移除指定键的数据
   * @param {String} key 存储的键
   */
export function removeLocalStorage(key) {
  if (!key) return
  localStorage.removeItem(key)
}

/**
   * 将数据存储到localStorage，并设置短期过期时间（以秒为单位）
   * @param {String} key 存储的键
   * @param {any} value 存储的值
   * @param {Number} seconds 过期秒数，默认8秒
   */
export function setLocalStorageWithShortExpiry(key, value, seconds = 8) {
  if (!key) return

  // 设置过期时间为当前时间加上指定的秒数
  const expiry = moment().add(seconds, 'seconds').valueOf()

  const item = {
    value,
    expiry
  }

  localStorage.setItem(key, JSON.stringify(item))
}
