<template>
  <div>
    <el-form :inline="true" :model="formInline" class="demo-form-inline" size="mini">
      <el-form-item label="选择账户">
        <el-select v-model="formInline.accountId" placeholder="请选择" @change="getTableData">
          <el-option
            v-for="item in accountOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!--    table-->
    <div v-loading="tableLoading" class="finance-table">
      <div class="table-item">
        <div class="label">公司账户重置总金额</div>
        <div class="val">{{ tableData.rechargeTotalAmount || '--' }}</div>
      </div>
      <div class="table-item">
        <div class="table-item-child">
          <div class="label">公司账户充值余额</div>
          <div class="val">{{ tableData.rechargeBalance || '--' }}</div>
        </div>
        <div class="table-item-child">
          <div class="label">公司账户保证金</div>
          <div class="val">{{ tableData.marginAmount || '--' }}</div>
        </div>
      </div>
      <div class="table-item">
        <div class="table-item-child">
          <div class="label">公司账户冻结金额</div>
          <div class="val">{{ tableData.freezeAmount || '--' }}</div>
        </div>
        <div class="table-item-child">
          <div class="label">公司账户赠送金额</div>
          <div class="val">{{ tableData.giveAmount || '--' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAccountApi } from '@/api/mechanism'

const formInlineState = {
  accountId: null
}
export default {
  name: 'Finance',
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      formInline: { ...formInlineState },
      accountOptions: [],
      tableData: {}
    }
  },
  methods: {
    // 刷新列表
    async refreshList() {
      // 获取账户
      await this.getAccount()
      this.getTableData()
    },
    // 获取表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const { data } = await getAccountApi({
          pageNumber: 1,
          pageSize: 10000,
          accountId: this.formInline.accountId
        })

        this.tableData = data.records[0]
      } catch (err) {
        console.log(err)
      } finally {
        this.tableLoading = false
      }
    },
    async getAccount() {
      try {
        const params = {
          pageNumber: 1,
          pageSize: 10000,
          accountId: this.id
        }
        const { data } = await getAccountApi(params)

        this.accountOptions = data.records.map(item => {
          return {
            id: item.id,
            name: `${item.accountName}(${item.accountNo})`
          }
        })

        this.formInline.accountId = this.accountOptions[0].id
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.finance-table{
  width: 100%;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  border: 1px solid #eee;
  .table-item{
    width: 100%;
    display: flex;
    border-top: 1px solid #eee;
    height: 40px;
    &:first-child {
      border-top: none;
    }
    .table-item-child{
      display: flex;
      flex: 1;
      height: 100%;
    }
    .label{
      width: 150px;
      border-right: 1px solid #eee;
      padding: 0 10px;
      height: 100%;
      display: flex;
      align-items: center;
    }
    .val {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 10px;
      border-right: 1px solid #eee;
    }
  }
}
</style>
