import moment from 'moment'

const statisticsConfig = {
  defaultDate(obj = false, subtract = 6, args = {}) {
    if (obj) {
      return {
        startDate: moment()
          .subtract(subtract, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        ...args
      }
    } else {
      return [moment().subtract(subtract, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    }
  }
}

export default statisticsConfig
