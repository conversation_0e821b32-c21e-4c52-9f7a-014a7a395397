
import { logout, user_me } from '@/api/user'
import utils from '@/libs/utils'
import { getToken, removeToken, setToken } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    authorization: getToken(),
    name: utils.getUserData().userName,
    roleName: utils.getUserData().roleName,
    menus: utils.getMenus(),
    lastLoginTime: '',
    userInfo: {},
    isLogin: false
  }
}

const state = getDefaultState()

const mutations = {
  authorization(state, token) {
    state.authorization = token
    setToken(token)
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    utils.setUserData(userInfo)
  },
  SET_LAST_LOGIN_TIME(state, lastLoginTime) {
    state.lastLoginTime = lastLoginTime
    utils.setLastLoginTime(lastLoginTime)
  },
  SET_NAME(state, name) {
    state.name = name
  },
  SET_ROLE_NAME(state, roleName) {
    state.roleName = roleName
  },
  SET_MENUS(state, menus) {
    state.menus = menus
    utils.setMenus(menus)
  },
  SET_LOGIN(state, isLogin) {
    state.isLogin = isLogin
  }
}

const actions = {
  // get user info
  getUserInfo({ commit }, token) { // 获取用户信息
    return new Promise((resolve, reject) => {
      user_me().then(res => {
        const formatMenu = utils.formatMenu(res.data.menus)
        const user = {
          userId: res.data.id,
          userName: res.data.username,
          roleName: res.data.roleName,
          localIp: res.data.ip,
          buttons: res.data?.buttons
        }
        commit('SET_NAME', user.userName)
        commit('SET_ROLE_NAME', user.roleName)
        commit('SET_LOGIN', true)
        commit('SET_MENUS', formatMenu)
        commit('SET_USER_INFO', user) // 存储用户信息
        resolve()
      }).catch(err => {
        reject(err)
      })
    }).catch(err => {
      throw new Error(err)
    })
  },
  logout({ commit }, token) {
    return new Promise((resolve, reject) => {
      logout({
        token: token
      }).then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

