import {
  get
} from '@/libs/axios.package'

/**
* 首页统计数据
*/
// export const getHomeStatisticsApi = obj => { return get("/index/statistics", obj )};
/**
* H5渠道和站内关键指标
*/
export const getHomeKeyWordCountNowDay = obj => { return get('/orderStatistic/keyWordCountNowDay', obj) }

/**
* 首页统计数据
*/
// export const getHomeIndexStatistic = obj => { return get("/index/statistic", obj )};

/**
 * H5渠道今日实时数据
 */

export const getHomeHFIndexStatistic = obj => { return get('/index/h5/statistic', obj) }

/**
 * 站内今日实时数据
 */

export const getHomAppeKeyWordCount = obj => { return get('/index/app/keyWordCount', obj) }

/**
 * 获取内链跳转数据
 */
export const get_innerlinkcode = obj => { return get('/innerlinkcode', obj) }

