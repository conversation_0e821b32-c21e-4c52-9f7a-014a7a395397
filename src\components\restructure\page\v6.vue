<script>
import CheckboxPage from './checkbox.vue'
import Page from './index.vue'
function normalizeProps(vm, keys) {
  const { list, ...otherProps } = vm.$attrs
  const listShow = filterTableHidden(list, keys)
  return {
    on: vm.$listeners,
    props: { ...otherProps, list: listShow },
    scopedSlots: vm.$scopedSlots
  }
}

function getSetting(
  vm,
  all = false,
  pathPrefix = '@setting',
  isExpanded = false
) {
  if (!vm.$attrs?.list) {
    return
  }

  const path = vm.$parent?.$parent?.$parent?.listQuery?.showDataType + pathPrefix + vm.$parent?.$route?.path
  const settingStr = localStorage.getItem(path)

  if (isExpanded) {
    return settingStr ? JSON.parse(settingStr) : []
  }

  if (!all && settingStr) {
    return JSON.parse(settingStr)
  }

  return filterListData(vm.$attrs.list, !all)
}

function setSetting(vm, setting, pathPrefix = '@setting') {
  if (!vm.$attrs?.list) {
    return
  }
  const path = vm.$parent?.$parent?.$parent?.listQuery?.showDataType + pathPrefix + vm.$parent?.$route?.path
  if (
    (pathPrefix === '@setting' && setting.length === 0) ||
    !Array.isArray(setting)
  ) {
    return new Error('setting is empty')
  }
  localStorage.setItem(path, JSON.stringify(setting))
}

function resetSetting(vm, pathPrefix = '@setting') {
  const path = pathPrefix + vm.$parent?.$route?.path
  localStorage.removeItem(path)
}

/**
 * 过滤显示的key
 * @param {array} data
 * @param {boolean} all
 */
function filterListData(data, all) {
  if (!Array.isArray(data)) {
    return []
  }
  return data.reduce((pre, cur) => {
    const showKey = all ? true : (cur.defaultShowKey ?? true) // 是否依赖defaultShowKey字段过滤

    if (!cur.tableHidden && showKey && cur.key) {
      pre.push(cur.key)
    }
    if (!cur.tableHidden && showKey && cur.children) {
      pre.push(
        ...filterListData(cur.children, all)
      )
    }
    return pre
  }, [])
}

/**
 * 树形数组，过滤
 */
function filterTreeData(data, setting) {
  return data.reduce((pre, cur) => {
    if (cur.children) {
      cur.children = filterTreeData(cur.children, setting)
    }

    if (setting.includes(cur.key)) {
      return pre.concat([cur])
    }
    return pre
  }, [])
}
/**
 * 树形数组，根据过滤数组，对应数据添加tableHidden属性
 */
function filterTableHidden(data, setting) {
  return data.reduce((pre, cur) => {
    const { children = [], ...i } = cur
    if (
      children.length === 0 &&
      !setting.includes(i.key) &&
      !i.tableHidden &&
      !i.search
    ) {
      return pre
    }
    if (children.length > 0 && !setting.includes(i.key) && !i.tableHidden) {
      const child = filterTableHidden(children, setting)
      if (child.length > 0) {
        i.children = child
        return pre.concat([i])
      }
      return pre
    }
    return pre.concat([
      {
        ...i,
        children:
          children.length > 0
            ? filterTableHidden(children, setting)
            : undefined,
        tableHidden: !setting.includes(i.key)
      }
    ])
  }, [])
}

export default {
  name: 'PageNext',
  data() {
    return {
      checkboxShow: false
    }
  },
  render(h) {
    const ctx = this._self

    const data = ctx.$attrs.list
    let defaultCheckedKeys = getSetting(ctx)
    const allKeys = getSetting(ctx, true)
    const checkboxList = filterTreeData(
      JSON.parse(JSON.stringify(data)),
      allKeys
    )

    return h(Page, normalizeProps(ctx, defaultCheckedKeys), [
      h(
        'template',
        {
          slot: 'searchLine'
        },
        [
          h(
            'el-button',
            {
              style: 'display: inline-block; margin: 0 0 0 10px;',
              props: {
                size: 'small',
                type: 'primary',
                icon: ctx.checkboxShow ? 'el-icon-caret-top' : 'el-icon-caret-bottom',
                plain: true
              },
              on: {
                click() {
                  ctx.checkboxShow = !ctx.checkboxShow
                }
              }
            },
            ctx.checkboxShow ? '收起' : '展开'
          ),
          h(CheckboxPage, {
            props: {
              show: ctx.checkboxShow,
              data: checkboxList,
              'default-checked-keys': defaultCheckedKeys
            },
            on: {
              change(val) {
                defaultCheckedKeys = val
                setSetting(ctx, val)
                ctx.$forceUpdate()
                ctx.$store.commit('setTableShouldUpdate')
              },
              reset() {
                resetSetting(ctx)
                ctx.$forceUpdate()
                ctx.$store.commit('setTableShouldUpdate')
              }
            }
          })
        ]
      )
    ])
  }
}
</script>
<style lang="scss">
.el-popper--page-next {
  padding: 0px !important;
}

.el-tree-node__content {
  height: 33px;
}
.title-container--next {
  text-align: right;
  margin-bottom: 10px;
}
</style>
