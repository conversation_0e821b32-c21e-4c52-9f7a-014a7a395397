<template>
  <el-popover
    placement="right"
    width="400"
    trigger="manual"
    v-model="showModal"
  >
    <div id="emoji" class="emoji">
      <el-scrollbar wrap-class="menu-wrap" style="height:100%">
        <span v-for="(item, index) in emojiList" :key="index" class="emoji-item" @click="selectEmoji(item)">{{item}}</span>
      </el-scrollbar>
    </div>
    <el-button slot="reference" type="text" @click="openEmoji">添加表情</el-button>
  </el-popover>
</template>

<script>
// import Emoji from "@/libs/emoji";
import EmojiData from "@/libs/emojiData";
export default {
  name: 'emoji',
  components: {},
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    emojiList: [],
    showModal: false
  }),
  watch: {},
  mounted() {
    this.emojiList = EmojiData
  },
  methods: {
    selectEmoji(item) {
      this.showModal=false
      this.$emit('selectEmoji', item)
    },
    openEmoji() {
      if(!this.disabled) {
        this.showModal = !this.showModal
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.emoji {
  height: 300px;
  .emoji-item {
    display: inline-block;
    margin: 3px;
    font-size: 18px;
    cursor: pointer;
  }
  & ::v-deep.menu-wrap {
    overflow-x: hidden!important;
  }
}

</style>
