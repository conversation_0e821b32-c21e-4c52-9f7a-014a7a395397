<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :direction="direction"
    :size="size"
    :with-header="false"
    :wrapper-closable="false"
    @open="$emit('open')"
    @opened="$emit('opened')"
    @close="$emit('close')"
    @closed="$emit('closed')"
  >
    <div v-if="visible" class="drawer">
      <div v-if="showTitle" class="title">
        <span class="title_text">{{ title }}</span>
        <div class="close" @click="handleClose">
          <i class="el-icon-close" />
        </div>
      </div>
      <!-- 内容区域 -->
      <div class="content">
        <slot />
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'BaseDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    size: {
      type: String,
      default: '50%'
    },
    // 是否显示title
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.$emit('cancel')
      this.handleClose()
    },
    handleConfirm() {
      this.$emit('confirm', this.handleClose)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  padding: 0;
}

.drawer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;

  .title {
    width: 100%;
    height: 40px;
    background-color: #fff;
    display: flex;
    align-items: center;
    position: relative;
    justify-content: space-between;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
    .close {
      width: 40px;
      height: 40px;
      // background-color: #000;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      >i{
        //font-size: 18px;
      }
    }
    .title_text {
      font-size: 22px;
      color: #333;
      font-weight: bold;
      line-height: normal;
      margin-left: 10px;
    }
  }

  .content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
  }

  .footer {
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    text-align: center;
    flex-shrink: 0;
    background: #fff;

    .el-button {
      margin-left: 20px;
    }
  }
}
</style>
