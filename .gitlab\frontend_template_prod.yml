apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${SERVER_NAME}
  name: ${SERVER_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: ${POD_SPEC}
  selector:
    matchLabels:
      app: ${SERVER_NAME}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  minReadySeconds: 1
  template:
    metadata:
      labels:
        app: ${SERVER_NAME}
    spec:
      terminationGracePeriodSeconds: 60
      affinity: 
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution: 
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ${SERVER_NAME}
              topologyKey: kubernetes.io/hostname
      nodeSelector:
        node_tag: ${K8S_NAMESPACE}
      containers:
        - name: ${SERVER_NAME}
          imagePullPolicy: Always
          image: registry.cn-hangzhou.aliyuncs.com/yoc_repo/${SERVER_NAME}:${DOCKER_TAG}
          env:
            - name: LOGSAVESTDOUTL
              value: ${K8S_NAMESPACE}-${SERVER_NAME}
            - name: aliyun_logs_${K8S_NAMESPACE}-${SERVER_NAME}
              value: stdout
            - name: aliyun_logs_${SERVER_NAME}_ttl
              value: "90"
            - name: aliyun_logs_${SERVER_NAME}_shard
              value: "2"
          resources:
            requests:
              memory: 512Mi
              cpu: 10m
            limits:
              memory: 4Gi
              cpu: "2"
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","sleep 2 && nginx -s quit"]
          livenessProbe:
            httpGet:
              path: /
              port: 80
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 2
          readinessProbe:
            httpGet:
              path: /
              port: 80
            failureThreshold: 3
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2

---
apiVersion: v1
kind: Service
metadata:
  name: ${SERVER_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  type: ClusterIP
  ports:
    - port: 80
      name: ${SERVER_NAME}
  selector:
    app:  ${SERVER_NAME}
