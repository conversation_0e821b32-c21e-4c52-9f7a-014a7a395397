<template>
  <div class="tree">
    <el-scrollbar wrap-class="tree" view-class="el-tree" style="height:100%">
      <el-tree
        ref="menuTree"
        v-loading="loading"
        :data="menuData"
        show-checkbox
        node-key="id"
        :props="defaultProps"
      />
    </el-scrollbar>
  </div>
  <!-- <div style="text-align: right;padding: 10px 30px 0 0;">
      <el-button type="primary" @click="sure">确定</el-button>
    </div> -->
</template>

<script>
import { get_menus_permission, get_role_by_id, deployRelationPermission } from '@/api/system'
import { handleMenuData } from '@/utils/menuCodes'
export default {
  props: {
    roleId: {
      type: [Number, String],
      default: ''
    }
  },
  data: () => ({
    defaultProps: {
      label: 'title',
      children: 'children'
    },
    menuData: [],
    actionData: [],
    loading: false,
    leafArr: [],
    permissions: [],
    initActiveButtons: [] // 默认勾选的权限按钮
  }),
  watch: {
    roleId() {
      this.getRoleDetail()
    }
  },
  mounted() {
    this.getRoleDetail()
  },
  methods: {
    getRoleById() {
      get_role_by_id(this.roleId).then(res => {
        if (res.code === 200) {
          const activeButtons = res.data.buttons || []
          this.getLeafId(this.menuData, this.leafArr)
          const initMenus = res.data.menus.filter(item => this.leafArr.includes(item)) || []
          this.getDefaultReviewChecked(res.data.menus, this.menuData, activeButtons)
          initMenus.push(...activeButtons, ...this.initActiveButtons)
          this.$refs.menuTree.setCheckedKeys(initMenus)
          this.permissions = res.data.permissions
        }
        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
    getDefaultReviewChecked(menus, menuData, activeButtons) {
      if (!menuData || !menuData.length || !menus || !menus.length) {
        return
      }
      for (let i = 0; i < menuData.length; i++) {
        const item = menuData[i]
        if (item.children && item.children.length > 0) {
          const childId = item.children[0].id
          if (childId && (typeof childId === 'string') && menus.includes(item.id)) {
            !activeButtons.includes(childId) && this.initActiveButtons.push(childId)
          } else {
            this.getDefaultReviewChecked(menus, item.children, activeButtons)
          }
        }
      }
    },
    getRoleDetail() {
      this.loading = true
      get_menus_permission().then(res => {
        const { menus } = res?.data ?? { }
        this.menuData = handleMenuData(menus)
        this.actionData = res.data.permissions
        this.getRoleById()
      }).catch(e => {
        this.loading = false
      })
    },
    close() {
      this.$emit('close')
    },
    sure() {
      let menus = this.$refs.menuTree.getCheckedKeys()
      const halfMenus = this.$refs.menuTree.getHalfCheckedKeys()
      menus.push(...halfMenus)
      const buttonCode = menus.filter(item => typeof item === 'string')
      menus = menus.filter(item => typeof item === 'number')
      if (menus.length > 0) {
        const postData = {
          roleId: this.roleId,
          menus: [...menus],
          permissions: this.permissions,
          buttonCode
        }
        deployRelationPermission(postData).then((res) => {
          if (res.code == 200) {
            this.$message.success('配置成功')
            this.$store.dispatch('user/getUserInfo')
            this.$emit('success')
            this.close()
            setTimeout(() => {
              location.reload()
            }, 200)
          }
        })
      } else {
        this.$alert('菜单权限不能为空', {
          confirmButtonText: '确定',
          type: 'warning'
        })
      }
    },

    getLeafId(tree, newArr) {
      if (tree.length === 0 || !tree) {
        return
      }
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].children == null) {
          newArr.push(tree[i].id)
        } else {
          // if (tree[i].children.length > 0) {
          //   const firstChildId = tree[i].children[0].id
          //   // console.log('firstChildId------', this.initActiveButtons, firstChildId)
          //   const isButtonsChildren = typeof firstChildId === 'string' && firstChildId.includes('review') && !this.initActiveButtons.includes(firstChildId)
          //   if (isButtonsChildren) {
          //     newArr.push(firstChildId)
          //     console.log(newArr, 'nerArry')
          //   }
          // }
          this.getLeafId(tree[i].children, newArr)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.tree-title {
  font-size: 18px;
  padding: 15px 0;
}
.tree {
  padding: 15px;
  height: 90%;
  border: #ddd solid 1px;
  ::v-deep .el-scrollbar__wrap{ overflow-x:hidden; }
}
</style>
