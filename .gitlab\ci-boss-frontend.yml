.docker_build: &docker_build
  - |-
    export DOCKER_TAG="${K8S_NAMESPACE}_${CI_COMMIT_SHORT_SHA}_$(date +'%Y%m%d%H%M%S')"
    envsubst '\${SERVER_NAME}' < ${NGINX_TEMPLATES} > ${SERVER_NAME}.conf
    envsubst < ${DOCKERFILE_PATH} | docker build -f - -t registry-vpc.cn-hangzhou.aliyuncs.com/yoc_repo/${SERVER_NAME}:${DOCKER_TAG} .
    docker login -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD} registry-vpc.cn-hangzhou.aliyuncs.com
    docker push registry-vpc.cn-hangzhou.aliyuncs.com/yoc_repo/${SERVER_NAME}:${DOCKER_TAG}
    docker rmi registry-vpc.cn-hangzhou.aliyuncs.com/yoc_repo/${SERVER_NAME}:${DOCKER_TAG}

.deploy_k8s: &deploy_k8s
  - |-
    envsubst < ${K8S_TEMPLATES} | tee ${SERVICE_CONFIG_PATH}/${SERVER_NAME}.yml | kubectl apply --record=true -f -
    sleep 5
    kubectl -n ${K8S_NAMESPACE} rollout status deployment ${SERVER_NAME}

cache:
  paths:
    - node_modules/

variables:
  NGINX_TEMPLATES: ".gitlab/nginx-template.conf"
  DOCKERFILE_PATH: ".gitlab/Dockerfile"
  SERVER_NAME: "boss-frontend"

YXC_LSXB_TEST_boss_frontend:
  stage: frontend_deploy
  tags:
    - LSXB_FRONTEND_TEST
  allow_failure: false
  variables:
    K8S_TEMPLATES: ".gitlab/frontend_template.yml"
    POD_SPEC: 1
    K8S_NAMESPACE: "lsxb-test"
  only:
    refs:
      - test
    variables:
      - $PROJECT_JOB == "YXC_LSXB_TEST_boss_frontend"
  except:
    refs:
      - master
  before_script:
    - |-
      export KUBECONFIG="${HOME}/k8s_config/${K8S_NAMESPACE}/config"
      export SERVICE_CONFIG_PATH="${HOME}/deploy/${K8S_NAMESPACE}"
      mkdir -p ${HOME}/k8s_config/${K8S_NAMESPACE} ${SERVICE_CONFIG_PATH}
      echo ${YXC_EC_TEST_K8S} |base64 -d > $KUBECONFIG
  script:
    - |-
      npm config set registry ${NMP_MIRROR}
      npm install
      npm run build:test
    - *docker_build
    - *deploy_k8s
  environment:
    name: test/${SERVER_NAME}

YXC_LSXB_PRE_boss_frontend:
  stage: frontend_deploy
  tags:
    - LSXB_FRONTEND_PRE
  allow_failure: false
  variables:
    K8S_TEMPLATES: ".gitlab/frontend_template.yml"
    POD_SPEC: 1
    K8S_NAMESPACE: "lsxb-pre"
  only:
    refs:
      - pre-release
    variables:
      - $PROJECT_JOB == "YXC_LSXB_PRE_boss_frontend"
  except:
    refs:
      - master
  before_script:
    - |-
      export KUBECONFIG="${HOME}/k8s_config/${K8S_NAMESPACE}/config"
      export SERVICE_CONFIG_PATH="${HOME}/deploy/${K8S_NAMESPACE}"
      mkdir -p ${HOME}/k8s_config/${K8S_NAMESPACE} ${SERVICE_CONFIG_PATH}
      echo ${YXC_EC_PRE_K8S} |base64 -d > $KUBECONFIG
  script:
    - |-
      npm config set registry ${NMP_MIRROR}
      npm install
      npm run build:preRelease
    - *docker_build
    - *deploy_k8s
  environment:
    name: pre/${SERVER_NAME}

YXC_LSXB_PROD_boss_frontend:
  stage: frontend_deploy
  tags:
    - LSXB_FRONTEND_PROD
  allow_failure: false
  variables:
    K8S_TEMPLATES: ".gitlab/frontend_template_prod.yml"
    POD_SPEC: 1
    K8S_NAMESPACE: "lsxb-pro"
  only:
    refs:
      - master
    variables:
      - $PROJECT_JOB == "YXC_LSXB_PROD_boss_frontend"
  before_script:
    - |-
      export KUBECONFIG="${HOME}/k8s_config/${K8S_NAMESPACE}/config"
      export SERVICE_CONFIG_PATH="${HOME}/deploy/${K8S_NAMESPACE}"
      mkdir -p ${HOME}/k8s_config/${K8S_NAMESPACE} ${SERVICE_CONFIG_PATH}
      echo ${YXC_LSXB_PROD_K8S} |base64 -d > $KUBECONFIG
  script:
    - |-
      npm config set registry ${NMP_MIRROR}
      npm install
      npm run build:prod
    - *docker_build
    - *deploy_k8s
  environment:
    name: prod/${SERVER_NAME}
