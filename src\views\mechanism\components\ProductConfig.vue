<!--产品配置-->
<template>
  <div>
    <el-dialog
      title="产品配置"
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @open="handleOpen"
    >
      <page ref="pageRef" :request="request" :list="list">
        <div slot="searchContainer" style="display: inline-block">
          <el-button size="small" type="primary" @click="handleCreate(null)">新增报价</el-button>
        </div>
      </page>
    </el-dialog>
    <QuoteForm v-model="quoteFormShow" :form-id="productConfigId" :agencies-id="id" @close="handleClose" />
  </div>
</template>

<script>
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { MechanismStatusList } from '@/views/mechanism/enum'
import QuoteForm from './QuoteForm.vue'
import { getProductConfigListApi, changeQuoteStatusApi } from '@/api/mechanism'
import { getProductTypeListApi } from '@/api/product'

export default {
  name: 'AccountMan',
  components: { page, QuoteForm },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      weeks: [
        {
          value: '1',
          label: '周一'
        },
        {
          value: '2',
          label: '周二'
        },
        {
          value: '3',
          label: '周三'
        },
        {
          value: '4',
          label: '周四'
        },
        {
          value: '5',
          label: '周五'
        },
        {
          value: '6',
          label: '周六'
        },
        {
          value: '7',
          label: '周日'
        }
      ],
      productConfigId: null,
      quoteFormShow: false,
      productTypeOptions: [],
      listQuery: {
        agenciesId: this.id,
        productId: null,
        status: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data, agenciesId: this.id }
          const res = await getProductConfigListApi(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    list() {
      return [
        {
          title: '产品类型',
          key: 'productId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.productTypeOptions,
          options: {
            placeholder: '请选择产品类型'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: MechanismStatusList,
          options: {
            placeholder: '请选择状态'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '序号',
          key: 'id',
          width: 80,
          render: (_h, params) => {
            return <div>{params.data.$index + 1}</div>
          }
        },
        {
          title: '产品类型',
          key: 'productName'
        },
        {
          title: '产品描述',
          key: 'description'
        },
        {
          title: '产品报价(元)',
          key: 'amount',
          width: 120
        },
        {
          title: '城市范围',
          key: 'areas',
          render: (_h, params) => {
            // 如果areaIds值为-1 渲染为全国
            if (params.data.row.areaIds && params.data.row.areaIds.includes('-1')) {
              return <div>全国</div>
            } else {
              // 遍历 areas
              const areasMap = params.data.row.areas.map(item => {
                return {
                  provinceName: item.provinceName,
                  cityName: item.cityName
                }
              })
              return <div>
                {areasMap.map(item => {
                  return <div>{item.provinceName}{item.cityName}</div>
                })}
              </div>
            }
          }
        },
        {
          title: '接收时间段',
          key: 'startAcceptTime',
          width: 120,
          render: (_h, params) => {
            return <div>{params.data.row.startAcceptTime}-{params.data.row.endAcceptTime}</div>
          }
        },
        {
          title: '接收星期',
          key: 'acceptWorkDay',
          render: (_h, params) => {
            const days = params.data.row.acceptWorkDay.split(',')
            const formatWeek = days.map((item, index) => {
              const curWeek = this.weeks.find(it => it.value === item)
              return (
                <span key={item}>
                  {curWeek?.label}
                  {index !== days.length - 1 ? '，' : ''}
                </span>
              )
            })

            return <div>{formatWeek}</div>
          }
        },
        {
          title: '节假日是否接收',
          key: 'holidayAccept',
          width: 120,
          render: (_h, params) => {
            return <div>{params.data.row.holidayAccept === 1 ? '是' : '否'}</div>
          }
        },
        {
          title: '创建人',
          key: 'createByName'
        },
        {
          title: '创建时间',
          key: 'createTime'
        },
        {
          title: '更新时间',
          key: 'updateTime'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: MechanismStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          fixed: 'right',
          width: 110,
          activeType: [
            {
              text: '编辑',
              key: 'detail',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleCreate(params.id)
              }
            },
            {
              text: '禁用|启用',
              key: 'operation',
              type: tableItemType.activeType.event,
              render: (_h, params) => {
                return <el-button type='primary' plain onClick={() => this.changeQuoteStatus(params.data)}>{ params.data.status === 1 ? '禁用' : '启用' }</el-button>
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    changeQuoteStatus(row) {
      this.$confirm(`确认${row.status === 1 ? '禁用' : '启用'}吗？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async() => {
        try {
          await changeQuoteStatusApi(row.id, row.status === 1 ? 0 : 1)
          this.$message.success('操作成功')
          this.$store.dispatch('tableRefresh', this)
        } catch (err) {
          console.log(err)
        }
      })
    },
    handleOpen() {
      this.getProductTypeList()
      this.$nextTick(() => {
        this.$refs.pageRef.exposeReset()
      })
    },
    handleClose() {
      this.$store.dispatch('tableRefresh', this)
    },
    handleCreate(id) {
      this.productConfigId = id
      this.quoteFormShow = true
    },
    // 获取产品类型
    async getProductTypeList() {
      try {
        const { data } = await getProductTypeListApi()

        this.productTypeOptions = data.map(item => {
          return {
            id: item.id,
            name: item.productName
          }
        })
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
