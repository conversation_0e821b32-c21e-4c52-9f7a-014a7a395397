<template>
  <div>
    <el-form>
      <el-radio-group v-model="type" @change="select">
        <el-radio label="0">全渠道</el-radio>
        <el-radio label="1">投放渠道</el-radio>
      </el-radio-group>
      <el-button
        v-if="type === '1'"
        style="margin-left: 8px"
        type="primary"
        size="mini"
        @click="showDialog(), queryList()"
      >选择渠道</el-button>
      <div v-if="type === '1'" ref="codeList">
        <span
          v-for="(item, index) in tempChannelList"
          :key="index"
          style="margin-right: 20px; display: inline-block; min-width: 70px"
        >
          {{ item }}

          <i class="el-icon-close" @click="deleteList(3, index)" />
        </span>
      </div>
      <el-dialog
        v-if="dialogChannelVisible"
        :before-close="done => ((inputVisible = false), done())"
        :visible.sync="dialogChannelVisible"
        title="添加渠道"
        append-to-body
        width="800px"
      >
        <el-tabs v-model="activeName2">
          <el-tab-pane label="渠道选择" name="tab1">
            <h1>搜索</h1>

            <el-input v-model="queryPra.keyword" style="width: 300px" size="small" />&nbsp;

            <el-button type="primary" @click=";(queryPra.pageNumber = 1), queryList()">搜索</el-button>

            <el-table
              ref="multipleTable"
              :data="list"
              tooltip-effect="dark"
              row-key="channelCode"
              style="width: 100%"
              @select="selectionChange"
              @select-all="selcetionAllChange"
            >
              <el-table-column type="selection" width="55" />

              <el-table-column prop="channelCode" label="渠道" width="120" show-overflow-tooltip />

              <el-table-column prop="channelName" label="渠道名称" show-overflow-tooltip />
            </el-table>

            <br>

            <el-pagination
              style="text-align: right"
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryPra.total"
              @size-change="handleSizeChange"
              @current-change="paginChange"
            />
          </el-tab-pane>

          <el-tab-pane label="分组选择" name="tab2">
            <el-tag
              v-for="item in dynamicTags2"
              :key="item.id"
              closable
              :disable-transitions="false"
              @close="handleClose2(item)"
              @click="tagClick2(item)"
            >
              {{ item.groupName }}

              <!-- <a
              @click.stop="handleAddTag2(item)"
              :style="{color: idslist2.filter(dd => dd.id == item.id).length > 0 ? '#666' : 'red'}"
              >{{ idslist2.filter(dd => dd.id == item.id).length > 0 ? '取消' : '选择' }}</a
            > -->

              <a :style="{color: item.isActivted ? '#666' : 'red'}" @click.stop="handleAddTag2(item)">{{
                item.isActivted ? '取消' : '选择'
              }}</a>
            </el-tag>

            <el-button v-if="!inputVisible" class="button-new-tag" size="small" @click="showInput2">+ 新增</el-button>

            <div v-if="inputVisible">
              <el-input
                ref="saveTagInput"
                v-model="addArea2.groupName"
                style="width: 300px"
                class="input-new-tag"
                size="small"
              />

              <h1>搜索</h1>

              <el-input v-model="queryPra2.keyword" style="width: 300px" size="small" />&nbsp;

              <el-button type="primary" @click=";(queryPra2.pageNumber = 1), queryList2()">搜索</el-button>

              <el-table
                ref="multipleTable2"
                :data="list2"
                tooltip-effect="dark"
                row-key="channelCode"
                style="width: 100%"
                @select="selectionChange2"
              >
                <el-table-column type="selection" width="55" />

                <el-table-column prop="channelCode" label="渠道" width="120" show-overflow-tooltip />

                <el-table-column prop="channelName" label="渠道名称" show-overflow-tooltip />
              </el-table>

              <br>

              <el-pagination
                style="text-align: right"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="queryPra2.total"
                @size-change="handleSizeChange2"
                @current-change="paginChange2"
              />

              <el-button @click=";(inputVisible = false), (addArea2 = {})">取 消</el-button>

              <el-button type="primary" @click="handleInputConfirm2">确 定</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div v-if="!inputVisible" slot="footer" class="dialog-footer">
          <el-button @click="closeDialog(3)">取 消</el-button>

          <el-button type="primary" @click="saveChannel">确 定</el-button>
        </div>
      </el-dialog>
    </el-form>
  </div>
</template>

<script>
import {
  GET_CHANNEL_LIST_ALL,
  channelAdGroupType,
  channelAdGroupCodes,
  channelAdGroupDelete
} from '@/api/NewChannel.js'
export default {
  props: {
    // 选择后的渠道
    tempChannelList: {
      type: Array,
      default: () => {
        return []
      }
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      // 渠道相关
      type: '1',
      dialogChannelVisible: false,
      queryPra: { pageNumber: 1, pageSize: 10, total: 0 },
      list: [],
      activeName2: 'tab1',
      queryPra2: { pageNumber: 1, pageSize: 10, total: 0 },
      inputVisible: false,
      dynamicTags2: [],
      onlySetVal2: [], // 每次选择的数据渠道
      list2: [],
      addArea2: { groupName: '', codeGroupIds: [] },
      idslist2: [] // 每次选择id集合渠道

    }
  },
  created() {
    this.type = this.id ? this.tempChannelList.length > 0 ? '1' : '0' : '1'
  },
  methods: {
    select(val) {
      this.$emit('channelTypeFn', val)
    },
    // 渠道相关开始
    showDialog() {
      this.dialogChannelVisible = true
    },
    queryList() {
      GET_CHANNEL_LIST_ALL(this.queryPra).then((res) => {
        this.list = res.data.rows

        this.queryPra.total = res.data.total
      })
    },
    selectionChange(e, row) {
      if (this.onlySetVal2.includes(row.channelCode)) {
        this.onlySetVal2.splice(this.onlySetVal2.indexOf(row.channelCode, 1))
      } else {
        this.onlySetVal2.push(row.channelCode)
      }
    },
    selectionChange2(e, row) {
      if (this.addArea2.codeGroupIds.includes(row.channelCode)) {
        this.addArea2.codeGroupIds.splice(
          this.addArea2.codeGroupIds.indexOf(row.channelCode),
          1
        )
      } else {
        this.addArea2.codeGroupIds.push(row.channelCode)
      }

      console.log('this.addArea2.codeGroupIds', this.addArea2.codeGroupIds)
    },
    selcetionAllChange(rows) {
      rows.forEach((row) => {
        if (this.onlySetVal2.includes(row.channelCode)) {
          this.onlySetVal2.splice(this.onlySetVal2.indexOf(row.channelCode, 1))
        } else {
          this.onlySetVal2.push(row.channelCode)
        }
      })
    },
    selcetionAllChange2(rows) {
      rows.forEach((row) => {
        if (this.addArea2.codeGroupIds.includes(row.channelCode)) {
          this.addArea2.codeGroupIds.splice(
            this.addArea2.codeGroupIds.indexOf(row.channelCode, 1)
          )
        } else {
          this.addArea2.codeGroupIds.push(row.channelCode)
        }
      })
    },
    handleSizeChange(e) {
      this.queryPra.pageSize = e

      this.queryList()
    },

    paginChange(e) {
      this.queryPra.pageNumber = e

      this.queryList()
    },
    handleClose2(tag) {
      this.$confirm('此操作将永久删除该分组, 是否继续?', '提示', {
        confirmButtonText: '确定',

        cancelButtonText: '取消',

        type: 'warning'
      })

        .then(() => {
          channelAdGroupDelete({ id: tag.id }).then((res) => {
            if (res.status == 200) {
              this.dynamicTags2 = this.dynamicTags2.filter(
                (item) => item.id != tag.id
              )

              this.$message({
                type: 'success',

                message: '删除成功!'
              })
            } else {
              this.$message({
                type: 'error',

                message: res.msg
              })
            }
          })
        })

        .catch(() => {
          this.$message({
            type: 'info',

            message: '已取消删除'
          })
        })
    },

    tagClick2(item) {
      channelAdGroupCode({ id: item.id }).then((res) => {
        this.inputVisible = true

        this.addArea2 = {
          groupName: item.groupName,

          id: item.id,

          codeGroupIds: res.data
        }

        this.queryList2()
      })
    },
    handleAddTag2(item) {
      // 需求更改=> 需要添加分组的区域直接到this.tempChannelList 数组中，格式是[]

      // 后端将区域数据直接放在分组对象里面，这里直接concat item.key 然后回显到表格里面（再次打开的时候）

      this.$set(item, 'isActivted', !item.isActivted)
      if (item.isActivted) {
        if (this.idslist2.filter((dd) => dd.id == item.id).length > 0) {
          this.idslist2 = this.idslist2.filter((dd) => dd.id != item.id)
        } else {
          this.idslist2.push(item)
        }
        this.idslist2.forEach((dts) => {
          this.onlySetVal2.push(...dts.codeList)
        })
      } else {
        item.codeList.forEach((item) => {
          const index = this.onlySetVal2.findIndex((o) => o == item)
          this.onlySetVal2.splice(index, 1)
        })
      }
    },
    showInput2() {
      this.inputVisible = true

      this.queryPra2.pageNumber = 1

      this.addArea2 = {
        groupName: '',

        id: '',

        codeGroupIds: []
      }

      this.queryList2()
    },
    queryList2() {
      GET_CHANNEL_LIST_ALL(this.queryPra2).then((res) => {
        this.list2 = res.data.rows

        this.queryPra2.total = res.data.total

        if (
          this.addArea2.codeGroupIds &&
          this.addArea2.codeGroupIds.length > 0
        ) {
          this.$nextTick((_) => {
            this.addArea2.codeGroupIds.forEach((item) => {
              this.list2

                .filter((dt) => item == dt.channelCode)

                .forEach((vals) => {
                  this.$refs.multipleTable2.toggleRowSelection(vals, true)
                })
            })
          })
        }
      })
    },
    handleInputConfirm2() {
      const inputValue = this.addArea2.groupName

      if (inputValue) {
        channelAdGroupCodes({
          ...this.addArea2,
          groupName: inputValue,
          codes: this.addArea2.codeGroupIds
        }).then((res) => {
          if (res.status == 200) {
            this.$message.success(res.msg)

            this.getDYtag2()

            this.inputVisible = false

            this.addArea2.groupName = ''

            this.addArea2.codeGroupIds = []
          }
        })
      }
    },
    handleSizeChange2(e) {
      this.queryPra2.pageSize = e

      this.queryList2()
    },

    paginChange2(e) {
      this.queryPra2.pageNumber = e

      this.queryList2()
    },
    closeDialog() {
      this.dialogChannelVisible = false

      this.onlySetVal2 = []

      this.idslist2 = []
      this.queryPra = { pageNumber: 1, pageSize: 10, total: 0 }

      this.queryPra2 = { pageNumber: 1, pageSize: 10, total: 0 }

      this.inputVisible = false
    },
    saveChannel() {
      const arr = Array.from(
        new Set(this.tempChannelList.concat(this.onlySetVal2))
      )
      this.$emit('channelList', arr)
      // this.tempChannelList
      this.closeDialog()
    },
    getDYtag2() {
      channelAdGroupType({ type: 1 }).then((res) => {
        this.dynamicTags2 = res.data
      })
    },
    deleteList(i, j) {
      this.tempChannelList.splice(j, 1)
    }
    // 渠道相关结束
  }
}
</script>
