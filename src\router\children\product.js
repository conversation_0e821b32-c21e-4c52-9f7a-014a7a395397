/*
 * 产品管理子路由
 * */

const product = [
  {
    path: '/product',
    name: 'Product',
    redirect: '/product/manage'
  },
  {
    path: '/product/manage',
    name: 'ProductManage',
    meta: {
      title: '产品管理',
      buttons: [
        { key: 'create', name: '新增产品' },
        { key: 'edit', name: '编辑' },
        { key: 'status', name: '启用|禁用' }
      ]
    },
    component: () => import('@/views/product/page/manage.vue')
  }
]

export default product
