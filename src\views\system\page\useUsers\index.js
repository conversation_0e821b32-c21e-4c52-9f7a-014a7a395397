import {
  get_admin_list,
  get_admin_role_list,
  adminBranchList
} from '@/api/system'

export const getDepartmentList = () => {
  return adminBranchList().then(res => {
    return res?.data ?? []
  })
}
export const getAllUserList = () => {
  return get_admin_list({
    pageNumber: 1,
    pageSize: 9999
  })
    .then(res => {
      return res.data?.records ?? []
    })
}
export const getRoleLists = () => {
  return get_admin_role_list().then(res => {
    return res?.data ?? []
  })
}
