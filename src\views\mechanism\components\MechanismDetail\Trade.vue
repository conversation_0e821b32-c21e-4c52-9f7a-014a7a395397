<template>
  <page v-if="accountDefault" ref="pageRef" :request="request" :list="list">
    <div slot="searchContainer" style="display: inline-block">
      <el-button :loading="exportLoading" plain size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
  </page>
</template>

<script lang="jsx">
import page from '@/components/restructure/page/index.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { TransactionTypeList } from '@/views/mechanism/enum'
import { getTransactionRecordApi, exportTransactionRecordApi, getAccountApi } from '@/api/mechanism'

export default {
  name: 'Trade',
  components: { page },
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      accountDefault: null,
      exportLoading: false,
      accountOptions: [],
      listQuery: {
        startDate: '',
        endDate: '',
        // 账户
        accountId: this.accountDefault,
        // 交易类型
        type: null
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getTransactionRecordApi(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择账户',
          key: 'accountId',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.accountOptions,
          val: this.accountDefault,
          clearable: false,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '交易类型',
          key: 'type',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: TransactionTypeList,
          options: {
            placeholder: '请选择'
          },
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '交易日期',
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          val: [this.listQuery.startDate, this.listQuery.endDate],
          search: true,
          pickerDay: 9999999,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '交易订单号',
          key: 'transactionId'
        },
        {
          title: '交易账户',
          key: 'agenciesAccount',
          render: (_h, params) => {
            return <div>
              <div>{params.data.row.agenciesAccount.accountName}</div>
              <div>({params.data.row.agenciesAccount.accountNo})</div>
            </div>
          }
        },
        {
          title: '交易备注',
          key: 'remark'
        },
        {
          title: '交易金额(元)',
          key: 'amount'
        },
        {
          title: '交易类型',
          key: 'businessTypeStr'
        },
        // {
        //   title: '交易类型',
        //   key: 'type',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: TransactionTypeList,
        //   listFormat: {
        //     label: 'name',
        //     value: 'id'
        //   }
        // },
        {
          title: '交易时间',
          key: 'updateTime'
        }
      ]
    }
  },
  methods: {
    async handleExport() {
      this.exportLoading = true
      try {
        window.open(exportTransactionRecordApi({
          ...this.listQuery,
          token: this.$store.getters.authorization
        }))
      } catch (err) {
        console.log(err)
      } finally {
        this.exportLoading = false
      }
    },
    // 刷新列表
    async refreshList() {
      // 获取账户
      await this.getAccount()
      this.$nextTick(() => {
        this.$refs.pageRef.exposeReset()
      })
    },
    async getAccount() {
      try {
        const params = {
          pageNumber: 1,
          pageSize: 10000,
          accountId: this.id
        }
        const { data } = await getAccountApi(params)

        this.accountOptions = data.records.map(item => {
          return {
            id: item.id,
            name: `${item.accountName}(${item.accountNo})`
          }
        })
        this.accountDefault = this.accountOptions[0].id
        this.listQuery.accountId = this.accountDefault
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
