<template>
  <div>
    <Template v-if="item.render" :render="item.render" :results="{data: {row: results}}" />
    <RenderCustom v-else-if="item.renderCustom" v-model="getVal" :render="item.renderCustom" :results="{data: {row: results, item}}" />
    <div v-else-if="item.slot">
      {{ item.slot }}
      <slot :name="item.slot" />
    </div>
    <span v-else>
      <SInput v-if="item.type === formItemType.input" v-model="getVal" :item="item" />
      <SSelect
        v-if="item.type === formItemType.select || item.type === formItemType.selectMultiple"
        v-model="getVal"
        :item="item"
        :results="{data: {row: results}}"
      />
      <SSelectSearch
        v-if="item.type === formItemType.selectSearch || item.type === formItemType.selectSearchMultiple"
        v-model="getVal"
        :item="item"
        v-on="$listeners"
      />
      <SRadio v-if="item.type === formItemType.radio" v-model="getVal" :item="item" />
      <SRadioButton v-if="item.type === formItemType.radioButton" v-model="getVal" :item="item" />
      <SCheckbox v-if="item.type === formItemType.checkbox" v-model="getVal" :item="item" />
      <SInputNumber v-if="item.type === formItemType.inputNumber" v-model="getVal" :item="item" />
      <STimePicker v-if="item.type === formItemType.timePicker" v-model="getVal" :item="item" />
      <STimePickerRange v-if="item.type === formItemType.timePickerRange" v-model="getVal" :item="item" />
      <SDateTimePicker v-if="item.type === formItemType.dateTimePicker" v-model="getVal" :item="item" />
      <SDatePicker v-if="item.type === formItemType.datePicker" v-model="getVal" :item="item" />
      <SDatePickerDaterange
        v-if="item.type === formItemType.rangeDatePicker"
        v-model="getVal"
        :item="item"
        @input="testInput"
      />
      <SDatePickerDaterangeGai v-if="item.type === formItemType.datePickerDaterangeGai" v-model="getVal" :item="item" />
      <STimeSelectRangeMultiple
        v-if="item.type === formItemType.timeSelectRangeMultiple"
        v-model="getVal"
        :item="item"
      />
      <upload
        v-if="item.type === formItemType.upload"
        :default-path="getVal"
        :before-upload="getOptions(item).beforeUpload"
        :edit-disable="getOptions(item).editDisable"
        :add-disable="getOptions(item).addDisable"
        @onSuccess="handleSuccess"
      />
      <jsonUpload
        v-if="item.type === formItemType.uploadJson"
        :default-path="getVal"
        :before-upload="getOptions(item).beforeUpload"
        :edit-disable="getOptions(item).editDisable"
        :add-disable="getOptions(item).addDisable"
        @onSuccess="handleSuccess"
      />
      <excelUpload
        v-if="item.type === formItemType.excelUpload"
        :accept="getOptions(item).accept"
        :url="getOptions(item).url"
        :data="getOptions(item).data"
        :name="getOptions(item).name"
        @file-change="fileChange"
      />
      <el-color-picker v-if="item.type === formItemType.colorPicker" v-model="getVal" />
      <SInputGai v-if="item.type === formItemType.inputGai" v-model="getVal" :item="item" />
      <SInputNext
        v-if="item.type === formItemType.inputNext"
        v-model="getVal"
        :item="item"
        :results="{data: {row: results}}"
      />
      <SSwitch v-if="item.type === formItemType.switch" v-model="getVal" :item="item" />
      <SCascader v-if="item.type === formItemType.cascade" v-model="getVal" :item="item" />

      <p v-if="item.type === formItemType.text">
        <span v-if="basics.isArrNull(item.list)">{{ getVal }}</span>
        <span v-for="i in item.list" v-else-if="basics.isArray(item.list)">
          <span v-if="(item.listFormat ? String(i[item.listFormat.value]) : String(i.value)) === String(getVal)">{{
            item.listFormat ? i[item.listFormat.label] : i.label
          }}</span>
        </span>
      </p>
      <p v-if="item.type === formItemType.textDate">
        {{ (getVal, ((item.options && item.options.format) || 'yyyy-MM-dd hh:mm:ss') | parseTimeTwo) }}
      </p>
      <!-- <p v-if="item.type===formItemType.textDateRange">{{getVal, ((item.options&&item.options.format)||'yyyy-MM-dd hh:mm:ss') | parseTimeTwo }}</p> -->
    </span>
    <span
      v-if="item.options && item.options.subTitle"
      :style="item.options.subTitleOptions ? item.options.subTitleOptions.style : {}"
    >{{ item.options.subTitle }}</span>
  </div>
</template>

<script>
import {
  SInput,
  SSelect,
  SRadio,
  SRadioButton,
  SCheckbox,
  SDatePicker,
  Template,
  SDatePickerDaterange,
  SDatePickerDaterangeGai,
  SInputGai,
  SInputNext,
  SSwitch,
  SSelectSearch,
  SCascader,
  STimeSelectRangeMultiple,
  RenderCustom
} from './render'
import upload from '../../../upload/index'
import jsonUpload from '../../../upload/json'
import excelUpload from '../../../upload/views/excel'
import { formItemType } from '@/config/sysConfig'

export default {
  name: 'Index',
  components: {
    SInput,
    SSelect,
    SRadio,
    SRadioButton,
    SCheckbox,
    SDatePicker,
    upload,
    jsonUpload,
    Template,
    SSwitch,
    SDatePickerDaterange,
    excelUpload,
    SDatePickerDaterangeGai,
    SInputGai,
    SInputNext,
    SSelectSearch,
    SCascader,
    STimeSelectRangeMultiple,
    RenderCustom
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    val: '',
    results: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formItemType
    }
  },
  computed: {
    getVal: {
      set(newVal) {
        this.handleInput(newVal)
      },
      get() {
        return this.val
      }
    }
  },
  methods: {
    handleInput(value) {
      this.$emit('update:val', value)
    },
    /* 图片上传成功回调*/
    handleSuccess(data) {
      this.handleInput(data.data)
    },
    testInput(data) {},
    fileChange(data) {
      this.handleInput(Object.values(data).join(','))
    },
    getOptions(item) {
      return item.options ? item.options : {}
    }
  }
}
</script>

<style scoped>
  p {
    margin: 0;
  }
</style>
