<template>
  <div class="noData">
    <div class="icon"><img src="@/assets/img/copy.png" alt=""></div>
    <p class="text">{{text}}</p>
  </div>
</template>

<script>
  export default {
    name: "yc-no-data",
    props: {
      text: {
        type: String,
        default: "提示内容"
      }

    }
  }
</script>

<style lang="scss" scoped>
  /*暂无数据*/
  .noData {
    position: absolute;
    transform: translate(-50%, -75%);
    -webkit-transform: translate(-50%, -75%);
    top: 50%;
    left: 50%;
    .icon {
      margin: auto;
      width: 3.32rem;
      height: 2.22rem;
      img {
        width: 3.32rem;
        height: 2.22rem;
      }
    }
    .text {
      font-size: .28rem;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #B3B3B3;
      line-height: .40rem;
      text-align: center;
      margin-top: .60rem;
    }
  }
</style>
