<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style lang="scss">
  .noArrowInput{
    -moz-appearance: textfield;
    input[type="number"] {
      -moz-appearance: textfield;
    }
    input[type="number"]::-webkit-outer-spin-button,::-webkit-inner-spin-button{
        margin: 0;
        -webkit-appearance: none !important;
    }
  }
  .font12{
    font-size: 12px;
  }
  .font14{
    font-size: 14px;
  }
  .font16{
    font-size: 16px;
  }
  .font18{
    font-size: 18px;
  }
  .bold{
    font-weight: bold;
  }
  .flex-align{
    display: flex;
    align-items: center;
  }
  .popoverTableClass{
    min-width: 55px !important;
  }
  .popoverTableClass .cell{
    min-width: 55px !important;
    padding:0;
  }
</style>
