<template>
  <FormDialog
    ref="dialogFormRef"
    v-model="dialogVisible"
    :title="title"
    width="20%"
    :form-model="{}"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <el-form-item label-width="0">
      <el-select v-model="selectVal" filterable placeholder="请选择" style="width: 100%">
        <el-option
          v-for="item in selectOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
  </FormDialog>
</template>

<script>
import FormDialog from '@/components/FormDialog/index.vue'
import { ProductModeTypeList, BiddingModeTypeList, OnlineModeList } from '../enum'

export default {
  name: 'StrategyPattern',
  components: {
    FormDialog
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Boolean,
    type: {
      type: Number,
      default: null
    },
    formValue: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectVal: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    title() {
      const titleMap = {
        1: '产品模式',
        2: '竞价模式'
      }

      return titleMap[this.type]
    },
    selectOptions() {
      const selectOptionsMap = {
        1: ProductModeTypeList,
        2: BiddingModeTypeList,
        3: OnlineModeList
      }

      return selectOptionsMap[this.type] || []
    }
  },
  watch: {
    formValue() {
      this.selectVal = this.formValue
    }
  },
  methods: {
    handleSubmit(_params, done) {
      this.$emit('selectVal', this.selectVal, this.type)
      this.handleClose()
      done()
    },
    handleClose() {
      this.selectVal = null
      this.$emit('change', false)
      this.$emit('close')
    },
  }
}
</script>
