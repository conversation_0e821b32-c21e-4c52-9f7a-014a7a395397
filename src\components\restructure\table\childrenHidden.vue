<template>
  <div class="table-base">
    <el-checkbox-group
      v-if="changeTab"
      v-model="chooseColumns"
      style="margin-bottom: 20px; display:flex; flex-flow: wrap; align-items:center"
    >
      <el-checkbox
        v-for="item in column"
        :key="item.key || item.type"
        :label="item.key || item.type"
      >
        {{ item.title || '操作' }}
      </el-checkbox>
      <el-button
        type="primary"
        size="mini"
        style="margin-left:30px"
        @click="ensureColumns"
      >保存</el-button>
    </el-checkbox-group>
    <div v-if="tableTitle" class="tab-head">
      <span class="title">{{ tableTitle }}</span>
    </div>
    <el-table
      :key="$store.state.ref.tableShouldUpdate"
      v-loading="tableLoading"
      :data="data"
      size="medium"
      border
      :highlight-current-row="type === tableItemType.tabType.radio"
      :height="tableStyle.height"
      :max-height="maxHeight"
      style="width: 100%"
      :cell-class-name="rowStyle"
      :span-method="spanMethod"
      :tree-props="treeProps"
      :row-key="rowKey"
      @select="selectMethodsType('select')"
      @select-all="selectMethodsType('selectAll')"
      @current-change="currentChange"
      @selection-change="changeFun"
    >
      <el-table-column
        v-if="type === tableItemType.tabType.selection"
        type="selection"
        :selectable="selectableMethods"
        align="center"
        width="55"
      />
      <el-table-column
        v-if="type === tableItemType.tabType.index"
        type="index"
        align="center"
        label="序号"
        width="50"
      />
      <template v-for="(i, index) in orderColumn">
        <template v-if="!i.slot">
          <el-table-column
            v-if="i.tableColumnType"
            :key="i.key || index"
            :type="i.tableColumnType"
            align="center"
            :show-overflow-tooltip="basics.isNull(i.tooltip) ? true : i.tooltip"
            :render-header="basics.isNull(i.renderHeader) ? null : i.renderHeader"
            :width="i.width || '50px'"
            :fixed="i.fixed ? i.fixed : false"
            :sortable="i.sortable ? i.sortable : false"
            :prop="i.key || null"
          />

          <el-table-column
            v-else
            :key="i.key || index"
            :fixed="i.fixed ? i.fixed : false"
            :label="getItemType(i, tableItemType.active) ? '操作' : i.title"
            align="center"
            :show-overflow-tooltip="basics.isNull(i.tooltip) ? true : i.tooltip"
            :render-header="basics.isNull(i.renderHeader) ? null : i.renderHeader"
            :width="i.width || ''"
            :sortable="i.sortable ? i.sortable : false"
            :prop="i.key || null"
          >
            <template v-for="(childrenItem, childrenIndex) in i.children">
              <el-table-column
                v-if="!childrenItem.tableHidden"
                :key="childrenItem.key ||childrenIndex"
                align="center"
                :label="childrenItem.title"
                :show-overflow-tooltip="
                  basics.isNull(childrenItem.tooltip)
                    ? true
                    : childrenItem.tooltip
                "
                :render-header="basics.isNull(childrenItem.renderHeader) ? null : childrenItem.renderHeader"
                :width="childrenItem.width"
                :fixed="childrenItem.fixed ? childrenItem.fixed : false"
                :sortable="childrenItem.sortable ? childrenItem.sortable : false"
                :prop="childrenItem.key || null"
              >
                <template slot-scope="scope">
                  <template v-if="!childrenItem.render">
                    <span>{{ getLabelText(scope.row, childrenItem.key) }}</span>
                  </template>
                  <Template
                    v-else
                    :render="childrenItem.render"
                    :results="{ data: scope, column: childrenItem }"
                  />
                </template>
              </el-table-column>
            </template>

            <template v-if="!i.children" slot-scope="scope">
              <div v-if="getItemType(i, tableItemType.active)">
                <actionButton
                  :active-type="i.activeType"
                  :params="scope.row"
                  :index="scope.$index"
                  @dialog="dialog"
                  @deleteActive="deleteActive(scope.row, scope.$index)"
                  @getById="getById(scope.row)"
                />
              </div>
              <div v-else-if="i.tableView">
                <span>
                  <tableView :column="i" :data="scope.row" />
                </span>
              </div>
              <template v-else-if="!i.render">
                <span
                  v-if="
                    i.exhibitionMode && i.exhibitionMode === exhibitionMode.text
                  "
                >{{ exhibitionModeText(i.list, scope.row[i.key]) }}</span>
                <span>{{ getLabelText(scope.row, i.key, i.standBy) }}</span>
              </template>
              <Template
                v-else
                :render="i.render"
                :results="{ data: scope, column: i }"
              />
            </template>
          </el-table-column>
        </template>
        <template v-if="i.slot">
          <slot :name="i.slot" :column="i" />
        </template>
      </template>
    </el-table>
    <div>
      <pagination
        v-if="paginationState"
        :total="total"
        :page-size.sync="tableInfo.pageSize"
        :current.sync="tableInfo.pageNumber"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import pagination from '../pagination/index'
import { Template, getDefaultListItem } from '../form/components/render/render'
import {
  keyWord,
  picturePath,
  exhibitionMode,
  tableItemType,
  deleteConfirm
} from '@/config/sysConfig'
import tableView from './components/tableView'
import actionButton from './components/button/index'
import { mapRecursion, copy } from '@/config/basicsMethods'
import debounce from 'lodash/debounce'
export default {
  components: {
    pagination,
    Template,
    actionButton,
    tableView
  },
  props: {
    spanMethod: {
      type: Function,
      default: () => {
        return [1, 1]
      }
    },
    currentTab: {
      type: String,
      default: ''
    },
    tableTitle: {
      type: String,
      default: ''
    },
    changeTab: {
      type: Boolean,
      default: false
    },
    column: {
      type: Array,
      required: true
    },
    query: {
      type: Object,
      default: () => {}
    },
    rowStyle: {
      type: Function,
      default: () => {}
    },
    request: {
      type: Object,
      default: () => {
        return {
          getListUrl: () => Promise.resolve([]),
          deleteUrl: () => Promise.resolve()
        }
      }
    },
    type: {
      type: String,
      default: ''
    },
    paginationState: {
      type: Boolean,
      default: true
    },
    maxHeight: {
      type: String,
      default: 'auto'
    },
    rowData: {
      type: Array,
      default: () => []
    },
    selectMethods: {
      type: Object,
      default: () => ({})
    },
    tableStyle: {
      type: Object,
      default: () => ({})
    },
    treeProps: {
      type: Object,
      default: () => ({})
    },
    rowKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      copyColumns: [],
      chooseColumns: [],
      tableItemType: tableItemType,
      exhibitionMode: exhibitionMode,
      keyWord: keyWord,
      total: 0,
      tableInfo: {
        pageSize: 10,
        pageNumber: 1
      },
      data: [],
      orderColumn: [],
      getLabelText: mapRecursion,
      firstEntry: false /* 是否第一次进入 兼容以前的模式*/
    }
  },
  watch: {
    query(to) {
      this.tableInfo = this.$options.data().tableInfo
      this.getTableList()
    },
    column(value) {
      this.orderColumn = this.handlerOrderColumn(value)
      // this.$forceUpdate
    },
    rowData(to) {
      this.tableInfo = this.$options.data().tableInfo
      this.getTableList()
    }
  },
  activated() {
    if (this.firstEntry) {
      this.firstEntry = false
      return
    }
    this.getTableList()
  },
  mounted() {
    this.firstEntry = true
    this.getTableList()
  },
  async created() {
    if (this.changeTab) {
      this.chooseColumns = []
      if (localStorage.getItem(this.currentTab)) {
        this.orderColumn = JSON.parse(localStorage.getItem(this.currentTab))
        for (const i in this.orderColumn) {
          this.chooseColumns.push(
            this.orderColumn[i]['key'] || this.orderColumn[i]['type']
          )
        }
      } else {
        this.orderColumn = JSON.parse(JSON.stringify(this.column))
        for (const i in this.orderColumn) {
          this.chooseColumns.push(
            this.orderColumn[i]['key'] || this.orderColumn[i]['type']
          )
        }
      }
      this.ensureColumns()
    }
    this.orderColumn = await this.handlerOrderColumn()
  },
  methods: {
    ensureColumns() {
      const arr = []
      for (let j = 0; j < this.column.length; j++) {
        for (const i in this.chooseColumns) {
          if (
            this.chooseColumns[i] == this.column[j].key ||
            this.chooseColumns[i] == this.column[j].type
          ) {
            arr.push(this.column[j])
          }
        }
      }
      this.orderColumn = arr
      localStorage.setItem(this.currentTab, JSON.stringify(this.orderColumn))
    },
    picturePath(path) {
      return picturePath(path)
    },
    handleSizeChange(val) {
      /* 条数切换触发*/
      this.tableInfo.pageSize = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      /* 页数切换触发*/
      this.tableInfo.pageNumber = val
      this.getTableList()
    },
    exhibitionModeText(list, index) {
      const filter = list.filter(item => {
        if (item.value === String(index)) {
          return item
        }
      })
      return filter[0].label
    },
    getItemType(item, type) {
      if (item.type && item.type === type) return true
      return false
    },
    getTableList() {
      this.tableLoading = true
      if (!this.basics.isArrNull(this.rowData)) {
        this.data = this.rowData
        this.total = this.data.length
        return false
      }
      this.request
        .getListUrl({ ...{}, ...this.tableInfo, ...this.query })
        .then(msg => {
          let res = copy(msg)
          if (msg.data && (msg.data.rows || msg.data.rows === null)) {
            res = {
              'code': 0,
              'data': msg.data.rows || [],
              'message': null,
              'result': 'success',
              'totalCount': msg.data.total,
              'pageId': null
            }
          }
          if (this.paginationState === false) {
            const { data = [] } = res
            this.data = data
          } else {
            const { data = [] } = res
            this.data = data
            this.total = this.basics.isNull(res.totalCount)
              ? data.length
              : res.totalCount
          }
          this.tableLoading = false
          this.$emit('load', res)
        })
        .catch(e => {
          this.tableLoading = false
        })
    },
    deleteActive(item, index) {
      deleteConfirm(this).then(msg => {
        this.request
          .deleteUrl(item)
          .then(msg => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.data.splice(index, 1)
            this.getTableList()
          })
          .catch(msg => {
            this.$message({
              type: 'error',
              message: '删除失败!'
            })
          })
      })
    },
    getById(item) {
      const getByIdUrl = this.request.getByIdHttp
      const http = this.basics.isNull(getByIdUrl)
        ? () => Promise.resolve(item)
        : this.basics.isObj(getByIdUrl)
          ? () => Promise.resolve(getByIdUrl)
          : getByIdUrl
      this.$emit('getByIdCallback', () => {
        return new Promise(resolve => {
          http(
            {
              id: item.id
            },
            item
          ).then(msg => {
            resolve(msg)
          })
        })
      })
    },
    dialog(data) {
      this.$emit('dialog', data)
    },
    changeFun(selection) {
      try {
        this.selectMethodsType('selectionChange')(selection)
      } catch (e) {
        console.log(e)
      }
      this.$emit('selectionChange', selection)
    },
    selectMethodsType(type) {
      if (this.selectMethods[type]) {
        return this.selectMethods[type]
      } else {
        return function() {}
      }
    },
    selectableMethods() {
      if (this.selectMethods && this.selectMethods['selectable']) {
        return this.selectMethods['selectable'].apply(undefined, arguments)
      }
      return true
    },
    currentChange(value) {
      this.selectMethodsType('currentChange')(value)
    },
    handlerOrderColumn(newValue) {
      let column = ''
      if (this.changeTab) {
        column = copy(this.orderColumn)
      } else {
        column = newValue || copy(this.column)
      }
      column.forEach(async(item, index) => {
        if (
          this.basics.isNull(item.list) &&
          item.tableView &&
          item.tableView === tableItemType.tableView.requestText
        ) {
          if (this.basics.isNull(item.list)) this.$set(item, 'list', [])
          await item.requestList().then(msg => {
            getDefaultListItem(msg.data, item.listFormat, list => {
              item.list.push(list)
            })
          })
        } else if (
          !this.basics.isNull(item.list) &&
          this.basics.isArray(item.list) &&
          !this.basics.isArrNull(item.list)
        ) {
          getDefaultListItem(item.list, item.listFormat, list => {
            item.list[list.index] = list
          })
        }
      })
      return column
    },
    debounceChangeTableKey: debounce(function() {
      this.tableKey++
    }, 400)
  }
}
</script>
<style scoped>
.table-base ::v-deep .el-checkbox {
  width: 150px;
  height: 34px;
  line-height: 30px;
  display: flex;
  align-items: center;
}
.table-base ::v-deep .el-checkbox .el-checkbox__label {
  font-size: 14px;
  white-space: normal;
}
</style>
