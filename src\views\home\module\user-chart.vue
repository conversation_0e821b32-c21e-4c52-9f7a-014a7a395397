<template>
  <div>
    <div class="item-title-wrap">
      <h1 class="yc-home-sub-title">新老用户行为趋势</h1>
      <div style="display: inline-flex;align-items: center;">
        <p style="margin-right: 40px;">
          <el-button
            type="text"
            :class="{'day-btn-true':dayTime === 7}"
            class="day-btn"
            @click="dayChange(7)"
          >7天</el-button>
          <el-button
            type="text"
            :class="{'day-btn-true':dayTime === 30}"
            class="day-btn"
            @click="dayChange(30)"
          >30天</el-button>
          <el-button
            type="text"
            :class="{'day-btn-true':dayTime === 90}"
            class="day-btn"
            @click="dayChange(90)"
          >90天</el-button>
        </p>
        <el-date-picker
          v-model="timeArr"
          :picker-options="basics.pickerOptions()"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          style="width: 290px;"
          @change="timeChange()"
        />
      </div>
    </div>
    <div class="yc-home-content">
      <el-row type="flex" align="center" justify="space-between">
        <el-col>
          <ul class="tagWrap">
            <li class="tag" :class="{tagTrue:tagName === 'login'}" @click="tagCik('login')">登录用户</li>
            <li class="tag" :class="{tagTrue:tagName === 'certification'}" @click="tagCik('certification')">认证用户</li>
            <li class="tag" :class="{tagTrue:tagName === 'order'}" @click="tagCik('order')">抢单用户</li>
            <li class="tag" :class="{tagTrue:tagName === 'recharge'}" @click="tagCik('recharge')">充值用户</li>
          </ul>
        </el-col>
        <el-col style="text-align: right">
          <ul class="colorWrap">
            <li class="clr">
              <span class="color" style="background: #34a0ff;" />
              <label class="name">新用户</label>
            </li>
            <li class="clr">
              <span class="color" style="background: #36cbcb;" />
              <label class="name">老用户</label>
            </li>
          </ul>
        </el-col>
      </el-row>
      <div ref="lineChart" class="chart-content" />
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import moment from 'moment'
import { overview_chat_user } from '@/api/dataStatistics'

export default {
  name: 'user-chart',
  data() {
    return {
      option: '',
      dayTime: 7,
      timeArr: [],
      tagName: 'login'
    }
  },
  mounted() {
    // let colors = ['#d48265', '#61a0a8'];
    const colors = ['#34a0ff', '#36cbcb']
    this.option = {
      color: colors,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      dataZoom: [
        {
          type: 'inside'
          // start: 0,
          // end: 10
        },
        {
          // start: 0,
          // end: 10,
          handleIcon:
            'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        }
      ],
      grid: {
        left: '6%',
        right: '8%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '新用户',
          type: 'bar',
          stack: '新用户',
          smooth: true,
          barMaxWidth: 20,
          data: []
        },
        {
          name: '老用户',
          type: 'bar',
          stack: '新用户',
          barMaxWidth: 20,
          smooth: true,
          data: []
        }
      ]
    }
    this.getDay()
    this.getData()
  },
  methods: {
    tagCik(name) {
      this.tagName = name
      this.getData()
    },
    getDay() {
      const day = moment().subtract(this.dayTime - 1, 'days')
      const arr = []
      arr[0] = moment(day).format('YYYY-MM-DD')
      arr[1] = moment().format('YYYY-MM-DD')
      this.timeArr = arr
    },
    dayChange(day) {
      this.dayTime = day
      this.getDay()
      this.getData()
    },
    timeChange(day) {
      this.dayTime = 0
      this.getData()
    },
    getData() {
      const thar = this
      overview_chat_user({
        begin_at: this.timeArr ? this.$utils.getTimer(this.timeArr[0]) : '',
        end_at: this.timeArr ? this.$utils.getTimer(this.timeArr[1]) : '',
        type: this.tagName
      }).then(res => {
        if (res.code === 200) {
          const new_list = []
          const old_list = []
          const day_list = []
          res.data.forEach(function(item) {
            day_list.push(item.day)
            new_list.push(item.new)
            old_list.push(item.old)
          })
          this.option.xAxis.data = day_list
          this.option.series[0].data = new_list
          this.option.series[1].data = old_list
          const _lineChart = echarts.init(thar.$refs.lineChart)
          _lineChart.setOption(thar.option, true)
          window.addEventListener('resize', function() {
            _lineChart.resize()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-content {
  width: 100%;
  height: 400px;
}
.yc-home-content {
  border-bottom: 1px solid #dfdfdf;
  padding: 20px;
}
.yc-home-sub-title {
  font-size: 18px;
  text-align: center;
}
.item-title-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: auto;
  border-radius: 4px 4px 0 0;
  padding: 20px;
}
.day-btn {
  color: #666;
}
.day-btn-true {
  color: #409eff;
}
.tagWrap {
  display: flex;
  align-items: center;
  .tag {
    background: #fff;
    border: 1px solid #dcdfe6;
    border-right: none;
    font-weight: 500;
    color: #606266;
    text-align: center;
    height: 35px;
    line-height: 35px;
    font-size: 13px;
    width: 97px;
    cursor: pointer;
  }
  .tag:first-child {
    border-radius: 4px 0 0 4px;
  }
  .tag:last-child {
    border-radius: 0 4px 4px 0;
    border-right: 1px solid #dcdfe6;
  }
  .tag:hover {
    color: #409eff;
  }
  .tagTrue {
    color: #fff !important;
    border: solid 1px #409eff;
    background-color: #409eff;
    border-right: none;
  }
}
.colorWrap {
  display: inline-flex;
  .clr {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
  }
  .clr:last-child {
    margin-right: 0;
  }
  .color {
    width: 40px;
    height: 20px;
    display: inline-block;
    margin-right: 10px;
    border-radius: 2px;
  }
  .name {
    font-size: 14px;
    color: #666;
  }
}
</style>
