<template>
  <div v-loading="!Object.keys(mechanismInfo).length">
    <Descriptions title="企业信息">
      <DescriptionsItem label="统一社会信用代码">{{ mechanismInfo.companyCreditCode || '--' }}</DescriptionsItem>
      <DescriptionsItem label="机构名称">{{ mechanismInfo.agencyName || '--' }}</DescriptionsItem>
      <DescriptionsItem label="机构类型">{{ MechanismTypeMap[mechanismInfo.agencyType] || '--' }}</DescriptionsItem>
      <DescriptionsItem label="企业地址">{{ `${mechanismInfo.companyAddressName}${mechanismInfo.companyAddressDetail}` || '--' }}</DescriptionsItem>
    </Descriptions>
    <Descriptions title="法人信息" style="margin-top: 20px">
      <DescriptionsItem label="法人姓名">{{ mechanismInfo.legalName || '--' }}</DescriptionsItem>
      <DescriptionsItem label="法人身份证号">{{ mechanismInfo.legalCardNumber || '--' }}</DescriptionsItem>
      <DescriptionsItem label="法人手机号">{{ mechanismInfo.legalMobile || '--' }}</DescriptionsItem>
    </Descriptions>
    <Descriptions title="联系人信息" style="margin-top: 20px">
      <DescriptionsItem label="法人是否联系人">{{ mechanismInfo.legalIsContact === 1 ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="联系人姓名">{{ mechanismInfo.contactName || '--' }}</DescriptionsItem>
      <DescriptionsItem label="联系人手机号">{{ mechanismInfo.contactMobile || '--' }}</DescriptionsItem>
      <DescriptionsItem label="联系人邮箱">{{ mechanismInfo.contactEmail || '--' }}</DescriptionsItem>
    </Descriptions>
    <Descriptions title="合作信息" style="margin-top: 20px">
      <DescriptionsItem label="有效期">{{ mechanismInfo.cooperateStartDate }} 至 {{ mechanismInfo.cooperateEndDate }}</DescriptionsItem>
      <DescriptionsItem label="是否API机构">{{ mechanismInfo.cooperateIsApi === 1 ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="是否撞库">{{ mechanismInfo.cooperateIsMatch === 1 ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="撞库地址" :span="3">{{ mechanismInfo.matchUrl || '--' }}</DescriptionsItem>
      <DescriptionsItem label="通知地址" :span="3">{{ mechanismInfo.notifyUrl || '--' }}</DescriptionsItem>
    </Descriptions>
  </div>
</template>

<script>
import Descriptions from '@/components/Descriptions'
import DescriptionsItem from '@/components/DescriptionsItem'
import { MechanismTypeMap } from '../../enum'

export default {
  name: 'BaseInfo',
  components: {
    Descriptions,
    DescriptionsItem
  },
  props: {
    mechanismInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    MechanismTypeMap() {
      return MechanismTypeMap
    }
  }
}
</script>
