<template>
  <el-upload
    class="upload-demo"
    :action="url"
    :on-remove="handleRemove"
    :on-success="handleSuccess"
    :before-upload="beforeUpload"
    multiple
    :accept="accept"
    :name="name"
    :data="data"
    :limit="limit"
    :on-exceed="handleExceed"
    :file-list="fileList"
    @on-error="handleError"
  >
    <el-button size="small" type="primary">点击上传</el-button>
    <slot name="tip">
      <div v-if="accept==='.csv, application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'" slot="tip" class="el-upload__tip">只能上传excel文件，且不超过{{ maxSize }}M</div>
    </slot>
  </el-upload>
</template>
<script>
import GLOBAL from '@/config/constant.conf'

export default {
  props: {
    limit: {
      type: Number,
      default: 5
    },
    accept: {
      type: String,
      default: '.csv, application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    },
    url: {
      type: String,
      default: `${GLOBAL.api}/excel/upload`
    },
    data: {
      type: Object,
      default: () => {
        return {
          excelType: 'excel'
        }
      }
    },
    maxSize: {
      type: Number,
      default: 50
    },
    name: {
      type: String,
      default: 'excel'
    }
  },
  data() {
    return {
      fileList: [],
      successData: {}
    }
  },
  methods: {
    beforeUpload(file) {
      if (this.basics.isNull(this.maxSize) || this.maxSize === 0) {
        return true
      } else {
        const size = this.maxSize * 1024 * 1024
        if (file.size > size) {
          this.confirm(`上传文件不能大于${this.maxSize}M`, '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
          return false
        }
      }
    },
    handleRemove(file, fileList) {
      const { uid = 0 } = file
      delete this.successData[uid]
      this.$emit('file-change', this.successData)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleError(errmsg, file, fileList) {
      this.$message.error(`${file.name}上传失败`)
    },
    handleSuccess(response, file, fileList) {
      const { uid = 0 } = file
      this.successData[uid] = response.data
      this.$emit('file-change', this.successData)
    }
  }
}
</script>
