import basics from '../libs/basics.js'
import { adminConfig } from './sysConfig'
import { Message } from 'element-ui'

// 图路径替换
export const picturePath = (picturePath) => {
  return adminConfig.otherConfiguration.picturesLinking + picturePath
}
export const unifiedFormList = (list, options) => { // 统一处理formlist
  list.forEach(item => {
    Object.keys(options).forEach(optionsItem => {
      if (basics.isNull(item[optionsItem])) {
        item[optionsItem] = options[optionsItem]
      }
    })
  })
}
// 格式化时间字符串 yyyy-mm-dd
export const changeDateFormatStr = (cellval) => {
  if (cellval != null) {
    var year = (cellval + '').substring(0, 4)
    var month = (cellval + '').substring(4, 6)
    var currentDate = (cellval + '').substring(6, 8)
    var time = (cellval + '').substring(8, cellval.length)
    if (!time === '') {
      time = Math.floor(time / 1000)
      var second = time % 100
      time = Math.floor(time / 100)
      var minitue = time % 100
      var hour = Math.floor(time / 100)
      return year + '-' + month + '-' + currentDate + ' ' + hour + ':' + minitue + ':' + second
    } else {
      return year + '-' + month + '-' + currentDate
    }
  }
}
// 下拉条位置
export const getScrollTop = () => {
  var scrollTop = 0
  if (document.documentElement && document.documentElement.scrollTop) {
    scrollTop = document.documentElement.scrollTop
  } else if (document.body) {
    scrollTop = document.body.scrollTop
  }
  return scrollTop
}
// 图片在线预览
export const pictureOnlineLook = (event) => {
  if (!basics.isNull(event.target.files[0])) {
    var size = (event.target.files[0].size / 1024).toFixed(2)
    if (adminConfig.otherConfiguration.uploadImgSize < size) {
      Message.error(`图片过大，请上传图片小于${adminConfig.otherConfiguration.uploadImgSize}kb`)
      return false
    } else {
      const file = event.target.files[0]
      window.URL = window.URL || window.webkitURL
      const imgSrc = window.URL.createObjectURL(file)
      return [imgSrc, file]
    }
  }
}
/* 驼峰*/
export const caseAndCase = (field) => {
  return field.replace(/\_\w/g, function(letter, position) {
    letter = letter.replace(/\_/g, '')
    return letter.toUpperCase()
  })
}
export const genBase64 = (file) => {
  var r = new FileReader() // 本地预览
  r.onload = function() {
  }
  r.readAsDataURL(file) // Base64
}
export const copy = (obj) => { // 深拷贝
  const objClone = Array.isArray(obj) ? [] : {}
  if (obj && typeof obj === 'object') {
    for (var key in obj) {
      if (Object.hasOwnProperty.call(obj, key)) {
        // 判断ojb子元素是否为对象，如果是，递归复制
        if (obj[key] && typeof obj[key] === 'object') {
          objClone[key] = copy(obj[key])
        } else {
          // 如果不是，简单复制
          objClone[key] = obj[key]
        }
      }
    }
  }
  return objClone
}
/**
 * @desc:设置页面meta文字
 * @author:djsong
 * @param:text 需要设置的文字
 * @return:void
 */
export const setPageMetaTitle = (text) => {
  document.title = text
}

export const getBasParamsString = (params) => {
  const data = getBasParams(params)
  let str = ''
  for (const i in data) {
    str += `&${i}=${data[i]}`
  }
  return str.substr(1)
}

export const getBasParams = (data = {}) => ({
  ...data
})

/**
 * @description:循环对象
 * @author:djsong
 * @date:2019/7/26
 * @param:data 对象
 * @param:key key(可以key.key的形式)
 * @param:stationSymbol 没有数据的站位符
 * @return: value
 */
export const mapRecursion = (data, key, stationSymbol) => {
  const keys = key.split('.')
  let value = data
  if (keys.length > 0) {
    const recursionKey = (recursionData, recursionKey) => {
      return recursionData[recursionKey]
    }
    for (const i of keys) {
      value = recursionKey(value, i)
      if (basics.isNull(value)) {
        if (!basics.isNull(stationSymbol)) {
          return stationSymbol
        } else {
          return '-'
        }
      }
    }
    return value
  } else {
    return '-'
  }
}

/**
 * @description: 百分比计算
 * @author:djsong
 * @date:2019/8/1
 * @param:a 被除数
 * @param:b 除数
 * @return:string
 */
export const percentage = (a, b) => {
  const value = a / b
  if (value === Infinity) return '0%'
  return isNaN(value) ? '0%' : (value * 100).toFixed(2) + '%'
}
