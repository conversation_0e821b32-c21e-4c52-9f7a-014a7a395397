<template>
  <div class="form_title_container">
    <div class="form_view_title">
      <div class="title_line"></div>
      <span>{{ title }}</span>
    </div>
    <div class="form_content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormSection',
  props: {
    title: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.form_title_container {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;

  .form_view_title {
    margin-bottom: 10px;

    .title_line {
      width: 4px;
      height: 18px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }

  .form_content {
    padding-left: 10px;
  }
}
</style> 