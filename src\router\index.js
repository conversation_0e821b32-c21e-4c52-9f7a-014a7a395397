/*
 * @Author: 陈小豆
 * @Date: 2023-07-21 14:56:17
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-07-09 14:56:37
 */
import Layout from '@/layout'
import Vue from 'vue'
import Router from 'vue-router'
import home from './children/home'
import system from './children/system'
import financial from './children/financial'
import mechanism from './children/mechanism'
import workbench from './children/workbench'
import clue from './children/clue'
import product from './children/product'
import distribution from './children/distribution'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/',
    redirect: '/home',
    meta: {
      title: '首页'
    },
    component: Layout,
    children: home
  },
  {
    path: '/404',
    name: 'error_404',
    meta: {
      title: '404',
      noVerify: true
    },
    component: () => import('@/views/error-page/404.vue')
  },

  {
    path: '/edit_pass',
    name: 'edit_pass',
    meta: {
      title: '修改密码'
    },
    component: () => import('@/views/home/<USER>/edit-pass')
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      noVerify: true
    },
    component: () => import('@/views/login/page/login-in')
  },
  {
    path: '/system',
    meta: {
      title: '系统管理'
    },
    component: Layout,
    children: system
  },
  {
    path: '/financial',
    meta: {
      title: '财务管理'
    },
    component: Layout,
    children: financial
  },
  {
    path: '/mechanism',
    meta: {
      title: '机构管理'
    },
    component: Layout,
    children: mechanism
  },
  {
    path: '/workbench',
    meta: {
      title: '工作台'
    },
    component: Layout,
    children: workbench
  },
  {
    path: '/clue',
    meta: {
      title: '分发管理'
    },
    component: Layout,
    children: clue
  },
  {
    path: '/product',
    meta: {
      title: '产品管理'
    },
    component: Layout,
    children: product
  },
  {
    path: '/distribution',
    meta: {
      title: '分发策略'
    },
    component: Layout,
    children: distribution
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

const createRouter = () =>
  new Router({
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
