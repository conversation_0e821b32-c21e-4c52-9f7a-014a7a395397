<template>
  <div v-if="showPage">
    <Page :request="request" :list="list">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" @click="handExport">导出</el-button>
      </div>
    </Page>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import { get_financial_detail_list, EXPORT_OFFLINE_EXPORT } from '@/api/financial'
const tradeTypes = [
  {
    key: 'recharge',
    name: '充值',
    value: 1
  },
  {
    key: 'deduct',
    name: '扣除',
    value: 2
  },
  {
    key: 'bail_recharge',
    name: '保证金充值',
    value: 3
  }
]
export default {
  components: {
    Page
  },
  props: {
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      listQuery: {
        startDate: '',
        endDate: '',
        type: ''
      },
      tradeList: [{
        label: '充值',
        value: 1
      },
      {
        label: '扣款',
        value: 2
      },
      {
        label: '赠送',
        value: 4
      },
      {
        label: '保证金充值',
        value: 3
      }
      ],
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data, accountId: this.id }
          const res = await get_financial_detail_list(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      },
      showPage: false,
      tradeTypes
    }
  },
  computed: {
    list() {
      return [
        {
          title: '交易订单号',
          key: 'transactionId'
        },
        {
          title: '交易日期',
          key: 'datePicker',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          search: true,
          val: [this.listQuery.startDate, this.listQuery.endDate],
          tableHidden: true,
          pickerDay: 365 * 50
        },
        {
          title: '交易账户',
          key: 'agenciesAccount',
          render: (_h, params) => {
            const data = params?.data?.row?.agenciesAccount
            const accountNo = data?.accountNo ?? '-'
            const accountTypeStr = data?.accountType === 1 ? '公司账户' : '个人账户'
            return <div>{accountTypeStr}<br/>{accountNo}</div>
          }
        },
        {
          title: '交易备注',
          key: 'remark'
        },
        {
          title: '交易金额(元)',
          key: 'amount'
        },
        {
          title: '交易类型',
          key: 'type',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.tradeList,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '交易类型',
          key: 'businessTypeStr'
          // type: formItemType.select,
          // tableView: tableItemType.tableView.text,
          // list: this.tradeTypes,
          // listFormat: {
          //   label: 'name',
          //   value: 'value'
          // }
        },
        {
          title: '交易时间',
          key: 'createTime'
        }
      ]
    }

  },
  created() {
    setTimeout(() => {
      this.showPage = true
    })
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_OFFLINE_EXPORT({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
      .title-container-box{
        justify-content: flex-start;
        ::v-deep .el-tabs__nav-wrap::after{
            background-color: transparent;
        }
      }
      .table-footer{
        margin-top: 15px;
      }
  </style>
