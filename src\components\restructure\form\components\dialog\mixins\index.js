import { clickType, dialogFooterState } from '@/config/sysConfig'
export default {
  props: {
    formItemList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    urls: {
      type: Object,
      default: () => {
        return {
          updateHttp: () => Promise.resolve(1),
          insertHttp: () => Promise.resolve(2),
          getByIdHttp: {}
        }
      }
    },
    data: {
      type: Object,
      default: () => {}
    },
    beforeUpdate: {
      type: Function,
      default: () => {
        return {}
      }
    },
    clickType: {
      type: String,
      default: clickType['default']
    },
    labelWidth: {
      type: String,
      default: '80px'
    },
    dialogFooterState: {
      type: String,
      default: dialogFooterState.common
    },
    displayState: {
      type: Boolean,
      default: false
    }
  }
}
