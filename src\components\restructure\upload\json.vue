<template>
  <div>
    <div v-show="!defaultPath">
      <el-upload
        :ref="refName"
        style="width: 300px"
        :accept="accept"
        :headers="headers"
        :action="url"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :multiple="false"
        :limit="1"
        list-type="picture"
      >
        <el-button
          ref="uploadBtn"
          icon="el-icon-upload"
          size="small"
          plain
          type="warning"
          :disabled="addDisable"
        >点击上传
        </el-button>
        <div slot="tip" class="el-upload__tip">
          只能上传json格式文件，文件不能超过1M
        </div>
      </el-upload>
    </div>
    <div v-if="defaultPath">
      <viewer style="display: inline-flex">
        <div class="item-img">
          {{ defaultPath }}
        </div>
      </viewer>
      <div slot="tip" class="el-upload__tip">
        只能上传json格式文件，文件不能超过1M
      </div>
      <p>
        <el-button
          type="warning"
          plain
          size="small"
          :disabled="editDisable"
          @click="copyUpload()"
        >重新选取</el-button>
      </p>
    </div>
  </div>
</template>
<script>
import GLOBAL from '@/config/constant.conf'

export default {
  props: {
    refName: {
      type: String,
      default: `upload`
    },
    url: {
      type: String,
      default: `${GLOBAL.publicPath}/upload/json`
    },
    accept: {
      type: String,
      default: 'application/json'
    },
    defaultPath: {
      type: String,
      default: ''
    },
    beforeUpload: {
      type: Function,
      default: function(file) {
        if (file.size > 1 * 1024 * 1024) {
          this.$message.error('上传图片大小不能超过1M!')
          return false
        }
        return file.size
      }
    },
    editDisable: {
      type: Boolean,
      default: false
    },
    addDisable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      headers: { Authorization: `${this.$store.getters.authorization}` }
    }
  },
  methods: {
    uploadSuccess: function(res, file) {
      // 图片上传成功
      if (res.code === 0) {
        this.$emit('onSuccess', res)
      }
    },
    uploadError: function(res, file) {
      // 图片上传失败
      this.$message.error('上传失败')
    },
    copyUpload() {
      this.$refs.uploadBtn.$el.click()
      this.$refs.upload.clearFiles()
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.item-img {
  width: auto;
  height: auto;
  border: 1px dashed #dfdfdf;
  padding: 10px;
  text-align: center;
  margin-right: 15px;
  border-radius: 4px;

  img {
    width: auto;
    height: auto;
    max-width: 120px;
    max-height: 70px;
  }
}
</style>
