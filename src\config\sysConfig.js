import GLOBAL from './constant.conf'

export const adminConfig = {
  otherConfiguration: {
    // 其他参数 本地 http://*************:8000/  http://*************:8000/static/  线上https://www.csdashi.cn/static/
    apiPrefixPath: '' /* 默认请求地址*/,
    pictureUploading: '', // 图片上传地址
    picturesLinking: '', // 图片路径
    uploadImgSize: 10000000000, // 图片大小
    defaultPicture: '' // 默认图片
  },
  requestParameters: {
    // 请求参数
    requestCode: 0, // 请求成功状态
    requestFail: 1, // 请求失败状态
    requestOverdue: 7, // 请求验证过期
    state: 'status', // 请求状态参数
    pageSize: 10, // 请求页数
    toast: 'msg', // 提示
    result: 'results' // 返回结果
  },
  store: {
    authorCacheName: 'masterCarDealerAdmin' /* 登录缓存*/,
    noCachingRouter: '/login'
  },
  templateList: [
    {
      value: '0',
      text: '是'
    },
    {
      value: '1',
      text: '否'
    }
  ],
  list: [
    {
      value: 0,
      text: '待签署'
    },
    {
      value: 100,
      text: '待放款'
    },
    {
      value: 200,
      text: '待还款'
    },
    {
      value: 300,
      text: '已逾期'
    },
    {
      value: 400,
      text: '已展期'
    },
    {
      value: 500,
      text: '已核销'
    }
  ],
  buttonObj: {
    0: {
      buttonList(vm, params) {
        return [
          {
            buttonText: '查看二维码',
            on(params) {
              return {
                click() {
                  vm.viewCodePop = !vm.viewCodePop
                  vm.id = params.row.id
                }
              }
            }
          }
        ]
      }
    },
    100: {
      buttonList(vm, params) {
        return [
          {
            buttonText: '放款',
            on(params) {
              return {
                click() {
                  vm.loanPop = !vm.loanPop
                  vm.id = params.row.id
                }
              }
            }
          },
          {
            buttonText: '详情',
            on(params) {
              return {
                click() {
                  vm.$router.push({
                    path: '/iouDetails/' + params.row.id
                  })
                }
              }
            }
          }
        ]
      }
    },
    200: {
      buttonList(vm, params) {
        return vm.basicsBtn
      }
    },
    300: {
      buttonList(vm, params) {
        return vm.basicsBtn
      }
    },
    400: {
      buttonList(vm, params) {
        return vm.basicsBtn
      }
    },
    500: {
      buttonList(vm, params) {
        return [
          // {
          //   buttonText: '核销',
          //   on (params) {
          //     return {
          //       click () {
          //         vm.writeOff = !vm.writeOff;
          //         vm.id = params.row.id;
          //       }
          //     };
          //   }
          // },
          {
            buttonText: '详情',
            on(params) {
              return {
                click() {
                  vm.$router.push({
                    path: '/iouDetails/' + params.row.id
                  })
                }
              }
            }
          }
        ]
      }
    }
  }
}

export const keyWord = {
  multiple: 'Multiple',
  picture: 'PICTURE',
  relatedWords: ','
}

export const formItemType = {
  text: 'text',
  input: 'input',
  select: 'select',
  switch: 'switch',
  selectMultiple: 'select' + keyWord.multiple,
  radio: 'radio',
  radioButton: 'radioButton',
  checkbox: 'checkbox' + keyWord.multiple,
  inputNumber: 'inputNumber',
  timePicker: 'timePicker',
  timePickerRange: 'timePickerRange',
  datePicker: 'datePicker',
  dateTimePicker: 'dateTimePicker',
  rangeDatePicker: 'rangeDatePicker' + keyWord.multiple,
  upload: 'upload',
  uploadJson: 'uploadJson',
  colorPicker: 'colorPicker',
  excelUpload: 'excelUpload',
  textDate: 'textDate',
  datePickerDaterangeGai: 'datePickerDaterangeGai' + keyWord.multiple,
  inputGai: 'SInputGai' + keyWord.multiple,
  inputNext: 'SInputNext',
  selectSearchMultiple: 'selectSearch' + keyWord.multiple,
  cascade: 'cascade',
  timeSelectRangeMultiple: 'timeSelectRange' + keyWord.multiple
}

export const clickType = {
  default: 'default',
  close: 'close'
}

export const updateIgnoreKey = ['createTime', 'updateTime']

export const dialogFooterState = {
  common: 'common',
  close: 'close'
}

export const tableItemType = {
  tabType: {
    selection: 'selection',
    radio: 'radio',
    index: 'index'
  },
  active: 'active',
  activeType: {
    delete: 'delete',
    detailsDialog: 'detailsDialog',
    dialog: 'Dialog',
    newWin: 'newWin',
    event: 'Event'
  },
  tableView: {
    text: 'text',
    tagState: 'tagState',
    picture: 'picture',
    requestText: 'requestText',
    date: 'date',
    prefixTitle: 'prefixTitle',
    subTitle: 'subTitle',
    colorView: 'colorView',
    pointerColorView: 'pointerColorView',
    jump: 'jump',
    clipboard: 'clipboard'
  },
  expand: 'expand'
}

export const tabType = {
  common: 'COMMON',
  own: 'OWN'
}

export const cascaderConfig = {
  filterable: true
}

export const selectConfig = {
  clearable: true,
  filterable: true
}

export const submission = {
  insert: 'insert',
  update: 'update'
}
export const exhibitionMode = {}

export const error = msg => {
  throw new Error(msg)
}

export const picturePath = picturePath => {
  return GLOBAL.publicPath + picturePath
}

export const deleteConfirm = vm => {
  return new Promise((resolve, reject) => {
    vm.$confirm('此操作将永久删除, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      closeOnClickModal: false
    })
      .then(() => {
        resolve()
      })
      .catch(() => {
        reject()
        vm.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
  })
}
export const mode = 'local'
