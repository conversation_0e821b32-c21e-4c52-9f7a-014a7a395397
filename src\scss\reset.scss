/*
* author: lints
* date: 2018-06-28
* description: 初始化css样式表
* */

/**
* <PERSON>"s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
* http://cssreset.com
*/

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  font-family: PingFangTC-Semibold, Helvetica, Arial, sans-serif;
}

[v-cloak] {
  display: none;
}

input {
  outline: none;
  background: none;
  border: none;
}

button {
  border: none;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

html,
body {
  width: 100%;
  height: 100%;
  font-size: 100px;
}

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

input {
  -webkit-tap-highlight-color: transparent;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

a {
  color: #7e8c8d;
  text-decoration: none;
  backface-visibility: hidden;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 8px;
  background-color: rgba(125, 125, 125, 0.7);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 8px;
  background-color: rgba(125, 125, 125, 0.7);
  border-radius: 6px;
}

a,
button {
  border: none;
  outline: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
}

.clear:after {
  display: block;
  clear: both;
  content: "";
  visibility: hidden;
  height: 0;
}
