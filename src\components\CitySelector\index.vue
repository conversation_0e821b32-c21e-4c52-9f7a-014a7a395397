<template>
  <el-cascader
    ref="cascaderRef"
    v-model="selectedCities"
    :options="options"
    :props="props"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    :collapse-tags="collapseTags"
    @change="handleChange"
  />
</template>

<script>
export default {
  name: 'CitySelector',
  props: {
    // 当前选中的值，可以是单个值或数组，取决于是否多选
    value: {
      type: [String, Array],
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 占位文本
    placeholder: {
      type: String,
      default: '请选择城市'
    },
    // 是否可清空选项
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可以搜索
    filterable: {
      type: Boolean,
      default: false
    },
    // 多选时是否折叠显示
    collapseTags: {
      type: Boolean,
      default: true
    },
    // 是否可过滤
    filterable: {
      type: Boolean,
      default: true
    },
    // 自定义API路径
    apiUrl: {
      type: String,
      default: 'https://capi.haoxincd.cn/cms/city/allCity'
    },
    // 显示全国
    showAll: {
      type: Boolean,
      default: false
    },
    // 选择任意一级选项
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 是否显示区级
    showArea: {
      type: Boolean,
      default: true
    },
    // 过滤直辖市下一级，只保留省份级
    filterMunicipality: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedCities: this.value,
      options: [],
      props: {
        multiple: this.multiple,
        value: 'id',
        label: 'name',
        children: 'cityDtoList',
        emitPath: false,
        checkStrictly: this.checkStrictly
      },
      // 最后选中的值
      _lastScrollTopList: []
    }
  },
  watch: {
    value(val) {
      this.selectedCities = val
    },
    multiple: {
      handler(val) {
        this.props.multiple = val
      },
      immediate: true
    }
  },
  created() {
    this.fetchCityData()
  },
  methods: {
    /**
     * 获取城市数据
     */
    async fetchCityData() {
      try {
        const res = await fetch(this.apiUrl)
        const data = await res.json()
        if (data && Array.isArray(data.data)) {
          let options = []
          // 所有级别展示
          if (this.showArea) {
            // eslint-disable-next-line no-inner-declarations
            function transformCities(cityList) {
              if (!cityList || !Array.isArray(cityList)) return []

              return cityList.map(city => {
                const transformed = {
                  id: String(city.id),
                  name: city.fullName
                }
                const children = transformCities(city.cityDtoList)
                if (children.length > 0) {
                  transformed.cityDtoList = children
                }
                return transformed
              })
            }

            options = data.data.map(province => ({
              id: String(province.provinceCode),
              name: province.provinceName,
              cityDtoList: transformCities(province.cityDtoList)
            }))
          } else {
            // 只展示到市级
            options = data.data.map(province => ({
              id: String(province.provinceCode),
              name: province.provinceName,
              cityDtoList: province.cityDtoList?.map(city => ({
                id: String(city.id),
                name: city.fullName
              }))
            }))

            if (this.filterMunicipality) {
              // 直辖市过滤掉市级，只保留省级
              const filterList = ['北京市', '天津市', '重庆市', '上海市', '香港特别行政区', '澳门特别行政区']
              options = options.map(province => {
                if (filterList.includes(province.name)) {
                  return {
                    ...province,
                    cityDtoList: null
                  }
                } else {
                  return province
                }
              })
            }
          }

          this.options = this.showAll ? [{ id: '-1', name: '全国', cityDtoList: options }] : options
          this.$emit('options', this.options)
        }
      } catch (error) {
        console.error('获取城市数据失败:', error)
      }
    },

    /**
     * 处理级联选择器选中值变化
     */
    handleChange(value) {
      const checkedNodes = this.$refs.cascaderRef.getCheckedNodes()

      if (!checkedNodes.length) {
        this.$emit('change', value)
      } else {
        // 完整name
        const fullName = checkedNodes[0].pathLabels.join(',')
        // 完整code
        const code = checkedNodes[0].path.join(',')

        this.$emit('change', value, {
          label: fullName,
          code
        })
      }
      this.$emit('input', value)
    },
    // 设置禁用
    setDisabled(val) {
      this.recordCascaderScroll()
      this.disableRelatives(this.options, val)
      // 修复滚动条错位
      this.$nextTick(() => {
        this.restoreCascaderScroll()
      })
    },
    // 记录当前弹出层滚动位置
    recordCascaderScroll() {
      const scrollWraps = document.querySelectorAll('.el-cascader__dropdown .el-scrollbar__wrap')
      this._lastScrollTopList = Array.from(scrollWraps).map(el => el.scrollTop)
    },

    // 恢复弹出层滚动位置
    restoreCascaderScroll() {
      this.$nextTick(() => {
        const scrollWraps = document.querySelectorAll('.el-cascader__dropdown .el-scrollbar__wrap')
        if (!this._lastScrollTopList || this._lastScrollTopList.length !== scrollWraps.length) return

        scrollWraps.forEach((el, index) => {
          el.scrollTop = this._lastScrollTopList[index]
        })

        this.$refs.cascaderRef?.updatePopper?.()
      })
    },
    disableRelatives(data, selectedValues) {
      const resetDisabled = (nodes) => {
        nodes.forEach(node => {
          this.$set(node, 'disabled', false) // ✔ 保证响应式
          if (node.cityDtoList) resetDisabled(node.cityDtoList)
        })
      }

      const markChildrenDisabled = (nodes) => {
        nodes.forEach(node => {
          this.$set(node, 'disabled', true) // ✔ 保证响应式
          if (node.cityDtoList) markChildrenDisabled(node.cityDtoList)
        })
      }

      const traverse = (nodes, currentPath = []) => {
        nodes.forEach(node => {
          const path = [...currentPath, node]

          if (selectedValues.includes(node.id)) {
            path.slice(0, -1).forEach(parent => {
              this.$set(parent, 'disabled', true) // ✔ 父级禁用
            })

            if (node.cityDtoList) markChildrenDisabled(node.cityDtoList)
          }

          if (node.cityDtoList) traverse(node.cityDtoList, path)
        })
      }

      resetDisabled(data)
      traverse(data)
    }
  }
}
</script>

<style lang="scss" scoped>
/* 解决 添加 filterable placeholder 重影 */
::v-deep{
  .el-cascader__tags input::-webkit-input-placeholder {
    color: white; opacity: 0;
  }
}
</style>
