import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/*
 * 账户管理
 * */
export const get_financial_account = obj => {
  return get('/api/agencies-account/page', obj, false)
}

export const edit_financial_account = obj => {
  return post('/api/agencies-account/recharge', obj, false)
}
export const get_financial_sum = obj => {
  return get('/api/agencies-account/sumData', obj, false)
}
// 查询单个账户
export const get_financial_person = id => {
  return get(`/api/agencies-account/${id}`, null, false)
}
// 查询单个账户
export const get_financial_detail_list = obj => {
  return get(`/api/agencies-account-detail/page`, obj, false)
}
// 导出账户
export const EXPORT_OFFLINE_EXPORT = data =>
  CONSTANT.publicPath + '/api/agencies-account-detail/export?' + qs.stringify(data)
