import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/*
 * 系统管理
 * */
export const get_admin_list = obj => {
  return get('/admin/list', obj, false)
}

/*
 * 角色列表
 * */
export const get_role_list = () => {
  return get('admin/list', null, false)
}

export const get_admin_role_list = () => {
  return get('/admin/role/list', null, false)
}

export const get_permission_list = () => {
  return get('/admin/permission/list', null, false)
}

export const get_menus_permission = () => {
  return get('/admin/role/menus/permission', null, false)
}

export const get_role_by_id = id => {
  return get(`/admin/role/${id}`, null, false)
}

export const get_permission_by_id = id => {
  return get(`/admin/permission/${id}`, null, false)
}

/*
 * 添加管理员
 * */
export const add_admin = obj => {
  return post('/admin/add', obj, false)
}

export const add_role = obj => {
  return post('/admin/role/addOrUpDate', obj, false)
}

export const add_permission = obj => {
  return post('/admin/permission', obj, false)
}

export const deployRelationPermission = obj => {
  return post('/admin/role/relation', obj, false)
}

/*
 * 删除角色
 * */

export const del_role = id => {
  return del(`/admin/role/${id}`, null, false)
}

export const del_permission = id => {
  return del(`/admin/permission/${id}`, null, false)
}

/**
 *
 * 修改用户状态
 */
export const update_admin_locked = id => {
  return put('admin/locked/' + id, null, false)
}

export const update_role = obj => {
  return post(`/admin/role/addOrUpDate`, obj, false)
}

export const update_permission = obj => {
  return put(`/admin/permission/`, obj, false)
}

/**
 * 添加菜单
 */
export const add_menu = obj => {
  return post('/admin/menu', obj, false)
}

/**
 * 修改菜单
 */
export const put_menu = obj => {
  return put('/admin/menu', obj, false)
}

/**
 * 菜单列表
 */
export const get_menu_list = obj => {
  return get('/admin/menu/list', obj, false)
}

/**
 * 删除菜单
 */
export const del_menu = obj => {
  return del(`/admin/menu/${obj.id}`, obj, false)
}

/**
 * 菜单详情
 */
export const get_menu_edit = obj => {
  return get(`/admin/menu/${obj.id}`, obj, false)
}

/**
 * 菜单详情
 */
export const admin_relationMenu = obj => {
  return post(`/admin/role/relationMenu`, obj, false)
}

/**
 * 修改管理员
 */
export const put_admin = obj => {
  return put(`/admin/update`, obj, false)
}

/**
 * 删除管理员
 */
export const del_admin = id => {
  return del(`/admin/delete/${id}`, null, false)
}
/**
 * 获取所有角色列表
 */
export const adminRolelist = obj => {
  return get(`/admin/role/list`, obj)
}

export const adminBranchList = obj => {
  return get(`/admin/branch/tree`, obj)
}
export const adminBranchDetail = obj => {
  return get(`/admin/branch/detail`, obj)
}
export const editBranch = obj => {
  return post(`/admin/branch/addOrUpdate`, obj, false)
}
export const deleteBranch = branchId => {
  return put(`/admin/branch/delete/${branchId}`, null, false)
}
// 角色下拉选
export const getSelectorRole = obj => {
  return get(`/admin/role/selector`, obj, false)
}

// 更新用户状态
export const updateUserStatus = id => {
  return put(`/admin/enable/${id}`, null, false)
}
// 查询可配置的部门主管
export const get_branchManger = obj => {
  return get(`/admin/list/branchManger`, obj, false)
}
// 新增用户时 可以选择的上级用户
export const get_branch_directSuperior = obj => {
  return get(`/admin/list/branchId`, obj, false)
}
// 操作日志
export const get_log_page = obj => {
  return get(`/syslog/page`, obj, false)
}

// 导出操作日志
export const get_log_export = data =>
  CONSTANT.publicPath + `/syslog/export?` + qs.stringify(data)

// 角色禁用启用
export const updateRoleStatus = id => {
  return put(`/admin/role/${id}`)
}
// 部门详情数据
export const get_branch_detail = obj => {
  return get(`/admin/branch/detail`, obj, false)
}
/**
 * 获取IP白名单列表
 */
export const getIpWhiteList = obj => {
  return get('/sys/ipWhitelist/list', obj)
}

/**
 * 更新IP白名单状态
 */
export const updateIpWhiteList = obj => {
  return post('/sys/ipWhitelist/saveOrUpdate', obj)
}

/**
 * 删除白名单
 */
export const deleteIpWhiteList = id => {
  return del(`/sys/ipWhitelist/${id}`)
}

/**
 * 获取ip白名单开启关闭状态
 */
export const getIpWhiteStatusApi = () => {
  return get('/sys/ipWhitelist/getIpWhiteConfig')
}
// 更新ip白名单开启关闭状态
export const updateIpWhiteStatusApi = obj => {
  return post('/sys/ipWhitelist/updateIpWhiteConfig', obj)
}
/**
 * 参数设置 1.4.1
 */
export const getSystemList = obj => {
  return get(`/systemParam/page`, obj)
}
/**
 * 参数设置 编辑 1.4.1
 */
export const postSystemDetail = obj => {
  return post(`/systemParam/updateById`, obj)
}

/**
 * 参数设置 新增
 */
export const createSystemDetailApi = obj => {
  return post(`/systemParam/saveSystemParam`, obj)
}

// 获取参数详情
export const getSystemDetailApi = id => {
  return get(`/systemParam/byId?paramId=${id}`)
}
